name: Python Unit Tests

on:
  push:
  # pull_request:
  #   branches: [ dev ]

jobs:
  run-tests:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.12'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install uv
        uv venv --python 3.12
        source .venv/bin/activate
        uv pip install -r pyproject.toml

    - name: Run unit tests
      continue-on-error: true # remove later when # WOS-60 is merged
      run: |
        source .venv/bin/activate
        uv run pytest
