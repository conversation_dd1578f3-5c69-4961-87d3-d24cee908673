#!/bin/bash
# Script to deploy THECHATBOT to ECS

set -e

# Configuration
AWS_REGION="ap-southeast-1"
AWS_ACCOUNT_ID="************"
ECR_REPOSITORY="wos-dev-wos-dev-thechatbot"
IMAGE_TAG="latest"
CLUSTER_NAME="wos-dev-cluster"
SERVICE_NAME="wos-dev-thechatbot-service"
TASK_FAMILY="wos-dev-thechatbot"

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Starting deployment of THECHATBOT to ECS...${NC}"

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    echo -e "${RED}AWS CLI is not installed. Please install it first.${NC}"
    exit 1
fi

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo -e "${RED}Docker is not installed. Please install it first.${NC}"
    exit 1
fi

# Login to ECR
echo -e "${YELLOW}Logging in to Amazon ECR...${NC}"
aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com

# Build the Docker image
echo -e "${YELLOW}Building the Docker image...${NC}"
docker build -t $ECR_REPOSITORY:$IMAGE_TAG .

# Tag the image
echo -e "${YELLOW}Tagging the image...${NC}"
docker tag $ECR_REPOSITORY:$IMAGE_TAG $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$ECR_REPOSITORY:$IMAGE_TAG

# Push the image to ECR
echo -e "${YELLOW}Pushing the image to ECR...${NC}"
docker push $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$ECR_REPOSITORY:$IMAGE_TAG

# Register the task definition
echo -e "${YELLOW}Registering the task definition...${NC}"
TASK_DEFINITION=$(aws ecs register-task-definition --cli-input-json file://ecs/dev/task-definition.json --region $AWS_REGION)
TASK_REVISION=$(echo $TASK_DEFINITION | jq -r '.taskDefinition.revision')
echo -e "${GREEN}Task definition registered with revision: $TASK_REVISION${NC}"

# Verify the container name in the registered task definition
CONTAINER_NAME=$(echo $TASK_DEFINITION | jq -r '.taskDefinition.containerDefinitions[0].name')
echo -e "${YELLOW}Verifying container name: $CONTAINER_NAME${NC}"

if [ "$CONTAINER_NAME" != "thechatbot" ]; then
    echo -e "${RED}Error: Container name in the registered task definition is '$CONTAINER_NAME', but should be 'thechatbot'.${NC}"
    echo -e "${RED}Please check the task-definition.json file and ensure the container name is 'thechatbot'.${NC}"
    exit 1
fi

# Check if the service exists
SERVICE_EXISTS=$(aws ecs describe-services --cluster $CLUSTER_NAME --services $SERVICE_NAME --region $AWS_REGION | jq -r '.services | length')

if [ $SERVICE_EXISTS -eq 0 ]; then
    # Create the service if it doesn't exist
    echo -e "${YELLOW}Creating the ECS service...${NC}"

    # Create a temporary service definition file with the correct task definition revision
    TMP_SERVICE_DEF=$(mktemp)
    cat ecs/dev/service-definition.json | jq --arg revision "$TASK_REVISION" '.taskDefinition = "wos-dev-thechatbot:" + $revision' > $TMP_SERVICE_DEF

    # Create the service using the temporary file
    aws ecs create-service --cli-input-json file://$TMP_SERVICE_DEF --region $AWS_REGION

    # Clean up the temporary file
    rm $TMP_SERVICE_DEF

    echo -e "${GREEN}Service created successfully!${NC}"
else
    # Update the service if it exists
    echo -e "${YELLOW}Updating the ECS service...${NC}"
    aws ecs update-service --cluster $CLUSTER_NAME --service $SERVICE_NAME --task-definition $TASK_FAMILY:$TASK_REVISION --force-new-deployment --region $AWS_REGION
    echo -e "${GREEN}Service updated successfully!${NC}"
fi

# Wait for the service to stabilize
echo -e "${YELLOW}Waiting for the service to stabilize...${NC}"
aws ecs wait services-stable --cluster $CLUSTER_NAME --services $SERVICE_NAME --region $AWS_REGION

echo -e "${GREEN}Deployment completed successfully!${NC}"
echo -e "${GREEN}THECHATBOT is now available at: https://chatbot.dev.wasteos.net${NC}"
