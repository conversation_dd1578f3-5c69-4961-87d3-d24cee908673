{"family": "wos-dev-the<PERSON><PERSON>bot", "executionRoleArn": "arn:aws:iam::028864969427:role/dev-wos-ecs-task-execution-role", "taskRoleArn": "arn:aws:iam::028864969427:role/wos-dev-bedrock-role", "networkMode": "awsvpc", "containerDefinitions": [{"name": "thechatbot", "image": "028864969427.dkr.ecr.ap-southeast-1.amazonaws.com/wos-dev-wos-dev-thechatbot:latest", "essential": true, "portMappings": [{"containerPort": 8000, "hostPort": 8000, "protocol": "tcp"}], "environment": [{"name": "ENV", "value": "dev"}, {"name": "DB_DRIVER", "value": "postgresql+psycopg2"}, {"name": "DB_USER", "value": "wos_chatbot_ro"}, {"name": "DB_HOST", "value": "wos-dev-postgres.cl8g000io4f1.ap-southeast-1.rds.amazonaws.com"}, {"name": "DB_PORT", "value": "5432"}, {"name": "DB_NAME", "value": "wos_dev"}], "secrets": [{"name": "OPENAI_API_KEY", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:028864969427:secret:wos/dev/thechatbot/openai-TDr7hd:OPENAI_API_KEY::"}, {"name": "DB_PASSWORD", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:028864969427:secret:/wos/dev/sembwaste-dashboard/DB_RO_PASSWORD-tCgcFi:password::"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/wos-dev-thechatbot", "awslogs-region": "ap-southeast-1", "awslogs-stream-prefix": "ecs"}}, "healthCheck": {"command": ["CMD-SHELL", "curl -f http://localhost:8000/api/health-check || exit 1"], "interval": 60, "timeout": 15, "retries": 3, "startPeriod": 60}}], "requiresCompatibilities": ["FARGATE"], "cpu": "512", "memory": "1024", "tags": [{"key": "Project", "value": "WasteOS"}, {"key": "Environment", "value": "dev"}, {"key": "ManagedBy", "value": "Terraform"}, {"key": "Owner", "value": "Platform Team"}, {"key": "CostCenter", "value": "Engineering"}]}