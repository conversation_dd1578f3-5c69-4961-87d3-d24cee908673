{"cluster": "wos-dev-cluster", "serviceName": "wos-dev-thechatbot-service", "taskDefinition": "wos-dev-the<PERSON><PERSON>bot", "loadBalancers": [{"targetGroupArn": "arn:aws:elasticloadbalancing:ap-southeast-1:028864969427:targetgroup/wos-dev-thechatbot-tg/3af23d8866c6b2e1", "containerName": "thechatbot", "containerPort": 8000}], "desiredCount": 1, "launchType": "FARGATE", "platformVersion": "LATEST", "deploymentConfiguration": {"deploymentCircuitBreaker": {"enable": true, "rollback": true}, "maximumPercent": 200, "minimumHealthyPercent": 100}, "networkConfiguration": {"awsvpcConfiguration": {"subnets": ["subnet-0e9e296f69f74b034", "subnet-0d253ec81eed2a3b5"], "securityGroups": ["sg-02ebcc846f3704958"], "assignPublicIp": "DISABLED"}}, "healthCheckGracePeriodSeconds": 120, "schedulingStrategy": "REPLICA", "enableECSManagedTags": true, "propagateTags": "SERVICE", "tags": [{"key": "Project", "value": "WasteOS"}, {"key": "Environment", "value": "dev"}, {"key": "Application", "value": "thechatbot"}, {"key": "ManagedBy", "value": "GitHub Actions"}, {"key": "Owner", "value": "Platform Team"}, {"key": "CostCenter", "value": "Engineering"}]}