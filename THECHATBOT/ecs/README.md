# ECS Deployment for THECHATBOT

This directory contains the necessary files to deploy THECHATBOT to AWS ECS.

## Directory Structure

- `dev/`: Contains deployment files for the development environment
  - `task-definition.json`: ECS task definition
  - `service-definition.json`: ECS service definition
  - `deploy.sh`: Deployment script

## Prerequisites

Before deploying, ensure you have:

1. AWS CLI installed and configured with appropriate permissions
2. <PERSON><PERSON> installed and running
3. Access to the AWS account where the infrastructure is deployed
4. The necessary infrastructure components created via Terraform:
   - ECR Repository
   - Bedrock IAM Role
   - ALB Target Group
   - Security Group Rules
   - Secrets Manager for OpenAI API Key
   - CloudWatch Logs

## Deployment Process

### Manual Deployment

To deploy THECHATBOT to ECS manually:

1. Navigate to the project root directory
2. Run the deployment script:

```bash
./ECS/dev/deploy.sh
```

This script will:
- Build the Docker image
- Push it to ECR
- Register the task definition
- Create or update the ECS service

### CI/CD Deployment

For CI/CD pipelines, you can use the same files:

1. Include the task definition and service definition in your CI/CD pipeline
2. Use AWS CLI commands to:
   - Build and push the Docker image
   - Register the task definition
   - Update the service

## Environment Variables

The application requires the following environment variables:

- `PROJECT_NAME`: Name of the project
- `CORS_ALLOW_ORIGINS`: Comma-separated list of allowed origins
- `SERVER_HOST`: Host to bind the server to
- `SERVER_PORT`: Port to run the server on
- `DB_DRIVER`: Database driver
- `DB_HOST`: Database host
- `DB_PORT`: Database port
- `DB_NAME`: Database name
- `DB_USER`: Database username (from Secrets Manager)
- `DB_PASSWORD`: Database password (from Secrets Manager)
- `OPENAI_API_KEY`: OpenAI API key (from Secrets Manager)

## Accessing the Application

After deployment, the application will be available at:

- Development: https://chatbot.dev.wasteos.net

## Troubleshooting

If you encounter issues during deployment:

1. Check the CloudWatch Logs for the ECS task
2. Verify that the task definition and service definition are correct
3. Ensure that the security groups allow traffic to and from the ECS task
4. Check that the target group health checks are passing

For more detailed logs, you can use the AWS Management Console or AWS CLI:

```bash
aws logs get-log-events --log-group-name /ecs/wos-dev-thechatbot --log-stream-name <log-stream-name> --region ap-southeast-1
```
