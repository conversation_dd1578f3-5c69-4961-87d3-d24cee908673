from langchain.callbacks.streaming_stdout import StreamingStdOutCallbackHandler
from langchain_aws import ChatBedrock
from langchain_openai import ChatOpenAI

from app.constants import (
    AWS_MODEL_NAME,
    LLM_STREAMING,
    LLM_TEMPERATURE,
    OPEAI_LLM_NAME,
)
from app.env import (
    AWS_ACCESS_KEY_ID,
    AWS_REGION,
    AWS_SECRET_ACCESS_KEY,
    AWS_SESSION_TOKEN,
    ENV,
)
from app.utils.logger import get_logger

logger = get_logger(__name__)


def get_openai_llm() -> ChatOpenAI | None:
    try:
        return ChatOpenAI(
            temperature=LLM_TEMPERATURE,
            model=OPEAI_LLM_NAME,
            streaming=LLM_STREAMING,
            callbacks=[StreamingStdOutCallbackHandler()],
        )
    except Exception as e:
        logger.error(f"Error creating LLM instance: {str(e)}")
        raise


def get_bedrock_llm() -> ChatBedrock | None:
    try:
        if ENV == "local":
            chatbedrock_llm = ChatBedrock(
                region=AWS_REGION,
                aws_access_key_id=AWS_ACCESS_KEY_ID,  # type: ignore
                aws_secret_access_key=AWS_SECRET_ACCESS_KEY,  # type: ignore
                aws_session_token=AWS_SESSION_TOKEN,  # type: ignore
                model=AWS_MODEL_NAME,
                temperature=LLM_TEMPERATURE,
                streaming=LLM_STREAMING,
            )
        else:
            chatbedrock_llm = ChatBedrock(
                region=AWS_REGION,
                aws_access_key_id=AWS_ACCESS_KEY_ID,  # type: ignore
                aws_secret_access_key=AWS_SECRET_ACCESS_KEY,  # type: ignore
                model=AWS_MODEL_NAME,
                temperature=LLM_TEMPERATURE,
                streaming=LLM_STREAMING,
            )
        return chatbedrock_llm
    except Exception as e:
        logger.error(f"Error creating AWS Bedrock LLM instance: {str(e)}")
        raise


def get_llm(provider: str = "openai") -> ChatBedrock | ChatOpenAI | None:
    """Get an LLM instance based on the specified provider.

    Args:
        provider (str): The LLM provider to use (openai or bedrock)

    Returns:
        An LLM instance

    Raises:
        ValueError: If the provider is not supported
    """
    if provider == "openai":
        return get_openai_llm()
    elif provider == "bedrock":
        return get_bedrock_llm()
    else:
        raise ValueError(f"Unsupported LLM provider: {provider}")
