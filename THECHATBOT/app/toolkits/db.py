from langchain_aws import ChatBedrock
from langchain_community.agent_toolkits.sql.toolkit import SQLDatabaseToolkit
from langchain_community.utilities.sql_database import SQLDatabase
from langchain_openai import ChatOpenAI

from app.utils.logger import get_logger

logger = get_logger(__name__)

custom_table_info = {
    "AnalyticsData": """
        Table: "AnalyticsData"

        This table stores analytics information for recyclable or non recyclable bags (category column).
        Each bag will have a material type based on subcategory column.
        Each row represents a single time interval based on timestamp column.

        Columns:
        - id (Primary Key, VARCHAR): Unique identifier for the analytics record
        - type (VARCHAR): Type of analytics data
        - category (VARCHAR): Indicates whether a material is recyclable or non_recyclable. Example: recyclables, non_recyclables
        - subcategory (VARCHAR): Represents the type of waste category (recyclables/non_recyclable)
          associated with an item. Examples: glass, metal, carton, mix_recycle, etc
        - frequency (VARCHAR): Frequency of data collection. Example: adhoc
        - timestamp (DateTime): Time the data was recorded
        - value (Float): Measured value
        - unit (VARCHAR): Unit of weight measurement for value. Example: kg
        - metadata (JSON): Additional metadata. Ignore this column
        - entityId (Foreign Key → Entity.id):  Associated entity ID in Entity table.
    """,  # noqa: E501
    "Entity": """
        Table: "Entity"

        This table stores information about
        entities (e.g., companies, organizations, etc.).

        Columns:
        - id (Primary Key, VARCHAR): Unique identifier for the entity.
          Maps to "AnalyticsData.entityId" as Foreign Key
        - name (VARCHAR): Name of the entity or company
        - type (VARCHAR): Type of entity (e.g. mbs_area, mbs_tenant, etc.)
        - industry (VARCHAR): Industry category of
          entity (e.g. tenant, waste management, area)
    """,  # noqa: E501
}


def create_sql_db_toolkit(
    db_uri: str, include_tables: list, llm: ChatOpenAI | ChatBedrock
) -> SQLDatabaseToolkit | None:
    try:
        logger.info("Creating SQL DB")
        db = SQLDatabase.from_uri(
            db_uri,
            include_tables=include_tables or [],
            custom_table_info=custom_table_info,
        )
        return SQLDatabaseToolkit(db=db, llm=llm)
    except Exception as e:
        logger.error(f"Error connecting to DB: {str(e)}")
        raise
