import hashlib
import json
from collections.abc import Sequence

import psycopg
from langchain.memory import ConversationBufferMemory
from langchain_core.messages import BaseMessage, message_to_dict
from langchain_postgres import PostgresChatMessageHistory
from psycopg import sql

from app.constants import CHAT_HISTORY_TABLE_NAME, DB_URI_MEMORY
from app.utils.logger import get_logger

logger = get_logger(__name__)


def generate_sha256(input_str: str):
    return hashlib.sha256(input_str.encode()).hexdigest()


def _insert_message_query(table_name: str):
    """Make a SQL query to insert a message."""
    return sql.SQL(
        """
        INSERT INTO {table_name} (
                user_id,
                session_id,
                message_id,
                message,
                created_at,
                updated_at
            )
            VALUES (
                %(user_id)s,
                %(session_id)s,
                %(message_id)s,
                %(message)s,
                NOW() at time zone 'utc',
                NOW() at time zone 'utc'
            )
            """
    ).format(table_name=sql.Identifier(table_name))


class CustomChatMessageHistory(PostgresChatMessageHistory):
    def __init__(
        self,
        user_id: str,
        table_name: str,
        session_id: str,
        sync_connection: psycopg.Connection | None = None,
        async_connection: psycopg.AsyncConnection | None = None,
    ):
        super().__init__(
            table_name,
            session_id,
            sync_connection=sync_connection,
            async_connection=async_connection,
        )
        self.user_id = user_id

    def add_messages(self, messages: Sequence[BaseMessage]) -> None:
        if self._connection is None:
            raise ValueError("Sync connection required")

        values = [
            {
                "user_id": self.user_id,
                "session_id": self._session_id,
                "message_id": generate_sha256(
                    f"{self.user_id}+{self._session_id}+{json.dumps(message_to_dict(message))}"
                ),
                "message": json.dumps(message_to_dict(message)),
            }
            for message in messages
        ]
        # logger.info(f"values: {values}")

        query = _insert_message_query(self._table_name)
        with self._connection.cursor() as cur:
            cur.executemany(query, values)

        self._connection.commit()

    async def aadd_messages(self, messages: Sequence[BaseMessage]) -> None:
        """Add messages to the chat message history."""
        if self._aconnection is None:
            raise ValueError(
                "Please initialize the PostgresChatMessageHistory "
                "with an async connection or use the sync add_messages method instead."
            )

        values = [
            {
                "user_id": self.user_id,
                "session_id": self._session_id,
                "message_id": generate_sha256(
                    f"{self.user_id}+{self._session_id}+{json.dumps(message_to_dict(message))}"
                ),
                "message": json.dumps(message_to_dict(message)),
            }
            for message in messages
        ]

        query = _insert_message_query(self._table_name)
        async with self._aconnection.cursor() as cursor:
            await cursor.executemany(query, values)
        await self._aconnection.commit()

    def close(self) -> None:
        """Close the sync connection if it exists."""
        if self._connection is not None:
            self._connection.close()

    async def aclose(self) -> None:
        """Close the async connection if it exists."""
        if self._aconnection is not None:
            await self._aconnection.close()


def get_chat_memory_manager(user_id, session_id) -> ConversationBufferMemory:
    chat_manager = CustomChatMessageHistory(
        user_id=user_id,
        table_name=CHAT_HISTORY_TABLE_NAME,
        session_id=session_id,
        sync_connection=psycopg.connect(DB_URI_MEMORY),
    )

    return ConversationBufferMemory(
        memory_key="chat_history",
        return_messages=True,
        chat_memory=chat_manager,
    )


async def get_async_chat_memory_manager(
    user_id, session_id
) -> ConversationBufferMemory:
    async_conn = await psycopg.AsyncConnection.connect(DB_URI_MEMORY)
    chat_manager = CustomChatMessageHistory(
        user_id=user_id,
        table_name=CHAT_HISTORY_TABLE_NAME,
        session_id=session_id,
        async_connection=async_conn,
    )
    return ConversationBufferMemory(
        memory_key="chat_history",
        return_messages=True,
        chat_memory=chat_manager,
    )
