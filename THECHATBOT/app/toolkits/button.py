import json
import shlex

from langchain.tools import BaseTool, Tool
from pydantic import BaseModel, Field

from app.env import WOS_BACKEND_URL


class RouteButtonTool(BaseTool):
    name: str = "route_button_tool"
    description: str = (
        "Creates a button that, when clicked, routes the user to a specific endpoint "
        "with ticker, start_date, and end_date as query parameters (expects args-schema)."
    )

    class Args(BaseModel):
        entity: str = Field(..., description="The entity or company name")
        start_date: str | None = Field(None, description="YYYY-MM-DD")
        end_date: str | None = Field(None, description="YYYY-MM-DD")

    args_schema = Args

    def _run(
        self, entity: str, start_date: str | None = None, end_date: str | None = None
    ) -> dict:
        params = [f"entity={entity}"]
        if start_date:
            params.append(f"start_date={start_date}")
        if end_date:
            params.append(f"end_date={end_date}")
        url = f"{WOS_BACKEND_URL}?{'&'.join(params)}"
        return {"type": "button", "label": f"View {entity} Data", "url": url}

    async def _arun(
        self, entity: str, start_date: str | None = None, end_date: str | None = None
    ) -> dict:
        return self._run(entity, start_date, end_date)


def route_button_wrapper(input_str: str) -> str:
    """
    Expects input like: "entity=AAPL start_date=2025-05-01 end_date=2025-05-24"
    """
    parts = shlex.split(input_str)
    kwargs = {}
    for part in parts:
        if "=" in part:
            key, val = part.split("=", 1)
            kwargs[key] = val

    # Check for required keys
    if "entity" not in kwargs:
        return json.dumps({"error": "Missing required parameter: entity"})

    result = RouteButtonTool()._run(**kwargs)
    return json.dumps(result)


route_button_tool = Tool(
    name="route_button_tool",
    func=route_button_wrapper,
    description=(
        "Generate a routing button. Use single-input string of key=value pairs, e.g.,"
        " 'entity=AAPL start_date=YYYY-MM-DD end_date=YYYY-MM-DD'."
    ),
)
