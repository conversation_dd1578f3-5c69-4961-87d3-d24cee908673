import time

from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware

from app.chat.router import router as chat_router
from app.health_check.router import router as health_check_router


def create_chatbot():
    app = FastAPI(
        generate_unique_id_function=lambda router: f"{router.tags[0]}-{router.name}",
    )

    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Include the routers
    app.include_router(chat_router)
    app.include_router(health_check_router)

    return app


app = create_chatbot()


@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    """
    Simple middleware to claculate the time taken to process each request and
    add it to the response
    header. Useful when optimising the application.

    This can be removed by removing the middleware from the app.

    The middleware is added via the app.middleware decorator above this function.
    """
    start_time = time.perf_counter()
    response = await call_next(request)
    process_time = time.perf_counter() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response
