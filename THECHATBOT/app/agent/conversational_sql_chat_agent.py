import asyncio
from ast import literal_eval
from typing import Any

from langchain.agents import initialize_agent
from langchain.agents.agent import Agent<PERSON>xecutor
from langchain.agents.agent_types import AgentType
from langchain.memory import ConversationBufferMemory
from langchain.prompts import MessagesPlaceholder
from langchain_aws import ChatBedrock
from langchain_community.tools.sql_database.tool import (
    InfoSQLDatabaseTool,
    QuerySQLCheckerTool,
    QuerySQLDatabaseTool,
)
from langchain_community.utilities.sql_database import SQLDatabase
from langchain_openai import ChatOpenAI

from app.agent.models import AgentResponse
from app.agent.prompts import SQL_PREFIX
from app.constants import DEFAULT_ERROR_RESPONSE, GREETING_RESPONSE, NEGATIVE_RESPONSE
from app.guard_rail.service import guardrail_check
from app.toolkits.button import route_button_tool
from app.utils.logger import get_logger

logger = get_logger(__name__)


class ConversationalSQLChatAgent:
    def __init__(
        self,
        db: SQLDatabase,
        llm: ChatOpenAI | ChatBedrock,
        memory: ConversationBufferMemory | None = None,
        sql_prefix: str = SQL_PREFIX,
        top_k: int = 5,
    ):
        self._agent = None
        self._format_agent_response_type = None
        logger.info("Initializing ConversationalSQLChatAgent")

        if isinstance(llm, ChatOpenAI):
            self._agent = AgentType.OPENAI_FUNCTIONS
            self._format_agent_response_type = "openai"
        else:
            self._agent = "tool-calling"
            self._format_agent_response_type = "aws_bedrock"

        sql_prefix = sql_prefix.format(
            dialect=db.dialect,
            top_k=top_k,
            negative_response=NEGATIVE_RESPONSE,
            greeting_response=GREETING_RESPONSE,
        )

        # agent = AgentType.CONVERSATIONAL_REACT_DESCRIPTION
        # agent = AgentType.CHAT_CONVERSATIONAL_REACT_DESCRIPTION
        # agent = AgentType.CHAT_ZERO_SHOT_REACT_DESCRIPTION
        agent = AgentType.STRUCTURED_CHAT_ZERO_SHOT_REACT_DESCRIPTION

        logger.info("Creating custom SQL toolkit")
        tools = self._create_custom_sql_toolkit(db, llm) + [
            route_button_tool,
        ]
        logger.info(f"Registered tools: {[tool.name for tool in tools]}")

        logger.info("Creating Conversational SQL Chat Agent")
        self._agent_executor = self._create_agent(
            llm=llm, agent=agent, memory=memory, tools=tools, sql_prefix=sql_prefix
        )

    def _create_custom_sql_toolkit(
        self, db: SQLDatabase, llm: ChatOpenAI | ChatBedrock
    ):
        info_sql_database_tool_description = (
            f"Input to this tool is a comma-separated list of known \
            tables: {', '.join(db._custom_table_info.keys())}. "  # type: ignore
            "It returns the schema and sample rows for the specified tables. "
            "Use this tool when you need to inspect the structure or example \
            data of those tables. "
            'Table names in the database are in camelcase. Use double quotes \
            in your SQL query, for example: SELECT column_1 FROM "MyTable";'
            "Example Input: TableOne, Table2"
        )

        info_sql_database_tool = InfoSQLDatabaseTool(
            db=db, description=info_sql_database_tool_description
        )

        query_sql_database_tool_description = (
            "Input to this tool is a detailed and correct SQL query, output is"
            "a result from the database. If the query is not correct,"
            "an error message will be returned."
            "If an error is returned, rewrite the query, check the "
            "query, and try again. If you encounter an issue with "
            "Unknown column 'xxxx' in 'field list',"
            f"use {info_sql_database_tool.name} to query"
            "the correct table fields."
        )
        query_sql_database_tool = QuerySQLDatabaseTool(
            db=db, description=query_sql_database_tool_description
        )

        query_sql_checker_tool_description = (
            "Use this tool to double check if your query is correct before"
            "executing it. Always use this tool before executing a query with "
            f"{query_sql_database_tool.name}!"
        )
        query_sql_checker_tool = QuerySQLCheckerTool(
            db=db, llm=llm, description=query_sql_checker_tool_description
        )

        return [
            query_sql_database_tool,
            info_sql_database_tool,
            query_sql_checker_tool,
        ]

    def _create_agent(
        self,
        llm: ChatOpenAI | ChatBedrock,
        memory: ConversationBufferMemory | None,
        agent: AgentType,
        tools: list,
        sql_prefix: str,
    ) -> AgentExecutor:
        chat_history = MessagesPlaceholder(variable_name="chat_history")

        return initialize_agent(
            tools=tools,
            llm=llm,
            agent=agent,
            agent_kwargs={
                "prefix": sql_prefix,
                "return_intermediate_steps": True,
                "memory_prompts": [chat_history],
                "input_variables": ["input", "agent_scratchpad", "chat_history"],
            },
            memory=memory,
            verbose=True,
            handle_parsing_errors=True,
        )

    def chat(self, query: str) -> AgentResponse:
        return self._chat_with_agent(query)

    async def chat_stream(self, query: str):
        return self._chat_with_agent(query)

    def _chat_with_agent(self, query: str) -> AgentResponse:
        """Run user input through the SQL agent and return response."""
        try:
            logger.info("Checking for sql guard rails")
            guard_rail = guardrail_check(query)
            logger.info(f"guardrail_check output: {guard_rail}")
            if not guard_rail.is_sql_like:
                response = self._agent_executor.stream(
                    {
                        "input": query,
                        "chat_history": self._agent_executor.memory.chat_memory.get_messages(),
                    }
                )
                output, button = self._format_stream_agent_response(response)
            else:
                logger.info("Failed guard rails")
                output, button = guard_rail.response, None
        except Exception as e:
            logger.error(f"Error: {str(e)}")
            output, button = DEFAULT_ERROR_RESPONSE, None
        return AgentResponse(response=output, button=button)

    def achat(self, query: str):
        return self._achat_with_agent(query)

    async def achat_stream(self, query: str):
        response = await self._achat_with_agent(query)
        return response

    async def _achat_with_agent(self, query: str):
        try:
            # Offload sync guard rails and agent invocation to thread pool
            response = await asyncio.to_thread(self._chat_with_agent, query)
        except Exception as e:
            logger.error(f"Error in _achat_with_agent: {str(e)}")
            return AgentResponse(response=DEFAULT_ERROR_RESPONSE, button=None)
        return response

    def _format_agent_response(self, response: Any) -> str:
        """
        Format the response from the agent.
        OpenAI and Claude in Bedrock has different responses
        """
        if self._format_agent_response_type == "openai":
            return response
        elif self._format_agent_response_type == "aws_bedrock":
            return response.get("output", NEGATIVE_RESPONSE)
        else:
            raise Exception("Invalid format agent response type")

    def _format_stream_agent_response(self, response: Any) -> tuple[str, dict | None]:
        if self._format_agent_response_type == "openai":
            return response
        elif self._format_agent_response_type == "aws_bedrock":
            output = NEGATIVE_RESPONSE
            button = None
            for tool in response:
                for k, v in tool.items():
                    if k == "output":
                        output = tool[k]
                    if k == "steps" and v[0].action.tool == "route_button_tool":
                        logger.info("Getting button!!")
                        try:
                            button = literal_eval(v[0].observation)
                        except Exception as err:
                            logger.error(f"error: {err}")
                            button = v[0].observation
            return output, button
        else:
            raise Exception("Invalid format agent response type")
