from collections.abc import AsyncGenerator
from typing import Any

from langchain.agents import AgentType, create_sql_agent
from langchain_aws import ChatBedrock
from langchain_community.agent_toolkits.sql.toolkit import SQLDatabaseToolkit
from langchain_openai import Chat<PERSON><PERSON><PERSON><PERSON>

from app.agent.prompts import SQL_PREFIX
from app.constants import (
    DEFAULT_ERROR_RESPONSE,
    GREETING_RESPONSE,
    NEGATIVE_RESPONSE,
    VERBOSE,
)
from app.guard_rail.service import guardrail_check
from app.utils.logger import get_logger

logger = get_logger(__name__)


class SQLChatAgent:
    def __init__(
        self,
        db_toolkit: SQLDatabaseToolkit,
        llm: ChatOpenAI | ChatBedrock,
        sql_prefix: str = SQL_PREFIX,
        top_k: int = 5,
    ):
        self._agent = None
        self._format_agent_response_type = None
        logger.info("Initializing SQLChatAgent")

        if isinstance(llm, ChatOpenAI):
            self._agent = AgentType.OPENAI_FUNCTIONS
            self._format_agent_response_type = "openai"
        else:
            self._agent = "tool-calling"
            self._format_agent_response_type = "aws_bedrock"

        logger.info(f"Agent type: {self._agent}")

        sql_prefix = sql_prefix.format(
            dialect=db_toolkit.dialect,
            top_k=top_k,
            negative_response=NEGATIVE_RESPONSE,
            greeting_response=GREETING_RESPONSE,
        )

        self._agent_executor = create_sql_agent(
            llm=llm,
            toolkit=db_toolkit,
            verbose=VERBOSE,
            prefix=sql_prefix,
            agent_type=self._agent,
        )

    def run(self, query: str) -> str:
        return self._chat_with_agent(query)

    async def stream(self, query: str):
        for chunk in self._chat_with_agent(query):
            yield chunk

    def _chat_with_agent(self, query: str) -> str:
        """Run user input through the SQL agent and return response."""
        try:
            logger.info("Checking for sql guard rails")
            guard_rail = guardrail_check(query)
            logger.info(f"guardrail_check output: {guard_rail}")
            if not guard_rail.is_sql_like:
                response = self._agent_executor.invoke(
                    {"input": query}, return_only_outputs=True
                )
                response = response.get("output", [{}])[-1].get(
                    "text", NEGATIVE_RESPONSE
                )
            else:
                logger.info("Failed guard rails")
                response = guard_rail.response
            return response
        except Exception as e:
            logger.error(f"Error: {str(e)}")
            return DEFAULT_ERROR_RESPONSE

    def arun(self, query: str):
        return self._achat_with_agent(query)

    async def astream(self, query: str):
        async for chunk in self._achat_with_agent(query):
            yield chunk
            # await asyncio.sleep(STREAM_SLEEP)

    async def _achat_with_agent(self, query: str) -> AsyncGenerator:
        """Run user input through the SQL agent and return response."""
        logger.info("Running _achat_with_agent")
        try:
            logger.info("Checking for sql guard rails")
            guard_rail = guardrail_check(query)
            logger.info(f"guardrail_check output: {guard_rail}")
            if not guard_rail.is_sql_like:
                response = await self._agent_executor.ainvoke(
                    {"input": query}, return_only_outputs=True
                )
                response = response.get("output", [{}])[-1].get(
                    "text", NEGATIVE_RESPONSE
                )
            else:
                logger.info("Failed guard rails")
                response = guard_rail.response
            yield response
        except Exception as e:
            logger.error(f"Error: {str(e)}")
            yield DEFAULT_ERROR_RESPONSE

    def _format_agent_response(self, response: Any) -> str:
        """
        Format the response from the agent.
        OpenAI and Claude in Bedrock has different responses
        """
        if self._format_agent_response_type == "openai":
            return response
        elif self._format_agent_response_type == "aws_bedrock":
            return response.get("output", [{}])[-1].get("text", NEGATIVE_RESPONSE)
        else:
            raise Exception("Invalid format agent response type")
