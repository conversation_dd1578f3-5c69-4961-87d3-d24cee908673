from fastapi import APIRouter
from fastapi.responses import JSONResponse

from app.health_check.model import HealthCheck
from app.utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter(prefix="/api", tags=["health_check"])


@router.get("/health-check")
async def health_check():
    logger.info("Health check endpoint called")
    response = HealthCheck(status="ok")
    return JSONResponse(content={"response": response.model_dump_json()})
