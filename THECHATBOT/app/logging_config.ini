[loggers]
keys=root

[handlers]
keys=stream_handler, rotating_file_handler

[formatters]
keys=formatter

[logger_root]
level=INFO
handlers=stream_handler, rotating_file_handler

[handler_stream_handler]
class=StreamHandler
level=INFO
formatter=formatter
args=(sys.stderr,)

[handler_rotating_file_handler]
class=handlers.TimedRotatingFileHandler
level=INFO
formatter=formatter
args=('logs/app.log', 'midnight', 1, 60) # Log rotation at midnight, daily, keep all backups

[formatter_formatter]
format=%(process)d %(asctime)s %(name)-8s %(levelname)-8s %(message)s
