from uuid import uuid4

from fastapi import APIRouter, Request, WebSocket, WebSocketDisconnect
from fastapi.responses import J<PERSON><PERSON>esponse, StreamingResponse

from app.agent.conversational_sql_chat_agent import ConversationalSQLChatAgent
from app.chat.models import (
    InChatMessage,
    NewChatSessionRequest,
    OutChatMessage,
    OutNewChatSessionRequest,
)
from app.chat.utils import event_generator
from app.constants import (
    DB_INCLUDE_TABLES,
    DB_URI,
    DEFAULT_ERROR_RESPONSE,
    TOP_K,
)
from app.toolkits.db import create_sql_db_toolkit
from app.toolkits.llm import get_llm
from app.toolkits.memory import get_chat_memory_manager
from app.utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter(prefix="/api", tags=["chat"])

llm = get_llm(provider="bedrock")
if not llm:
    raise Exception("Failed to initialize LLM")

sql_db_toolkit = create_sql_db_toolkit(
    DB_URI, include_tables=DB_INCLUDE_TABLES, llm=llm
)
if not sql_db_toolkit:
    raise Exception("Failed to initialize sql_db_toolkit")

sql_chat_agent = ConversationalSQLChatAgent(db=sql_db_toolkit.db, llm=llm, top_k=TOP_K)


@router.post("/create-new-session")
def new_session(req: NewChatSessionRequest):
    try:
        session_id = str(uuid4())
        out_chat_session = OutNewChatSessionRequest(
            user_id=req.user_id, session_id=session_id
        )
        status_code = 200
    except Exception as e:
        logger.error(f"Error: {e}")
        out_chat_session = OutNewChatSessionRequest(
            user_id=req.user_id, session_id="ERROR"
        )
        status_code = 500
    # TODO change to proper return later
    return JSONResponse(content=out_chat_session.model_dump(), status_code=status_code)


@router.post("/chat")
async def chat(chat_message: InChatMessage):
    user_id = chat_message.user_id
    message = chat_message.message
    session_id = chat_message.session_id

    logger.info(f"User ID: {user_id} | message: {message} | session_id: {session_id}")

    try:
        sql_chat_agent._agent_executor.memory = get_chat_memory_manager(
            user_id=user_id, session_id=session_id
        )
        response = sql_chat_agent.chat(message)
        logger.info(f"User ID: {user_id} | Response: {response}")
        response = OutChatMessage(
            response=response.response, user_id=user_id, button=response.button
        )
    except Exception as err:
        logger.error(f"Error: {str(err)}")
        response = OutChatMessage(response=DEFAULT_ERROR_RESPONSE, user_id=user_id)
    return JSONResponse(content=response.model_dump())


@router.post("/chat-stream")
async def chat_stream(chat_message: InChatMessage, request: Request):
    user_id = chat_message.user_id
    message = chat_message.message
    session_id = chat_message.session_id

    logger.info(f"User ID: {user_id} | message: {message} | session_id: {session_id}")

    sql_chat_agent._agent_executor.memory = get_chat_memory_manager(
        user_id=user_id, session_id=session_id
    )
    response = await sql_chat_agent.chat_stream(message)

    return StreamingResponse(event_generator(response), media_type="text/event-stream")


@router.post("/achat")
async def achat(chat_message: InChatMessage):
    user_id = chat_message.user_id
    message = chat_message.message
    session_id = chat_message.session_id

    logger.info(f"User ID: {user_id} | message: {message} | session_id: {session_id}")
    try:
        memory = get_chat_memory_manager(user_id=user_id, session_id=session_id)
        sql_chat_agent._agent_executor.memory = memory
        result = await sql_chat_agent.achat(message)
        logger.info(f"User ID: {user_id} | Response: {result}")
        response = OutChatMessage(
            response=result.response, user_id=user_id, button=result.button
        )

    except Exception as err:
        logger.error(f"Error: {str(err)}")
        response = OutChatMessage(
            response=DEFAULT_ERROR_RESPONSE, user_id=chat_message.user_id
        )
    return JSONResponse(content=response.model_dump())


@router.post("/achat-stream")
async def achat_stream(chat_message: InChatMessage, request: Request):
    user_id = chat_message.user_id
    message = chat_message.message
    session_id = chat_message.session_id

    logger.info(f"User ID: {user_id} | message: {message} | session_id: {session_id}")

    memory = get_chat_memory_manager(user_id=user_id, session_id=session_id)
    sql_chat_agent._agent_executor.memory = memory
    response = await sql_chat_agent.achat_stream(message)

    return StreamingResponse(event_generator(response), media_type="text/event-stream")


@router.websocket("/chat-ws")
async def websocket_chat(websocket: WebSocket):
    # NOTE: this is an old endpoint. Have not tested if it is working now
    # and will not be exposed.
    await websocket.accept()
    try:
        while True:
            message = await websocket.receive_text()
            try:
                response = sql_chat_agent.chat(message)
                await websocket.send_text(response.response)
            except Exception as err:
                logger.error(f"WebSocket Error: {str(err)}")
                await websocket.send_text(DEFAULT_ERROR_RESPONSE)
    except WebSocketDisconnect:
        logger.info("WebSocket connection closed")
