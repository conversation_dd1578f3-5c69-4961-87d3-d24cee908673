from app.env import (
    DB_DRIVER,
    DB_HOST,
    DB_NAME,
    DB_PASSWORD,
    DB_PORT,
    DB_RW_PASSWORD,
    DB_RW_USER,
    DB_USER,
)

OPEAI_LLM_NAME = "gpt-4o-mini"
AWS_MODEL_NAME = "anthropic.claude-3-5-sonnet-20240620-v1:0"

DB_URI = f"{DB_DRIVER}://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
DB_URI_MEMORY = (
    f"postgresql://{DB_RW_USER}:{DB_RW_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
)
DB_INCLUDE_TABLES = ["AnalyticsData", "Entity"]
CHAT_HISTORY_TABLE_NAME = "ChatHistory"

VERBOSE = True
LLM_STREAMING = True
LLM_TEMPERATURE = 0

DEFAULT_ERROR_RESPONSE = "Sorry, I'm having trouble processing your question."
NEGATIVE_RESPONSE = "I'm sorry, I am not able to answer your question."
GREETING_RESPONSE = (
    "Hello! I can help you query data from the database. What would you like to know?"
)
TOP_K = 5

STREAM_SLEEP = 0.015
