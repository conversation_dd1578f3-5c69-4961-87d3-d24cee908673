import re

from app.constants import NEGATIVE_RESPONSE
from app.guard_rail.models import GuardRail

SQL_PATTERN = re.compile(
    r"""
    \b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|JOIN|SET|UNION)\b
    | \*
    | \b[a-zA-Z_]+\s*,\s*[a-zA-Z_]+
    | \bFROM\s+[a-zA-Z_]+\b
    """,
    re.IGNORECASE | re.VERBOSE,
)


def _check_sql_input(user_input: str) -> bool:
    return bool(SQL_PATTERN.search(user_input))


def guardrail_check(input_text: str) -> GuardRail:
    is_sql_input = _check_sql_input(input_text)
    response = NEGATIVE_RESPONSE if is_sql_input else input_text
    return GuardRail(is_sql_like=is_sql_input, response=response)
