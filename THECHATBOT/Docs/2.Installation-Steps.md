# Running THECHATBOT Locally

Let me help you set up and run THECHATBOT locally. I'll first examine the project's dependencies and then provide step-by-step instructions for running it.

## Project Dependencies

Let's first look at the dependencies defined in the pyproject.toml file:

pyproject.toml

**Copy**

**[**project**]**

**dependencies** **=** **[**

**    **"fastapi[all]>=0.115.12"**,**

**    **"litellm>=1.64.1"**,**

**    **"pydantic-settings>=2.8.1"**,**

**    **"pydantic>=2.11.0"**,**

**    **"sqlalchemy[asyncio]>=2.0.40"**,**

**    **"pre-commit>=4.2.0"**,**

**    **"pytest>=8.3.5"**,**

**    **"pytest-cov>=6.0.0"**,**

**]**

The project also has development dependencies:

pyproject.toml

**Copy**

**[**tool.uv**]**

**dev-**dependencies** **=** **[

**    **"pre-commit>=3.8.0"**,**

**    **"pytest-cov>=5.0.0"**,**

**    **"pytest>=8.3.3"**,**

**    **"ruff>=0.6.5"**,**

**]**

## Step-by-Step Setup Instructions

Based on the Makefile and project structure, here's how to set up and run the project locally:

### 1. Install uv (Python package manager)

**Copy**

**curl **-**LsSf https://astral**.**sh**/**uv**/**install**.**sh **|** sh**

### 2. Set up the virtual environment

**Copy**

**uv** **venv** **--python** 3**.**12

**source** **.venv**/**bin**/**activate**

### 3. Install dependencies

**Copy**

**uv pip install -r pyproject.toml**

### 4. Set up pre-commit hooks (optional for development)

**Copy**

**uv pip install pre**-**commit**

**pre**-**commit** install

### 5. Set up PostgreSQL

You'll need a PostgreSQL database running. The default configuration expects:

* Host: localhost
* Port: 5432
* User: postgres
* Password: postgres
* Database: postgres

You can modify these settings by creating a `.env` file with the following variables:

**Copy**

**DB_DRIVER**=**postgresql+asyncpg**

**DB_USER**=**your_username**

**DB_PASSWORD**=**your_password**

**DB_HOST**=**your_host**

**DB_PORT**=**your_port**

**DB_DB**=**your_database**

### 6. Run the application

The project has a script entry point defined in pyproject.toml:

**Copy**

**python -m app.main**

Or you can use the defined script:

**Copy**

**dev**

This will start the FastAPI server on localhost:8000.

## Checking the API

Once the server is running, you can access:

* API documentation: [http://localhost:8000/docs](http://localhost:8000/docs)
* Alternative API documentation: [http://localhost:8000/redoc](http://localhost:8000/redoc)

## Additional Requirements

1. **Python 3.12** : The project requires Python 3.12 or higher as specified in the pyproject.toml file.
2. **PostgreSQL** : You need a PostgreSQL database server running and accessible.
3. **LiteLLM API Key** : Since the project uses LiteLLM for AI model integration, you might need to set up API keys for the LLM provider you want to use (like OpenAI). This would typically be configured in the `.env` file, though the current implementation doesn't yet specify this requirement.

## Current Limitations

It's worth noting that the project appears to be in early development:

1. The chat endpoint is defined but not implemented
2. The agent implementation is minimal
3. There might be missing functionality or incomplete features
