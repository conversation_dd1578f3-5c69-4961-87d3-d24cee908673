# AWS ECS Deployment Checklist for THECHATBOT

This document provides a comprehensive checklist of infrastructure components required to deploy THECHATBOT to an AWS ECS cluster.

## Core Infrastructure Components

### Container Management

- [X] **ECS Cluster**

  - [X] Define cluster capacity providers (EC2 or Fargate)
  - [X] Configure cluster settings and defaults
  - [X] Set up cluster auto-scaling (if using EC2)
- [X] **ECS Task Definition**

  - [X] Define container specifications
    - [X] Container name (e.g., "wos-chatbot")
    - [X] Docker image URI from ECR
    - [X] Essential container flag
    - [X] Port mappings (8000:8000)
  - [X] Set CPU and memory requirements
    - [X] Task-level CPU units
    - [X] Task-level memory (MB)
    - [X] Container-level memory limits
  - [X] Configure environment variables
    - [X] DB_DRIVER, DB_HOST, DB_PORT, DB_NAME, DB_USER
    - [X] PROJECT_NAME, CORS_ALLOW_ORIGINS
    - [X] SERVER_HOST, SERVER_PORT
    - [X] OPENAI_API_KEY (via Secrets Manager)
  - [X] Set up container health checks
    - [X] Health check command
    - [X] Interval, timeout, retries, start period
  - [X] Define volume mounts (if needed)
  - [X] Configure logging options
    - [X] CloudWatch log group
    - [X] Log driver configuration
  - [X] Set network mode (awsvpc for Fargate)
  - [X] Configure task execution role
  - [X] Configure task role
- [X] **ECS Service**

  - [X] Set desired task count
    - [X] Initial number of tasks to run
  - [X] Configure deployment strategy
    - [X] Deployment type (rolling update)
    - [X] Minimum healthy percent
    - [X] Maximum percent
  - [X] Configure network settings
    - [X] VPC ID
    - [X] Subnet IDs (private subnets)
    - [X] Security group IDs
  - [X] Configure load balancer integration
    - [X] Target group ARN
    - [X] Container name and port
    - [X] Health check grace period
  - [ ] Set up service auto-scaling (Later Stage)
    - [ ] Minimum and maximum task count
    - [ ] Scaling policies and metrics
  - [ ] Configure service discovery (if needed)
    - [ ] Namespace details
    - [ ] Service discovery name
  - [X] Set launch type (Fargate or EC2)
  - [X] Configure platform version (for Fargate)
  - [X] Set up tags for resource organization
- [X] **ECR Repository**

  - [X] Create repository for application images
    - [X] Repository name (e.g., "wos-chatbot")
    - [X] Repository tags
  - [X] Set up repository permissions
    - [X] IAM policies for push/pull access
    - [ ] Cross-account access (if needed)
  - [X] Configure lifecycle policies
    - [X] Image retention rules (Keep only the last 10 images)
    - [X] Untagged image cleanup
  - [X] Set up image scanning
    - [X] Scan on push configuration
  - [X] Configure repository encryption (AES256)
  - [ ] Document image tagging strategy

### Database

- [X] **RDS PostgreSQL**
  - [X] Select appropriate instance size
  - [X] Configure storage (type and size)
  - [X] Set up backup retention policy
  - [X] Configure multi-AZ deployment (for production)
  - [X] Set up parameter groups
  - [X] Configure security groups

### Networking

- [X] **VPC**

  - [X] Define CIDR block
  - [X] Configure DHCP options
  - [X] Set up VPC endpoints (if needed)
- [X] **Subnets**

  - [X] Create public subnets for ALB
  - [X] Create private subnets for ECS tasks
  - [X] Create isolated subnets for RDS
  - [X] Configure route tables
- [X] **Security Groups**

  - [X] ALB security group
  - [X] ECS tasks security group
  - [X] RDS security group
  - [X] Define inbound/outbound rules
- [X] **Application Load Balancer (ALB)**

  - [X] Configure listeners (HTTP/HTTPS)
  - [X] Set up SSL certificates
  - [X] Configure health checks
  - [X] Set up routing rules
- [X] **Target Group**

  - [X] Configure protocol and port
    - [X] Protocol (HTTP/HTTPS)
    - [X] Port (8000)
    - [X] Target type (IP for Fargate)
  - [X] Set up health check path and thresholds
    - [X] Health check path (/api/v1/health or /docs)
    - [X] Health check protocol
    - [X] Interval, timeout, healthy threshold, unhealthy threshold
  - [X] Configure deregistration delay
    - [X] Draining timeout
  - [ ] Configure stickiness (if needed)
  - [X] Set up target group attributes

### Access Management

- [X] **IAM Roles**
  - [X] ECS task execution role
    - [X] ECR image pull permissions
    - [X] CloudWatch Logs write permissions
    - [X] Secrets Manager read permissions
  - [X] ECS task role
    - [X] RDS PostgreSQL access permissions
    - [X] S3 access (if needed)
    - [X] Other AWS service permissions required by the application (Bedrock)
  - [X] GitHub Actions deployment role
    - [X] ECR permissions
      - [X] `ecr:GetAuthorizationToken`
      - [X] `ecr:BatchCheckLayerAvailability`
      - [X] `ecr:GetDownloadUrlForLayer`
      - [X] `ecr:BatchGetImage`
      - [X] `ecr:InitiateLayerUpload`
      - [X] `ecr:UploadLayerPart`
      - [X] `ecr:CompleteLayerUpload`
      - [X] `ecr:PutImage`
    - [X] ECS permissions
      - [X] `ecs:DescribeServices`
      - [X] `ecs:DescribeTaskDefinition`
      - [X] `ecs:RegisterTaskDefinition`
      - [X] `ecs:UpdateService`
      - [X] `ecs:ListTasks`
      - [X] `ecs:DescribeTasks`
      - [X] `ecs:TagResource` (critical for task definition registration)
    - [X] IAM PassRole permissions
      - [X] `iam:PassRole` for task execution role
      - [X] `iam:PassRole` for task role
    - [X] CloudWatch Logs permissions
      - [X] `logs:CreateLogGroup`
      - [X] `logs:CreateLogStream`
      - [X] `logs:PutLogEvents`
    - [X] Secrets Manager permissions
      - [X] `secretsmanager:GetSecretValue`
      - [X] `secretsmanager:CreateSecret`
      - [X] `secretsmanager:UpdateSecret`
      - [X] `secretsmanager:ListSecrets` (requires resource: "*")
    - [X] Load Balancer permissions
      - [X] `elasticloadbalancing:DescribeTargetHealth`
      - [X] `elasticloadbalancing:DescribeTargetGroups`
  - [X] Configure necessary permissions
    - [X] Create IAM policies with specific actions
    - [X] Scope permissions to specific resources
  - [X] Follow principle of least privilege
    - [X] Review and remove unnecessary permissions
    - [X] Use condition keys to further restrict access
    - [X] Avoid using AdministratorAccess policy

### Monitoring and Logging

- [X] **CloudWatch Logs**

  - [X] Configure log groups
  - [X] Set up log retention policy (30 days)
  - [ ] Configure metric filters (if needed)
- [ ] **CloudWatch Alarms**

  - [ ] Set up CPU/memory utilization alarms
  - [ ] Configure service health alarms
  - [ ] Set up RDS performance alarms
  - [ ] Configure notification actions

### Secret Management

- [X] **Secrets Manager**
  - [X] Store database credentials
  - [X] Store API keys (OpenAI)
  - [ ] Configure rotation (if applicable)
  - [X] Set up access policies

## Optional Components

### DNS and SSL

- [X] **Route 53**

  - [X] Register or configure domain
  - [X] Create hosted zone
  - [X] Set up DNS records pointing to ALB
- [ ] **ACM Certificate** (Later Stage)

  - [ ] Request certificate
  - [ ] Validate domain ownership
  - [ ] Associate with ALB

### Content Delivery

- [ ] **S3 Bucket**

  - [ ] Create bucket for static assets
  - [ ] Configure bucket policies
  - [ ] Set up lifecycle rules
- [ ] **CloudFront** (Later Stage)

  - [ ] Create distribution
  - [ ] Configure origins
  - [ ] Set up cache behaviors
  - [ ] Configure SSL settings

### Scaling and Configuration

- [ ] **Auto Scaling** (Later Stage)

  - [ ] Define scaling policies
  - [ ] Set up scaling triggers
  - [ ] Configure minimum and maximum capacity
- [ ] **Parameter Store** (Later Stage)

  - [ ] Store non-sensitive configuration
  - [ ] Set up parameter hierarchies
  - [ ] Configure access policies

## Deployment Pipeline

- [X] **CI/CD Pipeline**
  - [X] Set up source code repository integration
  - [X] Configure build process
  - [ ] Set up testing stage
  - [X] Configure deployment stage
  - [ ] Set up approval gates (if needed)

### GitHub Actions Workflow

- [X] **GitHub Actions Configuration**
  - [X] Set up GitHub OIDC provider in AWS
  - [X] Configure GitHub Actions workflow file
    - [X] Checkout code
    - [X] Set up Python environment
    - [X] Install dependencies
    - [X] Configure AWS credentials with role assumption
    - [X] Ensure OpenAI API key exists in AWS Secrets Manager
    - [X] Login to Amazon ECR
    - [X] Build, tag, and push Docker image
    - [X] Fill in the new image ID in the ECS task definition
    - [X] Deploy ECS task definition
    - [X] Verify deployment status
  - [X] Configure GitHub repository secrets
    - [X] AWS region
    - [X] OpenAI API key

### IAM Policy Examples

- [X] **GitHub Actions ECS Policy**
  ```json
  {
    "Version": "2012-10-17",
    "Statement": [
      {
        "Effect": "Allow",
        "Action": [
          "ecr:GetAuthorizationToken",
          "ecr:BatchCheckLayerAvailability",
          "ecr:GetDownloadUrlForLayer",
          "ecr:BatchGetImage",
          "ecr:InitiateLayerUpload",
          "ecr:UploadLayerPart",
          "ecr:CompleteLayerUpload",
          "ecr:PutImage"
        ],
        "Resource": "*"
      },
      {
        "Effect": "Allow",
        "Action": [
          "ecs:DescribeServices",
          "ecs:DescribeTaskDefinition",
          "ecs:RegisterTaskDefinition",
          "ecs:UpdateService",
          "ecs:ListTasks",
          "ecs:DescribeTasks",
          "ecs:TagResource"
        ],
        "Resource": "*"
      },
      {
        "Effect": "Allow",
        "Action": [
          "iam:PassRole"
        ],
        "Resource": [
          "arn:aws:iam::************:role/dev-wos-ecs-task-execution-role",
          "arn:aws:iam::************:role/wos-dev-bedrock-role"
        ]
      },
      {
        "Effect": "Allow",
        "Action": [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ],
        "Resource": "arn:aws:logs:ap-southeast-1:************:log-group:/aws/ecs/wos-dev-thechatbot*:*"
      },
      {
        "Effect": "Allow",
        "Action": [
          "secretsmanager:GetSecretValue",
          "secretsmanager:CreateSecret",
          "secretsmanager:UpdateSecret"
        ],
        "Resource": [
          "arn:aws:secretsmanager:ap-southeast-1:************:secret:wos/dev/thechatbot/*",
          "arn:aws:secretsmanager:ap-southeast-1:************:secret:dev/wos/database/credentials-I8PSjK"
        ]
      },
      {
        "Effect": "Allow",
        "Action": [
          "secretsmanager:ListSecrets"
        ],
        "Resource": "*"
      },
      {
        "Effect": "Allow",
        "Action": [
          "elasticloadbalancing:DescribeTargetHealth",
          "elasticloadbalancing:DescribeTargetGroups"
        ],
        "Resource": "*"
      }
    ]
  }
  ```

- [X] **GitHub Actions Trust Relationship**
  ```json
  {
    "Version": "2012-10-17",
    "Statement": [
      {
        "Effect": "Allow",
        "Principal": {
          "Federated": "arn:aws:iam::************:oidc-provider/token.actions.githubusercontent.com"
        },
        "Action": "sts:AssumeRoleWithWebIdentity",
        "Condition": {
          "StringEquals": {
            "token.actions.githubusercontent.com:aud": "sts.amazonaws.com"
          },
          "StringLike": {
            "token.actions.githubusercontent.com:sub": [
              "repo:sembwaste/THECHATBOT:*"
            ]
          }
        }
      }
    ]
  }
  ```

## Post-Deployment

- [ ] **Validation**

  - [ ] Verify application functionality
  - [ ] Check logs for errors
  - [ ] Validate security configurations
  - [ ] Test failover scenarios
- [ ] **Documentation**

  - [ ] Update deployment documentation
  - [ ] Document infrastructure configuration
  - [ ] Create runbooks for common operations

## Common Issues and Troubleshooting

### IAM Permission Issues

- [X] **Missing ECS Permissions**
  - **Issue**: `User is not authorized to perform: ecs:TagResource on resource`
  - **Solution**: Add `ecs:TagResource` permission to the IAM policy for the GitHub Actions role
  - **Note**: This permission is required for registering task definitions with tags

- [X] **Secrets Manager Access**
  - **Issue**: `User is not authorized to perform: secretsmanager:ListSecrets because no identity-based policy allows the secretsmanager:ListSecrets action`
  - **Solution**: Add `secretsmanager:ListSecrets` permission with resource `*` to the IAM policy
  - **Note**: ListSecrets can only be granted at the account level, not for specific secrets

- [X] **IAM PassRole Issues**
  - **Issue**: `User is not authorized to perform: iam:PassRole on resource`
  - **Solution**: Add `iam:PassRole` permission for both task execution role and task role
  - **Note**: Specify exact ARNs of the roles that need to be passed

### ECS Service Issues

- [X] **Missing ECS Service**
  - **Issue**: `Error: arn:aws:ecs:ap-southeast-1:************:service/wos-dev-thechatbot-service is MISSING`
  - **Solution**: Create the ECS service using the service-definition.json file
  - **Command**: `aws ecs create-service --cli-input-json file://service-definition.json`

- [X] **Missing Target Group**
  - **Issue**: `An error occurred (ValidationError) when calling the DescribeTargetHealth operation: 'None' is not a valid target group ARN`
  - **Solution**: Create the target group or hardcode the target group ARN in the workflow
  - **Note**: Ensure the target group exists before deploying the ECS service

## Cost Optimization

- [ ] **Resource Sizing**

  - [ ] Right-size ECS tasks
  - [ ] Optimize RDS instance
  - [ ] Configure appropriate ALB capacity
- [ ] **Cost Monitoring**

  - [ ] Set up budgets
  - [ ] Configure cost alarms
  - [ ] Implement tagging strategy

---

*Note: This checklist should be adapted based on specific project requirements and organizational standards.*
