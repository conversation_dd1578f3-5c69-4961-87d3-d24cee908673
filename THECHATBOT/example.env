PROJECT_NAME=chatbot
CORS_ALLOW_ORIGINS=["*"]
SERVER_HOST=localhost
SERVER_PORT=8000

# DB
DB_DRIVER=postgresql+asyncpg
DB_USER=postgres
DB_PASSWORD=postgres
DB_HOST=localhost
DB_PORT=5432
DB_DB=postgres

# DB only for memory manager
DB_RW_USER=postgres
DB_RW_PASSWORD=postgres

OPENAI_API_KEY=

WOS_BACKEND_URL="http://localhost:3000"

# Credentials based on MFA
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_SESSION_TOKEN=
