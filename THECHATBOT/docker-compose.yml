version: '3.8'

services:
  # wos_db:
  #   image: postgres:11
  #   container_name: wos_db
  #   restart: unless-stopped
  #   env_file: .env
  #   environment:
  #     POSTGRES_USER: ${DB_USER}
  #     POSTGRES_PASSWORD: ${DB_PASSWORD}
  #     POSTGRES_DB: ${DB_NAME}
  #   ports:
  #     - "5432:5432"
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #   healthcheck:
  #     test: ["CMD-SHELL", "pg_isready -U user"]
  #     interval: 10s
  #     timeout: 5s
  #     retries: 5

  wos_chat:
    build:
      context: .
    container_name: wos_chatbot
    volumes:
      - ./app:/app/app
      - ./scripts:/app/scripts
    env_file: .env
    ports:
      - "8000:8000"

volumes:
  postgres_data:
