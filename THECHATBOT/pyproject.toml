[project]
name = "thechatbot"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "fastapi[all]>=0.115.12",
    "pydantic-settings>=2.8.1",
    "pydantic>=2.11.0",
    "sqlalchemy[asyncio]>=2.0.40",
    "pre-commit>=4.2.0",
    "langchain>=0.3.25",
    "langchain-community>=0.3.23",
    "langchain-openai>=0.3.16",
    "psycopg2>=2.9.10",
    "pytest>=8.3.5",
    "pytest-cov>=6.0.0",
    "langchain-aws>=0.2.22",
    "langchain-postgres>=0.0.13",
]
[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/app"]

[project.scripts]
dev = "app.main:main"

[tool.uv]
dev-dependencies = [
    "pre-commit>=3.8.0",
    "pytest-cov>=5.0.0",
    "pytest>=8.3.3",
    "ruff>=0.6.5",
]

[tool.pytest.ini_options]
addopts = "-v --cov=src --cov-report=term --cov-report=xml:reports/coverage.xml --junitxml=reports/junit.xml"
pythonpath = ["."]
testpaths = ["tests"]
asyncio_mode = "auto"
asyncio_default_fixture_loop_scope = "session"
junit_suite_name = "pytest"
junit_duration_report = "call"                                                                                # Junit will only report the time to run the tests, not the setup and teardown time


[tool.ruff]
# Same as Black.
line-length = 88
indent-width = 4

# these are the slightly modified ruff default settings
[tool.ruff.lint]
select = [
    "E",      # pycodestyle errors
    "W",      # pycodestyle warnings
    "F",      # pyflakes
    "I",      # isort
    "B",      # flake8-bugbear
    "C4",     # flake8-comprehensions
    "UP",     # pyupgrade
    "ARG001", # unused arguments in functions],
    "FAST",   # FastAPI
]
ignore = [
    "ARG001", # unused arguments in functions
]

# Allow fix for all enabled rules (when `--fix`) is provided.
fixable = ["ALL"]
unfixable = []

# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

[tool.ruff.format]
# Like Black, use double quotes for strings.
quote-style = "double"

# Like Black, indent with spaces, rather than tabs.
indent-style = "space"

# Like Black, respect magic trailing commas.
skip-magic-trailing-comma = false

# Like Black, automatically detect the appropriate line ending.
line-ending = "auto"

# Enable auto-formatting of code examples in docstrings. Markdown,
# reStructuredText code/literal blocks and doctests are all supported.
#
# This is currently disabled by default, but it is planned for this
# to be opt-out in the future.
docstring-code-format = true

# Set the line length limit used when formatting code snippets in
# docstrings.
#
# This only has an effect when the `docstring-code-format` setting is
# enabled.
docstring-code-line-length = "dynamic"


[tool.pyright]
venvPath = "."
venv = ".venv"
typeCheckingMode = "standard"
