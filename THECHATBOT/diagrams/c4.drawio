<mxfile host="Electron" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.7.8 Chrome/128.0.6613.36 Electron/32.0.1 Safari/537.36" version="24.7.8">
  <diagram name="Page-1" id="fw6IHvOjgR60bpQE8BwE">
    <mxGraphModel dx="1434" dy="907" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="qPCY_-Pio9ErPp3daxp3-18" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="140" y="200" width="620" height="320" as="geometry" />
        </mxCell>
        <mxCell id="qPCY_-Pio9ErPp3daxp3-21" value="System Context Architecture" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="360" y="170" width="180" height="30" as="geometry" />
        </mxCell>
        <mxCell id="qPCY_-Pio9ErPp3daxp3-22" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry x="210" y="260" width="500" height="218" as="geometry" />
        </mxCell>
        <mxCell id="qPCY_-Pio9ErPp3daxp3-1" value="Actor" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="qPCY_-Pio9ErPp3daxp3-22">
          <mxGeometry width="30" height="60" as="geometry" />
        </mxCell>
        <mxCell id="qPCY_-Pio9ErPp3daxp3-2" value="FE / BE" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="qPCY_-Pio9ErPp3daxp3-22">
          <mxGeometry x="150" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="qPCY_-Pio9ErPp3daxp3-3" value="ChatBot" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="qPCY_-Pio9ErPp3daxp3-22">
          <mxGeometry x="380" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="qPCY_-Pio9ErPp3daxp3-5" value="" style="sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;fillColor=#C925D1;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.database;" vertex="1" parent="qPCY_-Pio9ErPp3daxp3-22">
          <mxGeometry x="171" y="140" width="78" height="78" as="geometry" />
        </mxCell>
        <mxCell id="qPCY_-Pio9ErPp3daxp3-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="qPCY_-Pio9ErPp3daxp3-22">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="136" y="20" as="targetPoint" />
            <mxPoint x="46" y="20" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="qPCY_-Pio9ErPp3daxp3-8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="qPCY_-Pio9ErPp3daxp3-22">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="46" y="43" as="targetPoint" />
            <mxPoint x="130" y="43" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="qPCY_-Pio9ErPp3daxp3-9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="qPCY_-Pio9ErPp3daxp3-22">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="370" y="18" as="targetPoint" />
            <mxPoint x="280" y="18" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="qPCY_-Pio9ErPp3daxp3-10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="qPCY_-Pio9ErPp3daxp3-22">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="280" y="41" as="targetPoint" />
            <mxPoint x="364" y="41" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="qPCY_-Pio9ErPp3daxp3-14" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="qPCY_-Pio9ErPp3daxp3-22">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="195" y="139" as="targetPoint" />
            <mxPoint x="195" y="69" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="qPCY_-Pio9ErPp3daxp3-15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="qPCY_-Pio9ErPp3daxp3-22">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="225" y="66" as="targetPoint" />
            <mxPoint x="225" y="136" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="qPCY_-Pio9ErPp3daxp3-16" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="qPCY_-Pio9ErPp3daxp3-22">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="420" y="70" as="targetPoint" />
            <mxPoint x="260" y="171" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="qPCY_-Pio9ErPp3daxp3-17" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="qPCY_-Pio9ErPp3daxp3-22">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="260" y="200" as="targetPoint" />
            <mxPoint x="450" y="70" as="sourcePoint" />
            <Array as="points">
              <mxPoint x="450" y="70" />
              <mxPoint x="450" y="200" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="qPCY_-Pio9ErPp3daxp3-62" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry x="80" y="610" width="820" height="337.51" as="geometry" />
        </mxCell>
        <mxCell id="qPCY_-Pio9ErPp3daxp3-53" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="qPCY_-Pio9ErPp3daxp3-62">
          <mxGeometry width="820" height="337.51" as="geometry" />
        </mxCell>
        <mxCell id="qPCY_-Pio9ErPp3daxp3-52" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="qPCY_-Pio9ErPp3daxp3-62" source="qPCY_-Pio9ErPp3daxp3-25" target="qPCY_-Pio9ErPp3daxp3-24">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="qPCY_-Pio9ErPp3daxp3-25" value="ChatBot" style="rounded=1;whiteSpace=wrap;html=1;verticalAlign=top;" vertex="1" parent="qPCY_-Pio9ErPp3daxp3-62">
          <mxGeometry x="220" y="37.50999999999999" width="570" height="155" as="geometry" />
        </mxCell>
        <mxCell id="qPCY_-Pio9ErPp3daxp3-49" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="qPCY_-Pio9ErPp3daxp3-62" source="qPCY_-Pio9ErPp3daxp3-26" target="qPCY_-Pio9ErPp3daxp3-27">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="qPCY_-Pio9ErPp3daxp3-26" value="SQL Agent" style="rounded=1;whiteSpace=wrap;html=1;verticalAlign=top;" vertex="1" parent="qPCY_-Pio9ErPp3daxp3-62">
          <mxGeometry x="450" y="65.63" width="120" height="98.75" as="geometry" />
        </mxCell>
        <mxCell id="qPCY_-Pio9ErPp3daxp3-50" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="qPCY_-Pio9ErPp3daxp3-62" source="qPCY_-Pio9ErPp3daxp3-27" target="qPCY_-Pio9ErPp3daxp3-26">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="qPCY_-Pio9ErPp3daxp3-27" value="LLM Service" style="rounded=1;whiteSpace=wrap;html=1;verticalAlign=top;" vertex="1" parent="qPCY_-Pio9ErPp3daxp3-62">
          <mxGeometry x="650" y="65.63999999999999" width="120" height="98.75" as="geometry" />
        </mxCell>
        <mxCell id="qPCY_-Pio9ErPp3daxp3-47" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="qPCY_-Pio9ErPp3daxp3-62" source="qPCY_-Pio9ErPp3daxp3-28" target="qPCY_-Pio9ErPp3daxp3-26">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="qPCY_-Pio9ErPp3daxp3-28" value="GuardRails" style="rounded=1;whiteSpace=wrap;html=1;verticalAlign=top;" vertex="1" parent="qPCY_-Pio9ErPp3daxp3-62">
          <mxGeometry x="235" y="65.63" width="120" height="98.75" as="geometry" />
        </mxCell>
        <mxCell id="qPCY_-Pio9ErPp3daxp3-51" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="qPCY_-Pio9ErPp3daxp3-62" source="qPCY_-Pio9ErPp3daxp3-24" target="qPCY_-Pio9ErPp3daxp3-25">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="qPCY_-Pio9ErPp3daxp3-24" value="FE / BE" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="qPCY_-Pio9ErPp3daxp3-62">
          <mxGeometry x="20" y="85.00999999999999" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="qPCY_-Pio9ErPp3daxp3-32" value="" style="sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;fillColor=#01A88D;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.bedrock;" vertex="1" parent="qPCY_-Pio9ErPp3daxp3-62">
          <mxGeometry x="665" y="107.00999999999999" width="38" height="38" as="geometry" />
        </mxCell>
        <mxCell id="qPCY_-Pio9ErPp3daxp3-42" value="" style="shape=image;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/jpeg,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;" vertex="1" parent="qPCY_-Pio9ErPp3daxp3-62">
          <mxGeometry x="271" y="100.88999999999999" width="47.13" height="51.76" as="geometry" />
        </mxCell>
        <mxCell id="qPCY_-Pio9ErPp3daxp3-31" value="" style="shape=image;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/langchain-color.png;" vertex="1" parent="qPCY_-Pio9ErPp3daxp3-62">
          <mxGeometry x="480.5" y="93.64999999999999" width="59" height="59" as="geometry" />
        </mxCell>
        <mxCell id="qPCY_-Pio9ErPp3daxp3-34" value="" style="shape=image;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=https://static-00.iconduck.com/assets.00/openai-icon-2021x2048-4rpe5x7n.png;" vertex="1" parent="qPCY_-Pio9ErPp3daxp3-62">
          <mxGeometry x="717" y="107.50999999999999" width="38" height="38.51" as="geometry" />
        </mxCell>
        <mxCell id="qPCY_-Pio9ErPp3daxp3-56" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="qPCY_-Pio9ErPp3daxp3-62" source="qPCY_-Pio9ErPp3daxp3-54" target="qPCY_-Pio9ErPp3daxp3-24">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="qPCY_-Pio9ErPp3daxp3-60" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="qPCY_-Pio9ErPp3daxp3-62" source="qPCY_-Pio9ErPp3daxp3-54" target="qPCY_-Pio9ErPp3daxp3-25">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="qPCY_-Pio9ErPp3daxp3-54" value="" style="sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;fillColor=#C925D1;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.database;" vertex="1" parent="qPCY_-Pio9ErPp3daxp3-62">
          <mxGeometry x="41" y="227.51" width="78" height="78" as="geometry" />
        </mxCell>
        <mxCell id="qPCY_-Pio9ErPp3daxp3-55" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="qPCY_-Pio9ErPp3daxp3-62" source="qPCY_-Pio9ErPp3daxp3-24" target="qPCY_-Pio9ErPp3daxp3-54">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="qPCY_-Pio9ErPp3daxp3-61" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="qPCY_-Pio9ErPp3daxp3-62" source="qPCY_-Pio9ErPp3daxp3-25" target="qPCY_-Pio9ErPp3daxp3-54">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
