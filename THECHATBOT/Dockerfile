FROM python:3.12.7

WORKDIR /app

# Set Python to run unbuffered
ENV PYTHONUNBUFFERED=1

# Copy requirements and backend code
COPY pyproject.toml ./
COPY app/ ./app/
COPY scripts/ ./scripts/

# Install dependencies
RUN pip install uv \
    && uv venv --python 3.12 \
    && . .venv/bin/activate \
    && uv pip install -r pyproject.toml \
    && uv pip install asyncpg greenlet

# Make entrypoint script executable
RUN chmod +x ./scripts/start_app.sh

# Expose the port for ECS/ALB health checks
EXPOSE 8000

# Set the entrypoint
CMD ["sh", "/app/scripts/start_app.sh"]
