# THECHATBOT

# Introduction

This repo has codes for sembwaste dashboard. It is written in python and using FastAPI and Langchain.



# Setup

## Prerequisite

- Install docker and docker compose

## To run

- Make a copy of the `example.env` file and rename to `.env`.
- You need to get the AWS credentials from the MFA outputs
- Run `docker compose up -d` to run the chatbot application
- You can access the fastapi swagger at `localhost:8000/docs`
