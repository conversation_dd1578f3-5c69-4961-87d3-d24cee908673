# See https://pre-commit.com for more information
# See https://pre-commit.com/hooks.html for more hooks
default_language_version:
    python: python3
repos:
-   repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
    -   id: check-added-large-files
        args:
        - --maxkb=1000
    -   id: check-yaml
        args:
        -   --unsafe
    -   id: end-of-file-fixer
    -   id: trailing-whitespace
-   repo: https://github.com/charliermarsh/ruff-pre-commit
    rev: v0.11.2
    hooks:
    -   id: ruff
        args:
        - --fix
        - --ignore=E501
    -   id: ruff-format
