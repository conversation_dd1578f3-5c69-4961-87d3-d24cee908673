from unittest.mock import patch

import pytest

from app.constants import NEGATIVE_RESPONSE
from app.guard_rail.models import GuardRail
from app.guard_rail.service import SQL_PATTERN, _check_sql_input, guardrail_check


class TestCheckSqlInput:
    @pytest.mark.parametrize(
        "input_text,expected",
        [
            ("SELECT * FROM users", True),
            ("select id from users", True),
            ("INSERT INTO users VALUES (1, 'John')", True),
            ("UPDATE users SET name = 'John' WHERE id = 1", True),
            ("DELETE FROM users WHERE id = 1", True),
            ("DROP TABLE users", True),
            ("CREATE TABLE users (id INT)", True),
            ("ALTER TABLE users ADD COLUMN email VARCHAR(255)", True),
            ("SELECT users.name, users.email FROM users", True),
            ("SELECT * FROM users JOIN orders ON users.id = orders.user_id", True),
            ("SELECT id FROM users UNION SELECT id FROM customers", True),
            ("Normal text without SQL keywords", False),
            ("Just some regular user input", False),
            ("How are you doing today?", False),
            ("Tell me about the weather", False),
        ],
    )
    def test_check_sql_input(self, input_text, expected):
        """Test that SQL patterns are correctly identified."""
        result = _check_sql_input(input_text)
        assert result == expected

    def test_sql_pattern_compilation(self):
        """Test that the SQL_PATTERN regex is compiled correctly."""
        assert SQL_PATTERN is not None
        assert hasattr(SQL_PATTERN, "search")


class TestGuardrailCheck:
    def test_guardrail_check_with_sql_input(self):
        """Test guardrail_check with SQL-like input."""
        input_text = "SELECT * FROM users"
        result = guardrail_check(input_text)

        assert isinstance(result, GuardRail)
        assert result.is_sql_like is True
        assert result.response == NEGATIVE_RESPONSE

    def test_guardrail_check_with_normal_input(self):
        """Test guardrail_check with normal input."""
        input_text = "Tell me about the weather"
        result = guardrail_check(input_text)

        assert isinstance(result, GuardRail)
        assert result.is_sql_like is False
        assert result.response == input_text

    @patch("app.guard_rail.service._check_sql_input")
    def test_guardrail_check_calls_check_sql_input(self, mock_check_sql):
        """Test that guardrail_check calls _check_sql_input."""
        mock_check_sql.return_value = False
        input_text = "Some text"

        result = guardrail_check(input_text)

        mock_check_sql.assert_called_once_with(input_text)
        assert result.is_sql_like is False
        assert result.response == input_text

    @patch("app.guard_rail.service._check_sql_input")
    def test_guardrail_check_with_mocked_sql_detection(self, mock_check_sql):
        """Test guardrail_check with mocked SQL detection."""
        mock_check_sql.return_value = True
        input_text = "Some innocent looking text"

        result = guardrail_check(input_text)

        mock_check_sql.assert_called_once_with(input_text)
        assert result.is_sql_like is True
        assert result.response == NEGATIVE_RESPONSE
