from unittest.mock import MagicMock, patch

import pytest
from langchain_openai import ChatOpenAI

from app.toolkits.db import create_sql_db_toolkit, custom_table_info


@pytest.fixture
def mock_llm():
    """Fixture to provide a mock LLM instance."""
    return MagicMock(spec=ChatOpenAI)


@pytest.fixture
def mock_include_tables():
    """Fixture to provide mock include_tables."""
    return ["AnalyticsData", "Entity"]


@pytest.fixture
def mock_db_uri():
    """Fixture to provide a mock database URI."""
    return "postgresql://user:password@localhost:5432/test_db"


class TestCreateSqlDbToolkit:
    """Test cases for create_sql_db_toolkit function."""

    def test_custom_table_info_structure(self):
        """Test that custom_table_info has the expected structure and content."""
        # Check that custom_table_info contains expected tables
        assert "AnalyticsData" in custom_table_info
        assert "Entity" in custom_table_info

        # Check content of AnalyticsData description
        analytics_data_info = custom_table_info["AnalyticsData"]
        assert 'Table: "AnalyticsData"' in analytics_data_info
        assert "recyclable or non recyclable bags" in analytics_data_info
        assert "id (Primary Key, VARCHAR)" in analytics_data_info
        assert "entityId (Foreign Key → Entity.id)" in analytics_data_info

        # Check content of Entity description
        entity_info = custom_table_info["Entity"]
        assert 'Table: "Entity"' in entity_info
        assert 'Maps to "AnalyticsData.entityId" as Foreign Key' in entity_info
        assert "id (Primary Key, VARCHAR)" in entity_info
        assert "industry (VARCHAR)" in entity_info

    @patch("app.toolkits.db.SQLDatabase")
    @patch("app.toolkits.db.SQLDatabaseToolkit")
    def test_create_sql_db_toolkit_success(
        self,
        mock_sql_toolkit,
        mock_sql_database,
        mock_llm,
        mock_include_tables,
        mock_db_uri,
    ):
        """Test successful creation of SQLDatabaseToolkit."""
        # Setup mocks
        mock_db_instance = MagicMock()
        mock_sql_database.from_uri.return_value = mock_db_instance

        mock_toolkit_instance = MagicMock()
        mock_sql_toolkit.return_value = mock_toolkit_instance

        # Call function under test
        result = create_sql_db_toolkit(
            db_uri=mock_db_uri, include_tables=mock_include_tables, llm=mock_llm
        )

        # Assertions
        mock_sql_database.from_uri.assert_called_once_with(
            mock_db_uri,
            include_tables=mock_include_tables,
            custom_table_info=custom_table_info,
        )
        mock_sql_toolkit.assert_called_once_with(db=mock_db_instance, llm=mock_llm)
        assert result == mock_toolkit_instance

    @patch("app.toolkits.db.SQLDatabase")
    @patch("app.toolkits.db.SQLDatabaseToolkit")
    def test_create_sql_db_toolkit_with_empty_include_tables(
        self, mock_sql_toolkit, mock_sql_database, mock_llm, mock_db_uri
    ):
        """Test creation with empty include_tables list."""
        # Setup
        mock_db_instance = MagicMock()
        mock_sql_database.from_uri.return_value = mock_db_instance

        mock_toolkit_instance = MagicMock()
        mock_sql_toolkit.return_value = mock_toolkit_instance

        # Call function with empty include_tables
        result = create_sql_db_toolkit(
            db_uri=mock_db_uri, include_tables=[], llm=mock_llm
        )

        # Assertions
        mock_sql_database.from_uri.assert_called_once_with(
            mock_db_uri, include_tables=[], custom_table_info=custom_table_info
        )
        mock_sql_toolkit.assert_called_once_with(db=mock_db_instance, llm=mock_llm)
        assert result == mock_toolkit_instance

    @patch("app.toolkits.db.SQLDatabase")
    @patch("app.toolkits.db.SQLDatabaseToolkit")
    def test_create_sql_db_toolkit_with_none_include_tables(
        self, mock_sql_toolkit, mock_sql_database, mock_llm, mock_db_uri
    ):
        """Test creation with None include_tables."""
        # Setup
        mock_db_instance = MagicMock()
        mock_sql_database.from_uri.return_value = mock_db_instance

        mock_toolkit_instance = MagicMock()
        mock_sql_toolkit.return_value = mock_toolkit_instance

        # Instead of None, use an empty list since the function expects a list
        empty_list: list[str] = []

        # Call function with empty list instead of None
        result = create_sql_db_toolkit(
            db_uri=mock_db_uri, include_tables=empty_list, llm=mock_llm
        )

        # Assertions
        mock_sql_database.from_uri.assert_called_once_with(
            mock_db_uri, include_tables=[], custom_table_info=custom_table_info
        )
        mock_sql_toolkit.assert_called_once_with(db=mock_db_instance, llm=mock_llm)
        assert result == mock_toolkit_instance

    @patch("app.toolkits.db.SQLDatabase")
    @patch("app.toolkits.db.logger")
    def test_create_sql_db_toolkit_exception(
        self, mock_logger, mock_sql_database, mock_llm, mock_db_uri, mock_include_tables
    ):
        """Test exception handling when SQLDatabase creation fails."""
        # Setup the mock to raise an exception
        mock_sql_database.from_uri.side_effect = Exception("Connection failed")

        # Call function and expect exception
        with pytest.raises(Exception) as excinfo:
            create_sql_db_toolkit(
                db_uri=mock_db_uri, include_tables=mock_include_tables, llm=mock_llm
            )

        # Assertions
        assert "Connection failed" in str(excinfo.value)
        mock_logger.error.assert_called_once()
        assert "Error connecting to DB" in mock_logger.error.call_args[0][0]
