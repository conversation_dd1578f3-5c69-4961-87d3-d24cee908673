from unittest.mock import MagicMock, patch

import pytest
from langchain.callbacks.streaming_stdout import StreamingStdOutCallbackHandler

from app.toolkits.llm import get_bedrock_llm, get_openai_llm


class TestLLMToolkit:
    @patch("app.toolkits.llm.ChatOpenAI")
    def test_get_openai_llm_success(self, mock_chat_openai):
        # Setup
        mock_instance = MagicMock()
        mock_chat_openai.return_value = mock_instance

        # Execute
        result = get_openai_llm()

        # Assert
        # Get the callback args from the call
        call_args = mock_chat_openai.call_args[1]
        callbacks = call_args.get("callbacks", [])

        assert len(callbacks) == 1
        assert isinstance(callbacks[0], StreamingStdOutCallbackHandler)
        assert result == mock_instance

    @patch("app.toolkits.llm.ChatOpenAI")
    def test_get_openai_llm_exception(self, mock_chat_openai):
        # Setup
        mock_chat_openai.side_effect = Exception("API error")

        # Execute & Assert
        with pytest.raises(Exception) as excinfo:
            get_openai_llm()

        assert "API error" in str(excinfo.value)

    @patch("app.toolkits.llm.ChatBedrock")
    @patch("app.toolkits.llm.AWS_REGION", "test-region")
    def test_get_bedrock_llm_success(self, mock_chat_bedrock):
        # Setup
        mock_instance = MagicMock()
        mock_chat_bedrock.return_value = mock_instance

        # Execute
        result = get_bedrock_llm()

        assert result == mock_instance

    @patch("app.toolkits.llm.ChatBedrock")
    def test_get_bedrock_llm_exception(self, mock_chat_bedrock):
        # Setup
        mock_chat_bedrock.side_effect = Exception("AWS Bedrock error")

        # Execute & Assert
        with pytest.raises(Exception) as excinfo:
            get_bedrock_llm()

        assert "AWS Bedrock error" in str(excinfo.value)
