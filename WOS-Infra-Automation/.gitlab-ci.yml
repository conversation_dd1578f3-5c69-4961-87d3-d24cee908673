image:
  name: hashicorp/terraform:1.9.4
  entrypoint:
    - '/usr/bin/env'
    - 'PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'

stages:
  - validate
  - plan
  - approve-prod
  - apply
  - rollback-to-previous

variables:
  TERRAFORM_WORKING_DIR: "${CI_PROJECT_DIR}/aws/infrastructure"
  TERRAFORM_DEV_ENV: "dev"
  TERRAFORM_STG_ENV: "stg"
  TERRAFORM_PREPROD_ENV: "prerpod"
  TERRAFORM_PROD_ENV: "prod"
  GIT_DEPTH: "1"  # Shallow clone
  GIT_CLEAN_FLAGS: none  # Prevent git clean from removing cached files
  PLAN_CACHE_KEY: "plan-${CI_COMMIT_REF_SLUG}-${CI_COMMIT_SHA}"
  TF_PLUGIN_CACHE_DIR: ${CI_PROJECT_DIR}/.terraform.d/plugin-cache
  TF_CLI_ARGS_plan: "-parallelism=20"
  TERRAFORM_HTTP_RETRY: 5
  TERRAFORM_HTTP_RETRY_WAIT_MIN: 5
  # Optimize Terraform operations
  TF_IN_AUTOMATION: "true"
  TF_INPUT: "false"
  TF_CLI_ARGS: "-no-color"
  # Cache settings
  CACHE_FALLBACK_KEY: "default-${CI_DEFAULT_BRANCH}"
  DEPLOYMENT_HISTORY_PATH: "${CI_PROJECT_DIR}/deployment_history"
  LAST_GOOD_STATE_PATH: "${CI_PROJECT_DIR}/last_good_state"
  DEPLOYMENT_HISTORY_TABLE: "deployment-history"

# Global cache configuration
cache:
  key:
    files:
      - ${TERRAFORM_WORKING_DIR}/**/*.tf
      - ${TERRAFORM_WORKING_DIR}/**/*.tfvars
  paths:
    - ${TERRAFORM_WORKING_DIR}/.terraform/
    - ${TERRAFORM_WORKING_DIR}/.terraform.lock.hcl
    - ${TERRAFORM_WORKING_DIR}/terraform.tfstate.d/
    - ${TF_PLUGIN_CACHE_DIR}
    - ${DEPLOYMENT_HISTORY_PATH}/
    - ${LAST_GOOD_STATE_PATH}/
  policy: pull-push
  when: always


# Add this before the before_script
default:
  interruptible: true
  retry:
    max: 2
    when:
      - runner_system_failure
      - stuck_or_timeout_failure

before_script:
  - |
    # Create required directories with proper permissions
    mkdir -p ${TF_PLUGIN_CACHE_DIR}
    mkdir -p ${DEPLOYMENT_HISTORY_PATH}
    mkdir -p ${LAST_GOOD_STATE_PATH}

    # Ensure terraform working directory exists
    if [ ! -d "${TERRAFORM_WORKING_DIR}" ]; then
      echo "❌ Terraform working directory not found: ${TERRAFORM_WORKING_DIR}"
      echo "Current directory: $(pwd)"
      echo "Directory contents: $(ls -la)"
      exit 1
    fi

    # Change to terraform working directory with error handling
    cd "${TERRAFORM_WORKING_DIR}" || {
      echo "❌ Failed to change to terraform working directory"
      exit 1
    }

    # Set proper permissions
    chmod -R 755 ${DEPLOYMENT_HISTORY_PATH}
    chmod -R 755 ${LAST_GOOD_STATE_PATH}

    # Install dependencies in parallel with optimized Alpine package installation
    apk add --no-cache --update-cache \
      curl \
      aws-cli \
      jq \
      parallel \
      &
    wait

    # Optimize DNS resolution
    echo "nameserver 8.8.8.8" > /etc/resolv.conf
    echo "nameserver 1.1.1.1" >> /etc/resolv.conf

    # AWS credentials setup with retry logic
    for i in {1..3}; do
      if eval $(aws sts assume-role \
        --role-arn arn:aws:iam::${AWS_ACCOUNT}:role/cicd-role \
        --role-session-name cicd-role | \
        jq -r '.Credentials | "export AWS_ACCESS_KEY_ID=\(.AccessKeyId)\nexport AWS_SECRET_ACCESS_KEY=\(.SecretAccessKey)\nexport AWS_SESSION_TOKEN=\(.SessionToken)\n"'); then
        break
      fi
      echo "Retrying AWS credential fetch... ($i)"
      sleep 5
    done
  - |
    # Clean initialization
    rm -rf .terraform
    rm -f .terraform.lock.hcl

    # Basic initialization first
    terraform init \
      -input=false \
      -no-color \
      -backend=false

    # Then initialize with backend
    terraform init \
      -input=false \
      -no-color \
      -force-copy \
      -backend=true

# Rules templates
.rules:
  prod-approval:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: manual
      allow_failure: false
  common:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_OPEN_MERGE_REQUESTS
      when: never
    - if: $CI_COMMIT_BRANCH
  main-manual:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH || $CI_COMMIT_BRANCH == "ncm_stg"
      when: manual
  main-always:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH || $CI_COMMIT_BRANCH == "ncm_stg"
      when: always

# Template for plan jobs with optimizations
.terraform-plan:
  variables:
    FORCE_INIT: "false"
  cache:
    key: ${PLAN_CACHE_KEY}
    paths:
      - ${TERRAFORM_WORKING_DIR}/.terraform/
      - ${TERRAFORM_WORKING_DIR}/.terraform.lock.hcl
      - ${TERRAFORM_WORKING_DIR}/terraform.tfstate.d/
    policy: pull-push
  script:
    - export PLAN="$CI_PROJECT_DIR/$TERRAFORM_ENV.tfplan"
    - |
      # Workspace selection with error handling
      if ! terraform workspace select $TERRAFORM_ENV 2>/dev/null; then
        terraform workspace new $TERRAFORM_ENV
      fi
    - |
      echo "🔍 Planning changes for environment: $TERRAFORM_ENV"
      echo "=============================================="

      # Show current state with error handling
      echo "Current State Summary:"
      echo "---------------------"
      if terraform show -json > state.json 2>/dev/null; then
        # Simple resource count by type
        echo "Resources by Type:"
        jq -r '
          [.values.root_module.resources[] | .type] |
          group_by(.) |
          map({type: .[0], count: length}) |
          sort_by(.type) | .[] |
          "• \(.type): \(.count)"
        ' state.json || echo "No resources found"

        # Total resource count
        echo -e "\nTotal Resources:"
        jq -r '.values.root_module.resources | length' state.json || echo "0"

        # Save simplified state summary
        jq -r '
          {
            resources: [.values.root_module.resources[] | {
              type: .type,
              name: .name
            }],
            total_count: (.values.root_module.resources | length)
          }
        ' state.json > "$CI_PROJECT_DIR/${TERRAFORM_ENV}_state_summary.json" || echo "[]" > "$CI_PROJECT_DIR/${TERRAFORM_ENV}_state_summary.json"
      else
        echo "Unable to read state or no state exists"
        echo "[]" > "$CI_PROJECT_DIR/${TERRAFORM_ENV}_state_summary.json"
      fi

      # Run plan with optimizations
      PLAN_OUTPUT=$(terraform plan \
        -refresh=false \
        -no-color \
        -input=false \
        -var-file="env/$TERRAFORM_ENV/terraform.tfvars" \
        -parallelism=20 \
        -out=$PLAN)

      # Process and display results
      terraform show -json $PLAN > "$CI_PROJECT_DIR/${TERRAFORM_ENV}_plan.json"

      # Display changes with error handling
      echo -e "\nComponent Impact Summary:"
      echo "------------------------"
      if jq -e . "$CI_PROJECT_DIR/${TERRAFORM_ENV}_plan.json" >/dev/null 2>&1; then
        jq -r '
          (.resource_changes // []) |
          map(select(.change.actions[] | . != "no-op")) |
          map({
            component: (
              if .address | contains("kong") then "Kong"
              elif .address | contains("weaviate") then "Weaviate"
              elif .address | contains("memgraph") then "Memgraph"
              elif .address | contains("clamav") then "ClamAV"
              elif .address | contains("mwaa") then "MWAA"
              elif .address | contains("lambda") then "Lambda"
              else "Other"
              end
            ),
            action: .change.actions[0],
            address: .address
          }) | group_by(.component) | .[] |
          "🔄 \(.[0].component):\n  \(group_by(.action) | map("\(length) resources will be \(.[0].action)") | join("\n  "))"
        ' "$CI_PROJECT_DIR/${TERRAFORM_ENV}_plan.json" || echo "No changes detected"

        # Add detailed resource list with names
        echo -e "\nDetailed Resource Changes:"
        echo "-------------------------"
        jq -r '
          (.resource_changes // []) |
          map(select(.change.actions[] | . != "no-op")) |
          sort_by(.address) |
          .[] |
          "\(.change.actions[0] | ascii_upcase) → \(.address)"
        ' "$CI_PROJECT_DIR/${TERRAFORM_ENV}_plan.json" || echo "No detailed changes to display"
      else
        echo "Unable to parse plan output"
      fi
  artifacts:
    expire_in: 1 week
    reports:
      terraform: $CI_PROJECT_DIR/$TERRAFORM_ENV.tfplan
    paths:
      - $CI_PROJECT_DIR/$TERRAFORM_ENV.tfplan
      - $CI_PROJECT_DIR/${TERRAFORM_ENV}_plan.json
      - $CI_PROJECT_DIR/${TERRAFORM_ENV}_state_summary.json

# Template for validate job
.terraform-validate:
  script:
    - terraform validate -no-color

terraform-validate:
  stage: validate
  extends: .terraform-validate
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
      when: always
    - when: never

# Environment plan jobs - all manual after validate
terraform-plan-dev:
  stage: plan
  extends: .terraform-plan
  variables:
    TERRAFORM_ENV: dev
  needs:
    - job: terraform-validate
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
    - when: never

terraform-plan-stg:
  stage: plan
  extends: .terraform-plan
  variables:
    TERRAFORM_ENV: stg
  needs:
    - job: terraform-validate
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
      when: never
    - if: $CI_COMMIT_BRANCH == "ncm_stg"
      when: manual
    - if: $CI_COMMIT_BRANCH == "ncm_vapt"
      when: manual
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: manual

terraform-plan-preprod:
  stage: plan
  extends: .terraform-plan
  variables:
    TERRAFORM_ENV: preprod
  needs:
    - job: terraform-validate
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
      when: never
    - if: $CI_COMMIT_BRANCH == "ncm_preprod"
      when: manual
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: manual

terraform-plan-prod:
  stage: plan
  extends: .terraform-plan
  variables:
    TERRAFORM_ENV: prod
  needs:
    - job: terraform-validate
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
      when: never
    - if: $CI_COMMIT_BRANCH == "ncm_prod"
      when: manual
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: manual

# Add these jobs specifically for AMP IRSA deployment

terraform-plan-preprod-amp:
  stage: plan
  script:
    - export PLAN="$CI_PROJECT_DIR/preprod.tfplan"
    - terraform workspace select preprod || terraform workspace new preprod
    - terraform plan -target=module.eks_amp_irsa[0] -no-color -input=false -var-file="env/preprod/terraform.tfvars" -out=$PLAN
  artifacts:
    name: plan
    paths:
      - $CI_PROJECT_DIR/preprod.tfplan
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: manual

# Add template for tracking successful deployments
.track-successful-deployment:
  script: |
    TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    DEPLOY_DIR="${DEPLOYMENT_HISTORY_PATH}/${TERRAFORM_ENV}/${TIMESTAMP}"

    # Create deployment history directory
    mkdir -p "${DEPLOY_DIR}"

    # Save current state and plan
    terraform show -json > "${DEPLOY_DIR}/state.json"
    terraform show > "${DEPLOY_DIR}/state.txt"  # Human readable state

    # Save deployment metadata
    cat > "${DEPLOY_DIR}/metadata.json" <<EOF
    {
      "timestamp": "${TIMESTAMP}",
      "environment": "${TERRAFORM_ENV}",
      "commit_sha": "${CI_COMMIT_SHA}",
      "pipeline_id": "${CI_PIPELINE_ID}",
      "triggered_by": "${GITLAB_USER_NAME}",
      "branch": "${CI_COMMIT_BRANCH}"
    }
    EOF

    # Update last good state symlink
    rm -f "${LAST_GOOD_STATE_PATH}/${TERRAFORM_ENV}"
    ln -s "${DEPLOY_DIR}" "${LAST_GOOD_STATE_PATH}/${TERRAFORM_ENV}"

# Modify the .terraform-apply template to track successful deployments
.terraform-apply:
  script:
    - |
      # Load environment variables from tfvars file
      if [ ! -f "env/${TERRAFORM_ENV}/terraform.tfvars" ]; then
        echo "❌ Missing tfvars file: env/${TERRAFORM_ENV}/terraform.tfvars"
        exit 1
      fi

      # Select workspace
      terraform workspace select ${TERRAFORM_ENV} || terraform workspace new ${TERRAFORM_ENV}

      if terraform apply -auto-approve -input=false -var-file="env/${TERRAFORM_ENV}/terraform.tfvars" $PLAN; then
        echo "✅ Apply completed successfully, tracking deployment..."

        # Create deployment record in DynamoDB
        TIMESTAMP=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
        if aws dynamodb put-item \
          --table-name ${DEPLOYMENT_HISTORY_TABLE} \
          --item '{
            "service": {"S": "ncm_platform_automation"},
            "environment": {"S": "'${TERRAFORM_ENV}'"},
            "deployed_by": {"S": "'${GITLAB_USER_NAME}'"},
            "deployment_status": {"S": "SUCCESS"},
            "deployment_timestamp": {"S": "'${TIMESTAMP}'"},
            "last_successful_sha": {"S": "'${CI_COMMIT_SHA}'"},
            "pipeline_id": {"S": "'${CI_PIPELINE_ID}'"},
            "branch": {"S": "'${CI_COMMIT_BRANCH}'"},
            "state_file": {"S": "'$(terraform show -json | base64 -w 0)'"}
          }'; then
          echo "✅ Deployment tracked in DynamoDB"
          exit 0  # Explicitly exit with success
        else
          echo "⚠️ Warning: Failed to track deployment in DynamoDB, but apply was successful"
          exit 0  # Still exit with success since apply worked
        fi
      else
        echo "❌ Apply failed!"
        exit 1
      fi
  rules: !reference [.rules, main-manual]

# Apply stages
terraform-apply-dev:
  stage: apply
  extends: .terraform-apply
  variables:
    TERRAFORM_ENV: dev
  needs:
    - job: terraform-plan-dev
      artifacts: true
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
    - when: never

terraform-apply-stg:
  stage: apply
  extends: .terraform-apply
  variables:
    TERRAFORM_ENV: stg
  needs:
    - job: terraform-plan-stg
      artifacts: true
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
      when: never
    - if: $CI_COMMIT_BRANCH == "ncm_stg"
      when: manual
    - if: $CI_COMMIT_BRANCH == "ncm_vapt"
      when: manual
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: manual

terraform-apply-preprod:
  stage: apply
  extends: .terraform-apply
  variables:
    TERRAFORM_ENV: preprod
  needs:
    - job: terraform-plan-preprod
      artifacts: true
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
      when: never
    - if: $CI_COMMIT_BRANCH == "ncm_preprod"
      when: manual
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: manual

terraform-apply-prod:
  stage: apply
  extends: .terraform-apply
  variables:
    TERRAFORM_ENV: prod
  needs:
    - job: terraform-plan-prod
      artifacts: true
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
      when: never
    - if: $CI_COMMIT_BRANCH == "ncm_prod"
      when: manual
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: manual

terraform-apply-preprod-amp:
  stage: apply
  script:
    - export PLAN="$CI_PROJECT_DIR/preprod.tfplan"
    - terraform workspace select preprod || terraform workspace new preprod
    - terraform apply -target=module.eks_amp_irsa[0] -auto-approve -input=false $PLAN
  needs:
    - job: terraform-plan-preprod-amp
      artifacts: true
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: manual

approve-production:
  stage: approve-prod
  script:
    - echo "Production deployment approved"
  needs:
    - job: terraform-plan-prod
    - optional: true
      job: terraform-plan-dev
    - optional: true
      job: terraform-plan-stg
    - optional: true
      job: terraform-plan-preprod
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
      when: never
    - if: $CI_COMMIT_BRANCH == "ncm_prod"
      when: manual
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: manual
      allow_failure: false

# Add cleanup job
cleanup:
  stage: .post
  script:
    - |
      # Clean up old plan files
      find ${CI_PROJECT_DIR} -name "*.tfplan" -mtime +7 -delete
      find ${CI_PROJECT_DIR} -name "*_plan.json" -mtime +7 -delete
      find ${CI_PROJECT_DIR} -name "*_state_summary.json" -mtime +7 -delete

      # Clean up terraform plugin cache
      find ${TF_PLUGIN_CACHE_DIR} -type f -atime +30 -delete
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: always
  allow_failure: true

# Add rollback jobs for each environment
.rollback-to-previous:
  stage: rollback-to-previous
  script:
    - |
      # Select workspace
      terraform workspace select ${TERRAFORM_ENV} || terraform workspace new ${TERRAFORM_ENV}

      # Load environment variables
      if [ ! -f "env/${TERRAFORM_ENV}/terraform.tfvars" ]; then
        echo "❌ Missing tfvars file: env/${TERRAFORM_ENV}/terraform.tfvars"
        exit 1
      fi

      echo "🔍 Finding last successful deployment..."

      # Get last successful deployment from DynamoDB
      LAST_DEPLOYMENT=$(aws dynamodb query \
        --table-name ${DEPLOYMENT_HISTORY_TABLE} \
        --key-condition-expression "service = :service AND environment = :env" \
        --expression-attribute-values '{
          ":service": {"S": "ncm_platform_automation"},
          ":env": {"S": "'${TERRAFORM_ENV}'"
        }' \
        --filter-expression "deployment_status = :status" \
        --expression-attribute-values '{
          ":status": {"S": "SUCCESS"}
        }' \
        --limit 1 \
        --scan-index-forward false)

      if [ -z "$LAST_DEPLOYMENT" ]; then
        echo "❌ No previous successful deployment found for ${TERRAFORM_ENV}"
        exit 1
      fi

      # Extract deployment details
      DEPLOY_SHA=$(echo $LAST_DEPLOYMENT | jq -r '.Items[0].last_successful_sha.S')
      DEPLOY_TIME=$(echo $LAST_DEPLOYMENT | jq -r '.Items[0].deployment_timestamp.S')
      DEPLOY_BY=$(echo $LAST_DEPLOYMENT | jq -r '.Items[0].deployed_by.S')

      echo "📋 Previous successful deployment:"
      echo "Commit: ${DEPLOY_SHA}"
      echo "Time: ${DEPLOY_TIME}"
      echo "By: ${DEPLOY_BY}"

      # Get state file from DynamoDB and decode
      STATE_FILE=$(echo $LAST_DEPLOYMENT | jq -r '.Items[0].state_file.S' | base64 -d)
      echo "$STATE_FILE" > previous_state.json

      echo "🔍 Analyzing changes for rollback..."
      # Create rollback plan with variables
      terraform plan -refresh=true \
        -state=previous_state.json \
        -var-file="env/${TERRAFORM_ENV}/terraform.tfvars" \
        -out="${CI_PROJECT_DIR}/${TERRAFORM_ENV}_rollback.tfplan"

      # Show what will change
      echo "📝 Changes that will be made:"
      terraform show -json "${CI_PROJECT_DIR}/${TERRAFORM_ENV}_rollback.tfplan" | \
        jq -r '.resource_changes[] | select(.change.actions[0] != "no-op") |
        "• \(.address) will be \(.change.actions[0])"'

      echo "⚠️ Are you sure you want to rollback to commit ${DEPLOY_SHA}? (Type 'yes' to continue)"
      read confirmation
      if [ "$confirmation" = "yes" ]; then
        echo "🔄 Applying rollback..."
        terraform apply -auto-approve "${CI_PROJECT_DIR}/${TERRAFORM_ENV}_rollback.tfplan"

        # Record rollback in DynamoDB
        ROLLBACK_TIME=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
        aws dynamodb put-item \
          --table-name ${DEPLOYMENT_HISTORY_TABLE} \
          --item '{
            "service": {"S": "ncm_platform_automation"},
            "environment": {"S": "'${TERRAFORM_ENV}'"},
            "deployed_by": {"S": "'${GITLAB_USER_NAME}'"},
            "deployment_status": {"S": "ROLLBACK"},
            "deployment_timestamp": {"S": "'${ROLLBACK_TIME}'"},
            "last_successful_sha": {"S": "'${DEPLOY_SHA}'"},
            "pipeline_id": {"S": "'${CI_PIPELINE_ID}'"},
            "branch": {"S": "'${CI_COMMIT_BRANCH}'"},
            "rollback_from_sha": {"S": "'${CI_COMMIT_SHA}'"}
          }'

        echo "✅ Rollback completed and tracked in DynamoDB"
      else
        echo "❌ Rollback cancelled"
        exit 1
      fi

rollback-dev:
  extends: .rollback-to-previous
  variables:
    TERRAFORM_ENV: dev
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
    - when: never

rollback-stg:
  extends: .rollback-to-previous
  variables:
    TERRAFORM_ENV: stg
  rules:
    - if: $CI_COMMIT_BRANCH == "ncm_stg"
      when: manual

rollback-preprod:
  extends: .rollback-to-previous
  variables:
    TERRAFORM_ENV: preprod
  rules:
    - if: $CI_COMMIT_BRANCH == "ncm_preprod"
      when: manual

rollback-prod:
  extends: .rollback-to-previous
  variables:
    TERRAFORM_ENV: prod
  rules:
    - if: $CI_COMMIT_BRANCH == "ncm_prod"
      when: manual

