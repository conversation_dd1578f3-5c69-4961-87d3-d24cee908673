# WasteOS Terraform Infrastructure

This repository contains the Terraform code for deploying the WasteOS platform infrastructure on AWS.

## Repository Structure

```
wos-terraform/
├── modules/             # Reusable Terraform modules
│   ├── compute/         # Compute resources (ECS, EC2, etc.)
│   ├── database/        # Database resources (RDS, ElastiCache, etc.)
│   ├── networking/      # Networking resources (VPC, subnets, etc.)
│   └── storage/         # Storage resources (S3, EFS, etc.)
├── stacks/              # Composition of modules for specific purposes
│   ├── compute/         # Compute stacks
│   ├── database/        # Database stacks
│   ├── networking/      # Networking stacks
│   └── storage/         # Storage stacks
├── environments/        # Environment-specific configurations
│   ├── dev/             # Development environment
│   ├── staging/         # Staging environment
│   └── prod/            # Production environment
└── .github/             # GitHub Actions workflows
    └── workflows/       # CI/CD pipeline definitions
```

## Prerequisites

- AWS Account
- Terraform >= 1.0.0
- AWS CLI configured with appropriate credentials
- GitHub repository with Actions enabled

## GitHub Actions Workflow

This repository includes a GitHub Actions workflow for automated Terraform deployments. The workflow is defined in `.github/workflows/terraform-deploy.yml` and includes the following jobs:

1. **Terraform Validate**: Validates the Terraform code, checks formatting, and runs TFLint.
2. **Terraform Plan**: Generates a Terraform plan for review.
3. **Terraform Apply**: Applies the Terraform plan to deploy the infrastructure.
4. **Terraform Destroy**: Destroys the infrastructure (only triggered manually).

### Workflow Triggers

The workflow is triggered by:

- **Push** to the `main` branch or any `feature/*` branch
- **Pull Request** to the `main` branch
- **Manual Trigger** via GitHub Actions UI

### Manual Workflow Execution

You can manually trigger the workflow from the GitHub Actions UI:

1. Go to the "Actions" tab in your GitHub repository
2. Select the "Terraform Deploy" workflow
3. Click "Run workflow"
4. Select the environment (dev, staging, prod)
5. Select the action (plan, apply, destroy)
6. Click "Run workflow"

### Environment Protection

Production deployments require approval before they can be applied. This is configured using GitHub Environments.

## AWS IAM Role for GitHub Actions

The GitHub Actions workflow assumes an AWS IAM role to perform Terraform operations. You need to create this role in your AWS account:

```bash
# Create the IAM role for GitHub Actions
aws iam create-role \
  --role-name github-actions-terraform-role \
  --assume-role-policy-document '{
    "Version": "2012-10-17",
    "Statement": [
      {
        "Effect": "Allow",
        "Principal": {
          "Federated": "arn:aws:iam::************:oidc-provider/token.actions.githubusercontent.com"
        },
        "Action": "sts:AssumeRoleWithWebIdentity",
        "Condition": {
          "StringEquals": {
            "token.actions.githubusercontent.com:aud": "sts.amazonaws.com",
            "token.actions.githubusercontent.com:sub": "repo:sembwaste/WOS-Infra-Automation:ref:refs/heads/main"
          }
        }
      }
    ]
  }'

# Attach the necessary policies to the role
aws iam attach-role-policy \
  --role-name github-actions-terraform-role \
  --policy-arn arn:aws:iam::aws:policy/AdministratorAccess
```

## Local Development

### Setup

1. Clone the repository:

   ```bash
   <NAME_EMAIL>:sembwaste/WOS-Infra-Automation.git
   cd WOS-Infra-Automation
   ```
2. Initialize Terraform:

   ```bash
   cd environments/dev
   terraform init
   ```
3. Create a plan:

   ```bash
   terraform plan -out=tfplan
   ```
4. Apply the plan:

   ```bash
   terraform apply tfplan
   ```

### Adding New Resources

1. Create a new module in the `modules/` directory if needed
2. Create a new stack in the `stacks/` directory if needed
3. Reference the stack in the appropriate environment configuration
4. Commit and push your changes
5. The GitHub Actions workflow will validate, plan, and apply your changes

## Contributing

1. Create a feature branch from `main`
2. Make your changes
3. Create a pull request to `main`
4. The GitHub Actions workflow will validate and plan your changes
5. After review and approval, merge your pull request
6. The GitHub Actions workflow will automatically apply the changes to the dev environment

## License

This project is licensed under the MIT License - see the LICENSE file for details.


aws sts get-caller-identity

ssh -i "wos-dev-bastion-key.pem" <EMAIL>

wos_dev_admin

NmMidf:wJHh[Tx?V

psql -h wos-dev-postgres.cl8g000io4f1.ap-southeast-1.rds.amazonaws.com -U wos_dev_admin -d wos_dev -W


ssh-i /Users/<USER>/Documents/Projects/SembaWaste/wos-terraform/bootstrap/wos-dev-bastion-key.pemec2-user@13.212.175.165

aws secretsmanager get-secret-value--secret-id dev/wos/database/credentials --query SecretString --output text | jq -r '.password'

psql -h wos-dev-postgres.cl8g000io4f1.ap-southeast-1.rds.amazonaws.com -U wos_dev_admin -d wos_dev
