/**
 * # AWS DynamoDB Table Module
 *
 * This module creates a DynamoDB table with configurable attributes, key schema, and billing mode.
 */

resource "aws_dynamodb_table" "this" {
  name         = var.table_name
  billing_mode = var.billing_mode
  hash_key     = var.hash_key
  range_key    = var.range_key

  # Create attribute definitions
  dynamic "attribute" {
    for_each = var.attributes
    content {
      name = attribute.value.name
      type = attribute.value.type
    }
  }

  # Set read/write capacity if provisioned billing mode
  read_capacity  = var.billing_mode == "PROVISIONED" ? var.read_capacity : null
  write_capacity = var.billing_mode == "PROVISIONED" ? var.write_capacity : null

  # Enable point-in-time recovery if specified
  point_in_time_recovery {
    enabled = var.enable_point_in_time_recovery
  }

  # Enable server-side encryption if specified
  server_side_encryption {
    enabled = var.enable_server_side_encryption
  }

  # Apply tags
  tags = merge(
    var.tags,
    {
      Name = var.table_name
    }
  )

  # Prevent destroy of the table to protect state locks
  lifecycle {
    prevent_destroy = true
  }
}
