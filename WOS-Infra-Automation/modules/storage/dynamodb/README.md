# AWS DynamoDB Table Module

This module creates a DynamoDB table with configurable attributes, key schema, and billing mode.

## Features

- Creates a DynamoDB table with a specified name
- Configures hash key and optional range key
- Supports both PAY_PER_REQUEST and PROVISIONED billing modes
- Enables point-in-time recovery by default
- Enables server-side encryption by default
- Applies tags to the table

## Usage

### State Locking Table Example

```hcl
module "terraform_lock_table" {
  source = "../../modules/storage/dynamodb"

  table_name = "terraform-statefile-locks-wos"
  hash_key   = "LockID"
  
  attributes = [
    {
      name = "LockID"
      type = "S"
    }
  ]
  
  tags = {
    Project     = "WasteOS"
    Environment = "Management"
    ManagedBy   = "Terraform"
  }
}
```

### Deployment History Table Example

```hcl
module "deployment_history_table" {
  source = "../../modules/storage/dynamodb"

  table_name = "wos-deployment-history"
  hash_key   = "Environment"
  range_key  = "Timestamp"
  
  attributes = [
    {
      name = "Environment"
      type = "S"
    },
    {
      name = "Timestamp"
      type = "S"
    }
  ]
  
  tags = {
    Project     = "WasteOS"
    Environment = "Management"
    ManagedBy   = "Terraform"
  }
}
```

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| table_name | Name of the DynamoDB table | `string` | n/a | yes |
| billing_mode | Controls how you are charged for read and write throughput. Valid values are PROVISIONED or PAY_PER_REQUEST | `string` | `"PAY_PER_REQUEST"` | no |
| hash_key | The attribute to use as the hash (partition) key | `string` | n/a | yes |
| range_key | The attribute to use as the range (sort) key | `string` | `null` | no |
| attributes | List of attribute definitions. Only required for hash_key and range_key attributes | `list(object({ name = string, type = string }))` | n/a | yes |
| read_capacity | The number of read units for the table. If the billing_mode is PROVISIONED, this field is required | `number` | `null` | no |
| write_capacity | The number of write units for the table. If the billing_mode is PROVISIONED, this field is required | `number` | `null` | no |
| enable_point_in_time_recovery | Whether to enable point-in-time recovery | `bool` | `true` | no |
| enable_server_side_encryption | Whether to enable server-side encryption | `bool` | `true` | no |
| tags | A map of tags to add to the table | `map(string)` | `{}` | no |
| prevent_destroy | Prevent destruction of the table | `bool` | `true` | no |

## Outputs

| Name | Description |
|------|-------------|
| table_id | The name of the table |
| table_arn | The ARN of the table |
| table_stream_arn | The ARN of the table stream if enabled |
| table_stream_label | The timestamp of the table stream if enabled |

## Notes

- The table has point-in-time recovery enabled by default to protect against accidental data loss
- Server-side encryption is enabled by default for security
- A lifecycle rule prevents the table from being destroyed by default to protect state locks
