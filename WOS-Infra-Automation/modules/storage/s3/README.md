# AWS S3 Bucket Module

This module creates an S3 bucket with versioning, encryption, and public access blocking. It also applies a bucket policy and tags.

## Features

- Creates an S3 bucket with a specified name
- Enables versioning for the bucket
- Configures server-side encryption with AES256
- Blocks all public access to the bucket
- Applies a bucket policy if provided
- Applies tags to the bucket

## Usage

```hcl
module "terraform_state_bucket" {
  source = "../../modules/storage/s3"

  bucket_name = "wos-terraform-state-bucket-123456789012"
  
  tags = {
    Project     = "WasteOS"
    Environment = "Management"
    ManagedBy   = "Terraform"
  }
  
  bucket_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid       = "DenyInsecureTransport"
        Effect    = "Deny"
        Principal = "*"
        Action    = "s3:*"
        Resource  = [
          "arn:aws:s3:::wos-terraform-state-bucket-123456789012",
          "arn:aws:s3:::wos-terraform-state-bucket-123456789012/*"
        ]
        Condition = {
          Bool = {
            "aws:SecureTransport" = "false"
          }
        }
      }
    ]
  })
}
```

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| bucket_name | Name of the S3 bucket | `string` | n/a | yes |
| tags | A map of tags to add to the bucket | `map(string)` | `{}` | no |
| bucket_policy | Bucket policy JSON document | `string` | `null` | no |
| prevent_destroy | Prevent destruction of the bucket | `bool` | `true` | no |

## Outputs

| Name | Description |
|------|-------------|
| bucket_id | The name of the bucket |
| bucket_arn | The ARN of the bucket |
| bucket_domain_name | The domain name of the bucket |
| bucket_regional_domain_name | The regional domain name of the bucket |

## Notes

- The bucket has versioning enabled by default to protect against accidental deletion of objects
- Server-side encryption is enabled by default using AES256
- All public access is blocked by default
- A lifecycle rule prevents the bucket from being destroyed by default to protect state files
