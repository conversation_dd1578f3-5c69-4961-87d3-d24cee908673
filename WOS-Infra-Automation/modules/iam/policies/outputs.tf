# IAM Policies Module - outputs.tf
# Outputs from the IAM policies module

output "ecr_access_policy_arn" {
  description = "ARN of the ECR access policy"
  value       = var.create_ecr_policy ? aws_iam_policy.ecr_access_policy[0].arn : null
}

output "ecr_push_policy_arn" {
  description = "ARN of the ECR push policy"
  value       = var.create_ecr_push_policy ? aws_iam_policy.ecr_push_policy[0].arn : null
}

output "secrets_manager_policy_arn" {
  description = "ARN of the Secrets Manager access policy"
  value       = var.create_secrets_manager_policy ? aws_iam_policy.secrets_manager_policy[0].arn : null
}

output "ecs_task_execution_policy_arn" {
  description = "ARN of the combined ECS task execution policy"
  value       = var.create_ecs_task_execution_policy ? aws_iam_policy.ecs_task_execution_policy[0].arn : null
}
