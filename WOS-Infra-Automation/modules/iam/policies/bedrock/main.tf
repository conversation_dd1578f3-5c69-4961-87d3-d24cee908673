# Bedrock IAM Policy Module - main.tf
# This module creates an IAM policy for Amazon Bedrock access

resource "aws_iam_policy" "bedrock_access" {
  name        = var.policy_name
  description = var.policy_description
  path        = var.policy_path

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect   = "Allow",
        Action   = var.bedrock_actions,
        Resource = var.bedrock_model_arns
      }
    ]
  })

  tags = merge(
    var.tags,
    {
      Name = var.policy_name
    }
  )
}
