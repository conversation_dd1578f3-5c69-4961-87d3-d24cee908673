# Bedrock IAM Policy Module - outputs.tf
# Outputs for the Bedrock IAM policy module

output "policy_arn" {
  description = "ARN of the IAM policy for Bedrock access"
  value       = aws_iam_policy.bedrock_access.arn
}

output "policy_id" {
  description = "ID of the IAM policy for Bedrock access"
  value       = aws_iam_policy.bedrock_access.id
}

output "policy_name" {
  description = "Name of the IAM policy for Bedrock access"
  value       = aws_iam_policy.bedrock_access.name
}
