# Bedrock IAM Policy Module - variables.tf
# Variables for the Bedrock IAM policy module

variable "policy_name" {
  description = "Name of the IAM policy for Bedrock access"
  type        = string
  default     = "BedrockModelAccess"
}

variable "policy_description" {
  description = "Description of the IAM policy for Bedrock access"
  type        = string
  default     = "Allows access to Amazon Bedrock models"
}

variable "policy_path" {
  description = "Path of the IAM policy"
  type        = string
  default     = "/"
}

variable "bedrock_actions" {
  description = "List of Bedrock API actions to allow"
  type        = list(string)
  default     = ["bedrock:InvokeModel"]
}

variable "bedrock_model_arns" {
  description = "List of Bedrock model ARNs to allow access to"
  type        = list(string)
}

variable "tags" {
  description = "A map of tags to add to all resources"
  type        = map(string)
  default     = {}
}
