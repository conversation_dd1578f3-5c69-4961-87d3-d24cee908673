# IAM Policies Module - main.tf
# This module creates IAM policies for ECR and Secrets Manager access

# ECR Access Policy
resource "aws_iam_policy" "ecr_access_policy" {
  count = var.create_ecr_policy ? 1 : 0

  name        = "${var.name_prefix}-ecr-access-policy"
  description = "Policy for ECR access in the ${var.name_prefix} environment"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "ecr:GetAuthorizationToken",
          "ecr:BatchCheckLayerAvailability",
          "ecr:GetDownloadUrlForLayer",
          "ecr:BatchGetImage",
          "ecr:DescribeRepositories",
          "ecr:ListImages",
          "ecr:DescribeImages"
        ]
        Effect   = "Allow"
        Resource = var.ecr_repository_arns
      },
      {
        Action   = ["ecr:GetAuthorizationToken"]
        Effect   = "Allow"
        Resource = ["*"]
      }
    ]
  })

  tags = merge(
    var.tags,
    {
      Name = "${var.name_prefix}-ecr-access-policy"
    }
  )
}

# ECR Push Access Policy (for CI/CD pipelines)
resource "aws_iam_policy" "ecr_push_policy" {
  count = var.create_ecr_push_policy ? 1 : 0

  name        = "${var.name_prefix}-ecr-push-policy"
  description = "Policy for pushing images to ECR in the ${var.name_prefix} environment"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "ecr:GetAuthorizationToken",
          "ecr:BatchCheckLayerAvailability",
          "ecr:GetDownloadUrlForLayer",
          "ecr:BatchGetImage",
          "ecr:InitiateLayerUpload",
          "ecr:UploadLayerPart",
          "ecr:CompleteLayerUpload",
          "ecr:PutImage"
        ]
        Effect   = "Allow"
        Resource = var.ecr_repository_arns
      },
      {
        Action   = ["ecr:GetAuthorizationToken"]
        Effect   = "Allow"
        Resource = ["*"]
      }
    ]
  })

  tags = merge(
    var.tags,
    {
      Name = "${var.name_prefix}-ecr-push-policy"
    }
  )
}

# Secrets Manager Access Policy
resource "aws_iam_policy" "secrets_manager_policy" {
  count = var.create_secrets_manager_policy ? 1 : 0

  name        = "${var.name_prefix}-secrets-manager-policy"
  description = "Policy for Secrets Manager access in the ${var.name_prefix} environment"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action   = ["secretsmanager:GetSecretValue"]
        Effect   = "Allow"
        Resource = var.secret_arns
      }
    ]
  })

  tags = merge(
    var.tags,
    {
      Name = "${var.name_prefix}-secrets-manager-policy"
    }
  )
}

# Combined ECS Task Execution Policy
resource "aws_iam_policy" "ecs_task_execution_policy" {
  count = var.create_ecs_task_execution_policy ? 1 : 0

  name        = "${var.name_prefix}-ecs-task-execution-policy"
  description = "Combined policy for ECS task execution in the ${var.name_prefix} environment"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = concat(
      # ECR Access
      [
        {
          Action = [
            "ecr:GetAuthorizationToken",
            "ecr:BatchCheckLayerAvailability",
            "ecr:GetDownloadUrlForLayer",
            "ecr:BatchGetImage",
            "ecr:DescribeRepositories",
            "ecr:ListImages",
            "ecr:DescribeImages"
          ]
          Effect   = "Allow"
          Resource = var.ecr_repository_arns
        },
        {
          Action   = ["ecr:GetAuthorizationToken"]
          Effect   = "Allow"
          Resource = ["*"]
        }
      ],
      # Secrets Manager Access (only if not restricted by SCP)
      var.scp_restrictions ? [] : [
        {
          Action   = ["secretsmanager:GetSecretValue"]
          Effect   = "Allow"
          Resource = var.secret_arns
        }
      ],
      # CloudWatch Logs Access
      [
        {
          Action = [
            "logs:CreateLogGroup",
            "logs:CreateLogStream",
            "logs:PutLogEvents",
            "logs:DescribeLogStreams",
            "logs:DescribeLogGroups"
          ]
          Effect   = "Allow"
          Resource = ["arn:aws:logs:*:*:*"]
        }
      ],
      # SSM Access for ECS Exec
      [
        {
          Action = [
            "ssm:GetParameters",
            "ssmmessages:CreateControlChannel",
            "ssmmessages:CreateDataChannel",
            "ssmmessages:OpenControlChannel",
            "ssmmessages:OpenDataChannel"
          ]
          Effect   = "Allow"
          Resource = ["*"]
        }
      ],
      # ECS Exec
      [
        {
          Action = [
            "ecs:ExecuteCommand"
          ]
          Effect   = "Allow"
          Resource = ["*"]
        }
      ]
    )
  })

  tags = merge(
    var.tags,
    {
      Name = "${var.name_prefix}-ecs-task-execution-policy"
    }
  )
}
