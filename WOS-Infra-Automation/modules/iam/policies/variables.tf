# IAM Policies Module - variables.tf
# Variables for the IAM policies module

variable "name_prefix" {
  description = "Prefix to use for resource names (usually environment name)"
  type        = string
}

variable "create_ecr_policy" {
  description = "Whether to create the ECR access policy"
  type        = bool
  default     = false
}

variable "create_ecr_push_policy" {
  description = "Whether to create the ECR push policy"
  type        = bool
  default     = false
}

variable "create_secrets_manager_policy" {
  description = "Whether to create the Secrets Manager access policy"
  type        = bool
  default     = false
}

variable "create_ecs_task_execution_policy" {
  description = "Whether to create the combined ECS task execution policy"
  type        = bool
  default     = false
}

variable "ecr_repository_arns" {
  description = "List of ECR repository ARNs to grant access to"
  type        = list(string)
  default     = []
}

variable "secret_arns" {
  description = "List of Secrets Manager secret ARNs to grant access to"
  type        = list(string)
  default     = []
}

variable "tags" {
  description = "Tags to apply to all resources"
  type        = map(string)
  default     = {}
}

variable "scp_restrictions" {
  description = "Whether there are SCP restrictions that prevent access to certain AWS services"
  type        = bool
  default     = false
}
