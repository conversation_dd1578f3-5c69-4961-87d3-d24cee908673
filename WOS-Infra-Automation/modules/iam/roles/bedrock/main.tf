# Bedrock IAM Role Module - main.tf
# This module creates an IAM role for Amazon Bedrock access

# IAM Role
resource "aws_iam_role" "bedrock_role" {
  name        = var.role_name
  description = var.role_description
  path        = var.role_path

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Principal = {
          Service = var.trusted_services
        },
        Action = "sts:AssumeRole"
      }
    ]
  })

  tags = merge(
    var.tags,
    {
      Name = var.role_name
    }
  )
}

# Attach policies to the role
resource "aws_iam_role_policy_attachment" "bedrock_policy_attachment" {
  count      = length(var.policy_arns)
  role       = aws_iam_role.bedrock_role.name
  policy_arn = var.policy_arns[count.index]
}
