# Bedrock IAM Role Module - outputs.tf
# Outputs for the Bedrock IAM role module

output "role_arn" {
  description = "ARN of the IAM role for Bedrock access"
  value       = aws_iam_role.bedrock_role.arn
}

output "role_id" {
  description = "ID of the IAM role for Bedrock access"
  value       = aws_iam_role.bedrock_role.id
}

output "role_name" {
  description = "Name of the IAM role for Bedrock access"
  value       = aws_iam_role.bedrock_role.name
}
