# Bedrock IAM Role Module - variables.tf
# Variables for the Bedrock IAM role module

variable "role_name" {
  description = "Name of the IAM role for Bedrock access"
  type        = string
  default     = "BedrockAccessRole"
}

variable "role_description" {
  description = "Description of the IAM role for Bedrock access"
  type        = string
  default     = "Role for accessing Amazon Bedrock models"
}

variable "role_path" {
  description = "Path of the IAM role"
  type        = string
  default     = "/"
}

variable "trusted_services" {
  description = "List of AWS services that can assume this role"
  type        = list(string)
  default     = ["ecs-tasks.amazonaws.com"]
}

variable "policy_arns" {
  description = "List of IAM policy ARNs to attach to the role"
  type        = list(string)
}

variable "tags" {
  description = "A map of tags to add to all resources"
  type        = map(string)
  default     = {}
}
