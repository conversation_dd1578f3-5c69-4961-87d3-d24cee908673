# IAM Roles Module - outputs.tf
# Outputs from the IAM roles module

output "ecs_task_execution_role_arn" {
  description = "ARN of the ECS task execution role"
  value       = var.create_ecs_task_execution_role ? aws_iam_role.ecs_task_execution_role[0].arn : null
}

output "ecs_task_execution_role_name" {
  description = "Name of the ECS task execution role"
  value       = var.create_ecs_task_execution_role ? aws_iam_role.ecs_task_execution_role[0].name : null
}
