# IAM Roles Module - main.tf
# This module creates IAM roles for ECS task execution

# ECS Task Execution Role
resource "aws_iam_role" "ecs_task_execution_role" {
  count = var.create_ecs_task_execution_role ? 1 : 0

  name = "${var.name_prefix}-ecs-task-execution-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ecs-tasks.amazonaws.com"
        }
      }
    ]
  })

  tags = merge(
    var.tags,
    {
      Name = "${var.name_prefix}-ecs-task-execution-role"
    }
  )
}

# Attach AWS managed ECS task execution policy
resource "aws_iam_role_policy_attachment" "ecs_task_execution_policy" {
  count = var.create_ecs_task_execution_role ? 1 : 0

  role       = aws_iam_role.ecs_task_execution_role[0].name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

# Attach custom policies to the ECS task execution role
resource "aws_iam_role_policy_attachment" "custom_policy_attachments" {
  count = var.create_ecs_task_execution_role ? length(var.custom_policy_arns) : 0

  role       = aws_iam_role.ecs_task_execution_role[0].name
  policy_arn = var.custom_policy_arns[count.index]
}
