# IAM Roles Module - variables.tf
# Variables for the IAM roles module

variable "name_prefix" {
  description = "Prefix to use for resource names (usually environment name)"
  type        = string
}

variable "create_ecs_task_execution_role" {
  description = "Whether to create the ECS task execution role"
  type        = bool
  default     = false
}

variable "custom_policy_arns" {
  description = "List of custom policy ARNs to attach to the ECS task execution role"
  type        = list(string)
  default     = []
}

variable "tags" {
  description = "Tags to apply to all resources"
  type        = map(string)
  default     = {}
}
