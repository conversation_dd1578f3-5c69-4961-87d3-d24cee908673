# Secrets Manager Module - main.tf
# This module creates a Secrets Manager secret and optionally a secret version

resource "aws_secretsmanager_secret" "this" {
  name        = var.name
  description = var.description
  kms_key_id  = var.kms_key_id

  recovery_window_in_days = var.recovery_window_in_days

  tags = merge(
    var.tags,
    {
      Name = var.name
    }
  )
}

resource "aws_secretsmanager_secret_version" "this" {
  count         = var.create_secret_version ? 1 : 0
  secret_id     = aws_secretsmanager_secret.this.id
  secret_string = var.secret_string
}
