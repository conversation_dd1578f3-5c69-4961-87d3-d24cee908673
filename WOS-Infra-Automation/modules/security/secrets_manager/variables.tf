# Secrets Manager Module - variables.tf
# Variables for the Secrets Manager module

variable "name" {
  description = "Name of the secret"
  type        = string
}

variable "description" {
  description = "Description of the secret"
  type        = string
  default     = ""
}

variable "kms_key_id" {
  description = "ARN or ID of the AWS KMS key to be used to encrypt the secret values"
  type        = string
  default     = null
}

variable "recovery_window_in_days" {
  description = "Number of days that AWS Secrets Manager waits before it can delete the secret"
  type        = number
  default     = 30
}

variable "create_secret_version" {
  description = "Whether to create a secret version with the secret value"
  type        = bool
  default     = false
}

variable "secret_string" {
  description = "Secret value to store"
  type        = string
  default     = ""
  sensitive   = true
}

variable "tags" {
  description = "Tags to apply to all resources"
  type        = map(string)
  default     = {}
}
