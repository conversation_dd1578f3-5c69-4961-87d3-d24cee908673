# IAM Module

This module creates IAM roles and policies for AWS access in the WasteOS platform.

## Features

- Creates EC2 instance IAM role with SSM access
- Creates VPC Flow Logs IAM role
- Optionally creates CI/CD IAM role
- Follows AWS security best practices
- Configurable for different environments

## Usage

```hcl
module "iam" {
  source = "../../modules/security/iam"

  environment = "dev"

  # EC2 Role Configuration
  create_ec2_role = true
  s3_bucket_arns = [
    "arn:aws:s3:::wos-dev-bucket/*",
    "arn:aws:s3:::wos-dev-bucket"
  ]
  secret_arns = [
    "arn:aws:secretsmanager:ap-southeast-1:account-id:secret:secret-name-1",
    "arn:aws:secretsmanager:ap-southeast-1:account-id:secret:secret-name-2"
  ]
  dynamodb_table_arns = [
    "arn:aws:dynamodb:ap-southeast-1:account-id:table/table-name"
  ]

  # VPC Flow Logs Role Configuration
  create_flowlogs_role = true

  # CI/CD Role Configuration (optional)
  create_cicd_role = false
  trusted_account_arns = ["arn:aws:iam::account-id:root"]
  external_id = "unique-external-id"

  tags = {
    Project     = "WasteOS"
    Environment = "dev"
    Owner       = "Platform Team"
    CostCenter  = "Engineering"
    ManagedBy   = "Terraform"
  }
}
```

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|----------|
| environment | Environment name (e.g., dev, stg, prod) | string | n/a | yes |
| tags | A map of tags to add to all resources | map(string) | {} | no |
| create_ec2_role | Whether to create the EC2 IAM role | bool | true | no |
| create_flowlogs_role | Whether to create the VPC Flow Logs IAM role | bool | true | no |
| create_cicd_role | Whether to create the CI/CD IAM role | bool | false | no |
| s3_bucket_arns | List of S3 bucket ARNs that EC2 instances need access to | list(string) | [] | no |
| secret_arns | List of Secrets Manager secret ARNs that EC2 instances need access to | list(string) | [] | no |
| dynamodb_table_arns | List of DynamoDB table ARNs that EC2 instances need access to | list(string) | [] | no |
| trusted_account_arns | List of AWS account ARNs that can assume the CI/CD role | list(string) | [] | no |
| external_id | External ID for the CI/CD role trust relationship | string | "" | no |
| terraform_state_bucket | Name of the S3 bucket used for Terraform state storage | string | "wos-terraform-state-bucket" | no |
| terraform_lock_table | Name of the DynamoDB table used for Terraform state locking | string | "terraform-statefile-locks-wos" | no |
| deployment_history_table | Name of the DynamoDB table used for deployment history | string | "wos-deployment-history" | no |

## Outputs

| Name | Description |
|------|-------------|
| ec2_instance_profile_name | Name of the EC2 instance profile |
| ec2_instance_profile_arn | ARN of the EC2 instance profile |
| ec2_iam_role_name | Name of the EC2 IAM role |
| ec2_iam_role_arn | ARN of the EC2 IAM role |
| ec2_ssm_policy_arn | ARN of the EC2 SSM policy |
| ec2_task_role_policy_arn | ARN of the EC2 task role policy |
| vpc_flowlogs_role_name | Name of the VPC Flow Logs IAM role |
| vpc_flowlogs_role_arn | ARN of the VPC Flow Logs IAM role |
| cicd_role_name | Name of the CI/CD IAM role |
| cicd_role_arn | ARN of the CI/CD IAM role |
| cicd_policy_arn | ARN of the CI/CD policy |

## Notes

- The CI/CD role is typically created outside of Terraform, but is included here for documentation purposes
- All resources use "wos" naming convention for consistency
- Ensure that the CI/CD role has appropriate permissions for all environments
- Consider implementing separate roles for different environments to enhance security
- Regularly rotate credentials and review IAM permissions
- Use AWS IAM Access Analyzer to identify unused permissions and potential security risks
