# AWS Network ACLs Terraform Module

This module creates network ACLs for different subnet types in the WasteOS platform.

## Features

- Creates network ACLs for public, private application, and private database subnets
- Configures appropriate inbound and outbound rules for each network ACL
- Applies consistent tagging to all resources
- Follows security best practices and the principle of least privilege

## Usage

```hcl
module "network_acls" {
  source = "../../modules/networking/network_acls"

  environment           = "dev"
  vpc_id                = module.vpc.vpc_id
  vpc_cidr              = module.vpc.vpc_cidr_block
  public_subnet_ids     = module.vpc.public_subnet_ids
  private_app_subnet_ids = module.vpc.private_app_subnet_ids
  private_db_subnet_ids  = module.vpc.private_db_subnet_ids
  private_app_cidr      = "*********/22"  # Combined CIDR for all private app subnets
  
  tags = {
    Project     = "WasteOS"
    Environment = "Dev"
    Owner       = "Platform Team"
    CostCenter  = "Engineering"
    ManagedBy   = "Terraform"
  }
}
```

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|----------|
| environment | Environment name (e.g., dev, uat, prod) | string | n/a | yes |
| vpc_id | ID of the VPC where network ACLs will be created | string | n/a | yes |
| vpc_cidr | CIDR block of the VPC | string | n/a | yes |
| public_subnet_ids | List of public subnet IDs | list(string) | n/a | yes |
| private_app_subnet_ids | List of private application subnet IDs | list(string) | n/a | yes |
| private_db_subnet_ids | List of private database subnet IDs | list(string) | n/a | yes |
| private_app_cidr | CIDR block for private application subnets | string | n/a | yes |
| tags | A map of tags to add to all resources | map(string) | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| public_nacl_id | ID of the public subnet network ACL |
| private_app_nacl_id | ID of the private application subnet network ACL |
| private_db_nacl_id | ID of the private database subnet network ACL |

## Network ACLs Created

### Public Subnet Network ACL
- Name: `wos-{environment}-public-nacl`
- Inbound Rules:
  - Rule 100: Allow HTTP (80) from 0.0.0.0/0
  - Rule 110: Allow HTTPS (443) from 0.0.0.0/0
  - Rule 120: Allow SSH (22) from internal network (10.0.0.0/8)
  - Rule 130: Allow return traffic (ephemeral ports 1024-65535) from 0.0.0.0/0
  - Rule 140: Allow all traffic from VPC CIDR
- Outbound Rules:
  - Rule 100: Allow HTTP (80) to 0.0.0.0/0
  - Rule 110: Allow HTTPS (443) to 0.0.0.0/0
  - Rule 120: Allow ephemeral ports (1024-65535) to 0.0.0.0/0
  - Rule 130: Allow all traffic to VPC CIDR

### Private Application Subnet Network ACL
- Name: `wos-{environment}-private-app-nacl`
- Inbound Rules:
  - Rule 100: Allow all traffic from VPC CIDR
  - Rule 110: Allow return traffic (ephemeral ports 1024-65535) from 0.0.0.0/0
- Outbound Rules:
  - Rule 100: Allow HTTP (80) to 0.0.0.0/0
  - Rule 110: Allow HTTPS (443) to 0.0.0.0/0
  - Rule 120: Allow all traffic to VPC CIDR

### Private Database Subnet Network ACL
- Name: `wos-{environment}-private-db-nacl`
- Inbound Rules:
  - Rule 100: Allow PostgreSQL (5432) from Private Application Subnet CIDRs
  - Rule 110: Allow Redis (6379) from Private Application Subnet CIDRs
  - Rule 120: Allow all traffic from VPC CIDR
- Outbound Rules:
  - Rule 100: Allow return traffic (ephemeral ports 1024-65535) to Private Application Subnet CIDRs
  - Rule 110: Allow all traffic to VPC CIDR

## Security Considerations

- Network ACLs are stateless, so return traffic must be explicitly allowed
- Network ACLs provide an additional layer of defense beyond security groups
- Rule numbers are spaced to allow for future insertions
- Network ACLs enforce subnet-level security policies
- All network ACLs are properly tagged for easier management

## Notes

- This module is designed to be used with the WasteOS platform
- Network ACLs are associated with specific subnet types
- The module is designed to work with the existing VPC infrastructure
- Network ACL rules can be customized by modifying the module code
