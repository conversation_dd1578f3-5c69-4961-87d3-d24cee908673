# AWS VPC Flow Logs Terraform Module

This module creates VPC Flow Logs for the WasteOS platform.

## Features

- Creates VPC Flow Logs to capture network traffic
- Configures CloudWatch Log Group with customizable retention period
- Creates IAM role with necessary permissions for Flow Logs
- Applies consistent tagging to all resources

## Usage

```hcl
module "vpc_flow_logs" {
  source = "../../modules/networking/vpc_flow_logs"

  environment       = "dev"
  vpc_id            = module.vpc.vpc_id
  log_retention_days = 7  # 7 days for dev, 14 for UAT, 30 for prod
  
  tags = {
    Project     = "WasteOS"
    Environment = "Dev"
    Owner       = "Platform Team"
    CostCenter  = "Engineering"
    ManagedBy   = "Terraform"
  }
}
```

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|----------|
| environment | Environment name (e.g., dev, uat, prod) | string | n/a | yes |
| vpc_id | ID of the VPC where flow logs will be created | string | n/a | yes |
| log_retention_days | Number of days to retain flow logs in CloudWatch | number | 7 | no |
| tags | A map of tags to add to all resources | map(string) | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| flow_log_id | ID of the VPC Flow Log |
| log_group_arn | ARN of the CloudWatch Log Group for VPC Flow Logs |
| log_group_name | Name of the CloudWatch Log Group for VPC Flow Logs |
| iam_role_arn | ARN of the IAM Role for VPC Flow Logs |
| iam_role_name | Name of the IAM Role for VPC Flow Logs |

## Resources Created

### VPC Flow Logs
- Name: `wos-{environment}-vpc-flow-logs`
- Captures all traffic (ALL)
- Sends logs to CloudWatch Log Group

### CloudWatch Log Group
- Name: `/aws/vpc/flow-logs/{vpc_id}`
- Configurable retention period (default: 7 days)

### IAM Role
- Name: `wos-{environment}-vpc-flow-logs-role`
- Allows VPC Flow Logs service to assume the role

### IAM Policy
- Name: `wos-{environment}-vpc-flow-logs-policy`
- Allows creating log groups, log streams, and putting log events

## Security Considerations

- VPC Flow Logs provide visibility into network traffic for security analysis
- Flow logs can be used for troubleshooting connectivity issues
- Flow logs can be used for compliance purposes
- The IAM role follows the principle of least privilege

## Notes

- This module is designed to be used with the WasteOS platform
- Log retention periods should be adjusted based on environment:
  - Development: 7 days
  - UAT: 14 days
  - Production: 30 days
- The module is designed to work with the existing VPC infrastructure
- Flow logs capture traffic at the network interface level
