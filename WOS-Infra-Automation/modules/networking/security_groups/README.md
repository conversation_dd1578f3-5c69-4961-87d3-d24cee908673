# AWS Security Groups Terraform Module

This module creates security groups for various components of the WasteOS platform.

## Features

- Creates security groups for VPC endpoints, ALB, EKS cluster, EKS nodes, RDS, and Redis
- Configures appropriate ingress and egress rules for each security group
- Applies consistent tagging to all resources
- Follows security best practices and the principle of least privilege

## Usage

```hcl
module "security_groups" {
  source = "../../modules/networking/security_groups"

  environment = "dev"
  vpc_id      = module.vpc.vpc_id
  vpc_cidr    = module.vpc.vpc_cidr_block
  
  tags = {
    Project     = "WasteOS"
    Environment = "Dev"
    Owner       = "Platform Team"
    CostCenter  = "Engineering"
    ManagedBy   = "Terraform"
  }
}
```

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|----------|
| environment | Environment name (e.g., dev, uat, prod) | string | n/a | yes |
| vpc_id | ID of the VPC where security groups will be created | string | n/a | yes |
| vpc_cidr | CIDR block of the VPC | string | n/a | yes |
| tags | A map of tags to add to all resources | map(string) | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| vpc_endpoints_sg_id | ID of the VPC endpoints security group |
| vpc_endpoints_sg_arn | ARN of the VPC endpoints security group |
| alb_sg_id | ID of the ALB security group |
| alb_sg_arn | ARN of the ALB security group |
| eks_cluster_sg_id | ID of the EKS cluster security group |
| eks_cluster_sg_arn | ARN of the EKS cluster security group |
| eks_node_sg_id | ID of the EKS node security group |
| eks_node_sg_arn | ARN of the EKS node security group |
| rds_sg_id | ID of the RDS security group |
| rds_sg_arn | ARN of the RDS security group |
| redis_sg_id | ID of the Redis security group |
| redis_sg_arn | ARN of the Redis security group |

## Security Groups Created

### VPC Endpoints Security Group
- Name: `wos-{environment}-vpc-endpoints-sg`
- Allows HTTPS (443) from VPC CIDR block
- Allows all outbound traffic

### ALB Security Group
- Name: `wos-{environment}-alb-sg`
- Allows HTTP (80) and HTTPS (443) from anywhere
- Allows all outbound traffic

### EKS Cluster Security Group
- Name: `wos-{environment}-eks-cluster-sg`
- Allows HTTPS (443) from ALB Security Group
- Allows all traffic from self (self-referencing rule)
- Allows all outbound traffic

### EKS Node Security Group
- Name: `wos-{environment}-eks-node-sg`
- Allows all traffic from self (node-to-node communication)
- Allows all traffic from EKS Cluster Security Group
- Allows TCP ports 1025-65535 from ALB Security Group
- Allows all outbound traffic
- Includes Kubernetes-specific tags

### RDS Security Group
- Name: `wos-{environment}-rds-sg`
- Allows PostgreSQL (5432) from EKS Cluster Security Group
- Allows all outbound traffic

### Redis Security Group
- Name: `wos-{environment}-redis-sg`
- Allows Redis (6379) from EKS Cluster Security Group
- Allows all outbound traffic

## Security Considerations

- Security groups follow the principle of least privilege
- Security group references are used instead of CIDR blocks where appropriate
- All rules include descriptions for better documentation
- Ingress rules are limited to specific ports and sources
- All security groups are properly tagged for easier management

## Notes

- This module is designed to be used with the WasteOS platform
- Security groups are stateful, so return traffic is automatically allowed
- The module is designed to work with the existing VPC infrastructure
- Security group rules can be customized by modifying the module code
