# Route53 Module - variables.tf
# Variables for the Route53 module

variable "environment" {
  description = "Environment name"
  type        = string
}

variable "domain_name" {
  description = "Domain name for the Route53 records"
  type        = string
}

variable "hosted_zone_id" {
  description = "ID of the Route53 hosted zone"
  type        = string
}

variable "alb_dns_name" {
  description = "DNS name of the ALB"
  type        = string
}

variable "alb_zone_id" {
  description = "Zone ID of the ALB"
  type        = string
}

variable "create_api_record" {
  description = "Whether to create an API record"
  type        = bool
  default     = false
}

variable "create_dashboard_record" {
  description = "Whether to create a dashboard record"
  type        = bool
  default     = false
}

variable "create_chatbot_record" {
  description = "Whether to create a chatbot record"
  type        = bool
  default     = false
}
