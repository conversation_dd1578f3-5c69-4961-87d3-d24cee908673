/**
 * # AWS Route53 Module
 *
 * This module creates Route53 records for the WasteOS platform.
 */

# Route53 record for the main application
resource "aws_route53_record" "app" {
  zone_id = var.hosted_zone_id
  name    = var.environment == "prod" ? "wos.${var.domain_name}" : "wos.${var.environment}.${var.domain_name}"
  type    = "A"

  alias {
    name                   = var.alb_dns_name
    zone_id                = var.alb_zone_id
    evaluate_target_health = true
  }
}

# Route53 record for the API (optional)
resource "aws_route53_record" "api" {
  count = var.create_api_record ? 1 : 0

  zone_id = var.hosted_zone_id
  name    = var.environment == "prod" ? "api.${var.domain_name}" : "api.${var.environment}.${var.domain_name}"
  type    = "A"

  alias {
    name                   = var.alb_dns_name
    zone_id                = var.alb_zone_id
    evaluate_target_health = true
  }
}

# Route53 record for the Dashboard (optional)
resource "aws_route53_record" "dashboard" {
  count = var.create_dashboard_record ? 1 : 0

  zone_id = var.hosted_zone_id
  name    = var.environment == "prod" ? "dashboard.${var.domain_name}" : "dashboard.${var.environment}.${var.domain_name}"
  type    = "A"

  alias {
    name                   = var.alb_dns_name
    zone_id                = var.alb_zone_id
    evaluate_target_health = true
  }
}

# Route53 record for the Chatbot (optional)
resource "aws_route53_record" "chatbot" {
  count = var.create_chatbot_record ? 1 : 0

  zone_id = var.hosted_zone_id
  name    = var.environment == "prod" ? "chatbot.${var.domain_name}" : "chatbot.${var.environment}.${var.domain_name}"
  type    = "A"

  alias {
    name                   = var.alb_dns_name
    zone_id                = var.alb_zone_id
    evaluate_target_health = true
  }
}
