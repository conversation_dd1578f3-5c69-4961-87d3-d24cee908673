# Route53 Module - outputs.tf
# Outputs from the Route53 module

output "app_fqdn" {
  description = "The FQDN of the application"
  value       = aws_route53_record.app.fqdn
}

output "api_fqdn" {
  description = "The FQDN of the API"
  value       = var.create_api_record ? aws_route53_record.api[0].fqdn : null
}

output "dashboard_fqdn" {
  description = "The FQDN of the Dashboard"
  value       = var.create_dashboard_record ? aws_route53_record.dashboard[0].fqdn : null
}

output "chatbot_fqdn" {
  description = "The FQDN of the Chatbot"
  value       = var.create_chatbot_record ? aws_route53_record.chatbot[0].fqdn : null
}
