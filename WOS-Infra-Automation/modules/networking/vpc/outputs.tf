# VPC Module - outputs.tf
# Outputs from the VPC module

output "vpc_id" {
  description = "The ID of the VPC"
  value       = aws_vpc.this.id
}

output "vpc_cidr_block" {
  description = "The CIDR block of the VPC"
  value       = aws_vpc.this.cidr_block
}

output "vpc_arn" {
  description = "The ARN of the VPC"
  value       = aws_vpc.this.arn
}

output "internet_gateway_id" {
  description = "The ID of the Internet Gateway"
  value       = aws_internet_gateway.this.id
}

output "public_subnet_ids" {
  description = "List of IDs of public subnets"
  value       = aws_subnet.public[*].id
}

output "public_subnet_arns" {
  description = "List of ARNs of public subnets"
  value       = aws_subnet.public[*].arn
}

output "public_subnet_cidrs" {
  description = "List of CIDR blocks of public subnets"
  value       = aws_subnet.public[*].cidr_block
}

output "private_app_subnet_ids" {
  description = "List of IDs of private application subnets"
  value       = aws_subnet.private_app[*].id
}

output "private_app_subnet_arns" {
  description = "List of ARNs of private application subnets"
  value       = aws_subnet.private_app[*].arn
}

output "private_app_subnet_cidrs" {
  description = "List of CIDR blocks of private application subnets"
  value       = aws_subnet.private_app[*].cidr_block
}

output "private_db_subnet_ids" {
  description = "List of IDs of private database subnets"
  value       = aws_subnet.private_db[*].id
}

output "private_db_subnet_arns" {
  description = "List of ARNs of private database subnets"
  value       = aws_subnet.private_db[*].arn
}

output "private_db_subnet_cidrs" {
  description = "List of CIDR blocks of private database subnets"
  value       = aws_subnet.private_db[*].cidr_block
}

output "nat_gateway_ids" {
  description = "List of NAT Gateway IDs"
  value       = aws_nat_gateway.this[*].id
}

output "nat_gateway_public_ips" {
  description = "List of public Elastic IPs created for NAT Gateways"
  value       = aws_eip.nat[*].public_ip
}

output "public_route_table_ids" {
  description = "List of IDs of public route tables"
  value       = aws_route_table.public[*].id
}

output "private_app_route_table_ids" {
  description = "List of IDs of private application route tables"
  value       = aws_route_table.private_app[*].id
}

output "private_db_route_table_ids" {
  description = "List of IDs of private database route tables"
  value       = aws_route_table.private_db[*].id
}

output "alb_security_group_id" {
  description = "ID of the ALB security group"
  value       = aws_security_group.alb.id
}

output "eks_cluster_security_group_id" {
  description = "ID of the EKS cluster security group"
  value       = aws_security_group.eks_cluster.id
}

output "rds_security_group_id" {
  description = "ID of the RDS security group"
  value       = aws_security_group.rds.id
}

output "redis_security_group_id" {
  description = "ID of the Redis security group"
  value       = aws_security_group.redis.id
}

output "vpn_access_security_group_id" {
  description = "ID of the VPN access security group"
  value       = var.create_vpn_access_sg ? aws_security_group.vpn_access[0].id : null
}

output "vpc_endpoint_s3_id" {
  description = "ID of the S3 VPC endpoint"
  value       = var.create_vpc_endpoints ? aws_vpc_endpoint.s3[0].id : null
}

output "vpc_endpoint_dynamodb_id" {
  description = "ID of the DynamoDB VPC endpoint"
  value       = var.create_vpc_endpoints ? aws_vpc_endpoint.dynamodb[0].id : null
}

output "vpc_endpoint_ids" {
  description = "Map of VPC endpoint IDs"
  value       = var.create_vpc_endpoints ? { for k, v in aws_vpc_endpoint.interface_endpoints : k => v.id } : {}
}
