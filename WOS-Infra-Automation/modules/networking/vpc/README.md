# AWS VPC Terraform Module

This module creates a complete AWS VPC infrastructure with public and private subnets across multiple availability zones, NAT gateways, route tables, security groups, and VPC endpoints.

## Features

- Creates a VPC with configurable CIDR block
- Creates public, private application, and private database subnets across multiple AZs
- Creates Internet Gateway and NAT Gateways for internet connectivity
- Creates route tables and routes for each subnet type
- Creates security groups for ALB, EKS, RDS, Redis, and VPN access
- Creates VPC endpoints for AWS services (S3, DynamoDB, EC2, ECR, Secrets Manager, SES, CloudWatch Logs)
- Applies consistent tagging to all resources

## Usage

```hcl
module "vpc" {
  source = "../../modules/networking/vpc"

  name_prefix = "wos-dev"
  vpc_cidr    = "*********/20"
  region      = "ap-southeast-1"
  
  public_subnets      = ["*********/24", "*********/24", "*********/24"]
  private_app_subnets = ["*********/24", "*********/24", "*********/24"]
  private_db_subnets  = ["*********/24", "*********/24", "*********/24"]
  
  azs = ["ap-southeast-1a", "ap-southeast-1b", "ap-southeast-1c"]
  
  create_nat_gateway   = true
  create_vpc_endpoints = true
  
  tags = {
    Project     = "WasteOS"
    Environment = "Dev"
    Owner       = "Platform Team"
    CostCenter  = "Engineering"
    ManagedBy   = "Terraform"
  }
}
```

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|----------|
| name_prefix | Prefix to use for resource names | string | `"wos-dev"` | no |
| vpc_cidr | CIDR block for the VPC | string | `"*********/20"` | no |
| enable_dns_hostnames | Enable DNS hostnames in the VPC | bool | `true` | no |
| enable_dns_support | Enable DNS support in the VPC | bool | `true` | no |
| azs | Availability zones to use | list(string) | `["ap-southeast-1a", "ap-southeast-1b", "ap-southeast-1c"]` | no |
| az_suffixes | Suffixes to use for AZ-specific resources | list(string) | `["1a", "1b", "1c"]` | no |
| public_subnets | CIDR blocks for public subnets | list(string) | `["*********/24", "*********/24", "*********/24"]` | no |
| private_app_subnets | CIDR blocks for private application subnets | list(string) | `["*********/24", "*********/24", "*********/24"]` | no |
| private_db_subnets | CIDR blocks for private database subnets | list(string) | `["*********/24", "*********/24", "*********/24"]` | no |
| map_public_ip_on_launch | Auto-assign public IP on launch for public subnets | bool | `true` | no |
| create_nat_gateway | Create NAT Gateways in public subnets | bool | `true` | no |
| create_vpc_endpoints | Create VPC endpoints for AWS services | bool | `true` | no |
| create_vpn_access_sg | Create VPN access security group | bool | `true` | no |
| vpn_cidr_blocks | CIDR blocks for VPN access | list(string) | `["10.0.0.0/8"]` | no |
| region | AWS region | string | `"ap-southeast-1"` | no |
| tags | Tags to apply to all resources | map(string) | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| vpc_id | The ID of the VPC |
| vpc_cidr_block | The CIDR block of the VPC |
| vpc_arn | The ARN of the VPC |
| internet_gateway_id | The ID of the Internet Gateway |
| public_subnet_ids | List of IDs of public subnets |
| public_subnet_arns | List of ARNs of public subnets |
| public_subnet_cidrs | List of CIDR blocks of public subnets |
| private_app_subnet_ids | List of IDs of private application subnets |
| private_app_subnet_arns | List of ARNs of private application subnets |
| private_app_subnet_cidrs | List of CIDR blocks of private application subnets |
| private_db_subnet_ids | List of IDs of private database subnets |
| private_db_subnet_arns | List of ARNs of private database subnets |
| private_db_subnet_cidrs | List of CIDR blocks of private database subnets |
| nat_gateway_ids | List of NAT Gateway IDs |
| nat_gateway_public_ips | List of public Elastic IPs created for NAT Gateways |
| public_route_table_ids | List of IDs of public route tables |
| private_app_route_table_ids | List of IDs of private application route tables |
| private_db_route_table_ids | List of IDs of private database route tables |
| alb_security_group_id | ID of the ALB security group |
| eks_cluster_security_group_id | ID of the EKS cluster security group |
| rds_security_group_id | ID of the RDS security group |
| redis_security_group_id | ID of the Redis security group |
| vpn_access_security_group_id | ID of the VPN access security group |
| vpc_endpoint_s3_id | ID of the S3 VPC endpoint |
| vpc_endpoint_dynamodb_id | ID of the DynamoDB VPC endpoint |
| vpc_endpoint_ids | Map of VPC endpoint IDs |

## Security Considerations

- All subnets are properly tagged for Kubernetes integration
- Security groups follow the principle of least privilege
- VPC endpoints are used to keep traffic within the AWS network
- NAT Gateways are deployed in each AZ for high availability

## Notes

- This module creates one NAT Gateway per AZ for high availability
- VPC endpoints are created for commonly used AWS services
- Security groups are created for common application components
- The module is designed to be used in a multi-AZ architecture
