output "instance_id" {
  description = "ID of the EC2 instance"
  value       = aws_instance.github_runner_ec2.id
}

output "instance_private_ip" {
  description = "Private IP of the EC2 instance"
  value       = aws_instance.github_runner_ec2.private_ip
}

output "security_group_id" {
  description = "ID of the EC2 security group"
  value       = aws_security_group.security_group.id
}

output "iam_role_arn" {
  description = "ARN of the IAM role"
  value       = aws_iam_role.instance_role.arn
}

output "iam_policy_arn" {
  description = "ARN of the IAM policy"
  value       = aws_iam_policy.rds_access_policy.arn
}