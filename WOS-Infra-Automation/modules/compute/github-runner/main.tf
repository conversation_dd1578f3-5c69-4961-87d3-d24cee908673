# EC2 Instance for GitHub Runner
resource "aws_instance" "github_runner_ec2" {
  ami                    = data.aws_ami.amazon_linux_2023.id
  instance_type          = var.instance_type
  subnet_id              = var.subnet_id
  vpc_security_group_ids = [aws_security_group.security_group.id]
  iam_instance_profile   = aws_iam_instance_profile.instance_profile.name
  key_name               = var.key_name

  root_block_device {
    volume_size = var.root_volume_size
    volume_type = "gp3"
    encrypted   = true
  }

  tags = merge(
    var.tags,
    {
      Name = "wos-${var.environment}-github-runner"
    }
  )
}

# Security Group for EC2 Instance
resource "aws_security_group" "security_group" {
  name        = "wos-${var.environment}-github-runner-sg"
  description = "Security group for GitHub Runner instance"
  vpc_id      = var.vpc_id

  # SSH access from allowed CIDR blocks
  ingress {
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = var.allowed_ssh_cidr_blocks
    description = "SSH access from allowed CIDR blocks"
  }

  # Outbound internet access (needed for GitHub communication)
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow all outbound traffic"
  }

  tags = merge(
    var.tags,
    {
      Name = "wos-${var.environment}-github-runner-sg"
    }
  )
}

# IAM Instance Profile
resource "aws_iam_instance_profile" "instance_profile" {
  name = "wos-${var.environment}-github-runner-profile"
  role = aws_iam_role.instance_role.name
}

# IAM Role
resource "aws_iam_role" "instance_role" {
  name = "wos-${var.environment}-github-runner-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ec2.amazonaws.com"
        }
      }
    ]
  })

  tags = merge(
    var.tags,
    {
      Name = "wos-${var.environment}-github-runner-role"
    }
  )
}

# Attach SSM policy for management
resource "aws_iam_role_policy_attachment" "ssm_policy_attachment" {
  role       = aws_iam_role.instance_role.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
}

# Attach RDS access policy
resource "aws_iam_role_policy_attachment" "rds_policy_attachment" {
  role       = aws_iam_role.instance_role.name
  policy_arn = aws_iam_policy.rds_access_policy.arn
}

# IAM Policy for RDS Access
resource "aws_iam_policy" "rds_access_policy" {
  name        = "wos-${var.environment}-rds-access-policy"
  description = "Policy for accessing RDS in private subnet"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "AllowRDSDescribeAccess"
        Effect = "Allow"
        Action = [
          "rds:DescribeDBInstances",
          "rds:DescribeDBClusters",
          "rds:DescribeDBParameterGroups",
          "rds:DescribeDBParameters",
          "rds:DescribeDBSubnetGroups",
          "rds:DescribeOptionGroups"
        ]
        Resource = "*"
      },
      {
        Sid    = "AllowSecretsManagerAccess"
        Effect = "Allow"
        Action = [
          "secretsmanager:GetSecretValue",
          "secretsmanager:ListSecrets",
          "secretsmanager:DescribeSecret"
        ]
        Resource = var.secret_arns
      }
    ]
  })

  tags = merge(
    var.tags,
    {
      Name = "wos-${var.environment}-rds-access-policy"
    }
  )
}

# Security Group Rule to allow EC2 to access RDS
resource "aws_security_group_rule" "rds_from_ec2" {
  type                     = "ingress"
  from_port                = 5432
  to_port                  = 5432
  protocol                 = "tcp"
  source_security_group_id = aws_security_group.security_group.id
  security_group_id        = var.rds_security_group_id
  description              = "Allow PostgreSQL from GitHub Runner"
}

# Data source to get the latest Amazon Linux 2023 AMI
data "aws_ami" "amazon_linux_2023" {
  most_recent = true
  owners      = ["amazon"]

  filter {
    name   = "name"
    values = ["al2023-ami-2023.*-x86_64"]
  }

  filter {
    name   = "virtualization-type"
    values = ["hvm"]
  }

  filter {
    name   = "root-device-type"
    values = ["ebs"]
  }
}