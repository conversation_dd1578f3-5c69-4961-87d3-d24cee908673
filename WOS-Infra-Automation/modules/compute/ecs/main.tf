# ECS Module - main.tf
# This module creates an ECS cluster with security groups and IAM roles

# Security Group for ECS Tasks
resource "aws_security_group" "ecs_sg" {
  name_prefix = "${var.name_prefix}-ecs-sg"
  description = "ECS security group"
  vpc_id      = var.vpc_id

  ingress {
    from_port   = 8000
    to_port     = 8000
    protocol    = "tcp"
    cidr_blocks = [var.vpc_cidr]
    description = "Allow HTTP traffic on port 8000 from VPC"
  }

  ingress {
    from_port   = 2000
    to_port     = 2000
    protocol    = "udp"
    cidr_blocks = [var.vpc_cidr]
    description = "Allow UDP traffic on port 2000 from VPC"
  }

  ingress {
    from_port   = 8443
    to_port     = 8443
    protocol    = "tcp"
    cidr_blocks = [var.vpc_cidr]
    description = "Allow HTTPS traffic on port 8443 from VPC"
  }

  ingress {
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow HTTP traffic on port 80 from anywhere"
  }

  ingress {
    from_port   = 8080
    to_port     = 8080
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow HTTP traffic on port 8080 from anywhere"
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow all outbound traffic"
  }

  tags = merge(
    var.tags,
    { "Name" = "${var.name_prefix}-ecs-sg" },
  )
}

# ECS Cluster
resource "aws_ecs_cluster" "cluster" {
  name = "${var.name_prefix}-cluster"

  setting {
    name  = "containerInsights"
    value = "enabled"
  }

  tags = merge(
    var.tags,
    { "Name" = "${var.name_prefix}-cluster" },
  )
}

# ECS Cluster Capacity Providers
resource "aws_ecs_cluster_capacity_providers" "cluster" {
  cluster_name = aws_ecs_cluster.cluster.name

  capacity_providers = ["FARGATE_SPOT", "FARGATE"]

  default_capacity_provider_strategy {
    base              = 1
    weight            = 40
    capacity_provider = "FARGATE"
  }

  default_capacity_provider_strategy {
    base              = 0
    weight            = 60
    capacity_provider = "FARGATE_SPOT"
  }
}

# IAM Role for ECS Task Execution
# Use the provided role ARN if available, otherwise create a new role
resource "aws_iam_role" "ecs_task_execution_role" {
  # Remove the count conditional to avoid dependency issues
  count = var.task_execution_role_arn != "" ? 0 : 1

  name = "${var.name_prefix}-ecs-task-execution-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ecs-tasks.amazonaws.com"
        }
      }
    ]
  })

  tags = merge(
    var.tags,
    { "Name" = "${var.name_prefix}-ecs-task-execution-role" },
  )
}

locals {
  # Use a static approach to avoid dependency issues
  create_role              = var.task_execution_role_arn == ""
  task_execution_role_arn  = var.task_execution_role_arn == "" ? (length(aws_iam_role.ecs_task_execution_role) > 0 ? aws_iam_role.ecs_task_execution_role[0].arn : "") : var.task_execution_role_arn
  task_execution_role_name = var.task_execution_role_arn == "" ? (length(aws_iam_role.ecs_task_execution_role) > 0 ? aws_iam_role.ecs_task_execution_role[0].name : "") : element(split("/", var.task_execution_role_arn), length(split("/", var.task_execution_role_arn)) - 1)
}

# Attach the Amazon ECS Task Execution Role policy
resource "aws_iam_role_policy_attachment" "ecs_task_execution_policy" {
  count = var.task_execution_role_arn != "" ? 0 : 1

  role       = aws_iam_role.ecs_task_execution_role[0].name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

# Custom IAM Policy for ECS Tasks
resource "aws_iam_policy" "ecs_task_role_access" {
  name = "${var.name_prefix}-ecs-task-role-access"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action   = ["secretsmanager:GetSecretValue"]
        Effect   = "Allow"
        Resource = var.secret_arns
      },
      {
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents",
          "logs:DescribeLogStreams",
          "logs:DescribeLogGroups"
        ]
        Effect   = "Allow"
        Resource = ["arn:aws:logs:*:*:*"]
      },
      {
        Action = [
          "s3:PutObject",
          "s3:DeleteObject",
          "s3:GetObject",
          "s3:List*"
        ]
        Effect   = "Allow"
        Resource = var.s3_bucket_arns
      }
    ]
  })

  tags = merge(
    var.tags,
    { "Name" = "${var.name_prefix}-ecs-task-access-policy" },
  )
}

# Attach the custom policy to the task execution role
resource "aws_iam_role_policy_attachment" "ecs_task_role_policy" {
  count = var.task_execution_role_arn != "" ? 0 : 1

  role       = aws_iam_role.ecs_task_execution_role[0].name
  policy_arn = aws_iam_policy.ecs_task_role_access.arn
}

# Create a specific CloudWatch Logs policy for ECS tasks with full access
resource "aws_iam_policy" "ecs_cloudwatch_logs_policy" {
  name = "${var.name_prefix}-ecs-cloudwatch-logs-policy"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid = "CloudWatchLogsFullAccess"
        Action = [
          "logs:*",
          "cloudwatch:GenerateQuery"
        ]
        Effect   = "Allow"
        Resource = "*"
      }
    ]
  })

  tags = merge(
    var.tags,
    { "Name" = "${var.name_prefix}-ecs-cloudwatch-logs-policy" },
  )
}

# Create SSM and ECS Execute Command policy
resource "aws_iam_policy" "ecs_ssm_policy" {
  name = "${var.name_prefix}-ecs-ssm-policy"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "ssm:StartSession",
          "ssm:TerminateSession",
          "ssm:DescribeSessions",
          "ssm:GetConnectionStatus",
          "ecs:ExecuteCommand"
        ]
        Resource = "*"
      }
    ]
  })

  tags = merge(
    var.tags,
    { "Name" = "${var.name_prefix}-ecs-ssm-policy" },
  )
}

# Create KMS policy for ECS
resource "aws_iam_policy" "ecs_kms_policy" {
  name = "${var.name_prefix}-ecs-kms-policy"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "kms:Decrypt",
          "kms:GenerateDataKey"
        ]
        Resource = "*"
      }
    ]
  })

  tags = merge(
    var.tags,
    { "Name" = "${var.name_prefix}-ecs-kms-policy" },
  )
}

# Attach the CloudWatch Logs policy to the task execution role
resource "aws_iam_role_policy_attachment" "ecs_cloudwatch_logs_policy" {
  count = var.task_execution_role_arn != "" ? 0 : 1

  role       = aws_iam_role.ecs_task_execution_role[0].name
  policy_arn = aws_iam_policy.ecs_cloudwatch_logs_policy.arn
}

# Attach the SSM policy to the task execution role
resource "aws_iam_role_policy_attachment" "ecs_ssm_policy" {
  count = var.task_execution_role_arn != "" ? 0 : 1

  role       = aws_iam_role.ecs_task_execution_role[0].name
  policy_arn = aws_iam_policy.ecs_ssm_policy.arn
}

# Attach the KMS policy to the task execution role
resource "aws_iam_role_policy_attachment" "ecs_kms_policy" {
  count = var.task_execution_role_arn != "" ? 0 : 1

  role       = aws_iam_role.ecs_task_execution_role[0].name
  policy_arn = aws_iam_policy.ecs_kms_policy.arn
}
