# ECS Module - outputs.tf
# Outputs from the ECS module

output "cluster_id" {
  description = "The ID of the ECS cluster"
  value       = aws_ecs_cluster.cluster.id
}

output "cluster_arn" {
  description = "The ARN of the ECS cluster"
  value       = aws_ecs_cluster.cluster.arn
}

output "cluster_name" {
  description = "The name of the ECS cluster"
  value       = aws_ecs_cluster.cluster.name
}

output "ecs_security_group_id" {
  description = "The ID of the ECS security group"
  value       = aws_security_group.ecs_sg.id
}

output "ecs_task_execution_role_arn" {
  description = "The ARN of the ECS task execution role"
  value       = local.task_execution_role_arn
}

output "ecs_task_execution_role_name" {
  description = "The name of the ECS task execution role"
  value       = local.task_execution_role_name
}

output "ecs_task_role_access_policy_arn" {
  description = "The ARN of the ECS task role access policy"
  value       = aws_iam_policy.ecs_task_role_access.arn
}

output "ecs_cloudwatch_logs_policy_arn" {
  description = "The ARN of the ECS CloudWatch Logs policy"
  value       = aws_iam_policy.ecs_cloudwatch_logs_policy.arn
}

output "ecs_ssm_policy_arn" {
  description = "The ARN of the ECS SSM policy"
  value       = aws_iam_policy.ecs_ssm_policy.arn
}

output "ecs_kms_policy_arn" {
  description = "The ARN of the ECS KMS policy"
  value       = aws_iam_policy.ecs_kms_policy.arn
}
