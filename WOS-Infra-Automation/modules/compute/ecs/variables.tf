# ECS Module - variables.tf
# Variables for the ECS module

variable "name_prefix" {
  description = "Prefix to use for resource names"
  type        = string
}

variable "vpc_id" {
  description = "ID of the VPC where the ECS cluster will be created"
  type        = string
}

variable "vpc_cidr" {
  description = "CIDR block of the VPC"
  type        = string
}

variable "secret_arns" {
  description = "ARNs of secrets to grant access to"
  type        = list(string)
  default     = ["*"]
}

variable "s3_bucket_arns" {
  description = "ARNs of S3 buckets to grant access to"
  type        = list(string)
  default     = ["*"]
}

variable "task_execution_role_arn" {
  description = "ARN of the IAM role to use for ECS task execution"
  type        = string
  default     = ""
}

variable "tags" {
  description = "Tags to apply to all resources"
  type        = map(string)
  default     = {}
}
