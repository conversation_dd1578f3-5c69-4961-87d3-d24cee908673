# Create Secrets Manager policy for ECS
resource "aws_iam_policy" "ecs_secrets_manager_policy" {
  name = "${var.name_prefix}-ecs-secrets-manager-policy"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "secretsmanager:GetSecretValue"
        ]
        Resource = var.secret_arns
      }
    ]
  })

  tags = merge(
    var.tags,
    { "Name" = "${var.name_prefix}-ecs-secrets-manager-policy" },
  )
}

# Attach the Secrets Manager policy to the task execution role
resource "aws_iam_role_policy_attachment" "ecs_secrets_manager_policy" {
  count = var.task_execution_role_arn == "" ? 1 : 0

  role       = aws_iam_role.ecs_task_execution_role[0].name
  policy_arn = aws_iam_policy.ecs_secrets_manager_policy.arn
}
