# AWS ECS Cluster Module

This module creates an Amazon Elastic Container Service (ECS) cluster with security groups and IAM roles.

## Features

- Creates an ECS cluster with Container Insights enabled
- Configures capacity providers (FARGATE and FARGATE_SPOT)
- Creates security group for ECS tasks with appropriate ingress/egress rules
- Creates IAM role for ECS task execution with necessary permissions
- Creates custom IAM policy for ECS tasks to access AWS services

## Usage

```hcl
module "ecs" {
  source = "../../modules/compute/ecs"

  name_prefix   = "wos-dev"
  vpc_id        = module.vpc.vpc_id
  vpc_cidr      = var.vpc_cidr
  secret_arns   = ["arn:aws:secretsmanager:ap-southeast-1:123456789012:secret:*"]
  s3_bucket_arns = ["arn:aws:s3:::my-bucket/*"]

  tags = {
    Project     = "WasteOS"
    Environment = "Dev"
    Owner       = "Platform Team"
    CostCenter  = "Engineering"
    ManagedBy   = "Terraform"
  }
}
```

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| name_prefix | Prefix to use for resource names | `string` | n/a | yes |
| vpc_id | ID of the VPC where the ECS cluster will be created | `string` | n/a | yes |
| vpc_cidr | CIDR block of the VPC | `string` | n/a | yes |
| secret_arns | ARNs of secrets to grant access to | `list(string)` | `["*"]` | no |
| s3_bucket_arns | ARNs of S3 buckets to grant access to | `list(string)` | `["*"]` | no |
| tags | Tags to apply to all resources | `map(string)` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| cluster_id | The ID of the ECS cluster |
| cluster_arn | The ARN of the ECS cluster |
| cluster_name | The name of the ECS cluster |
| ecs_security_group_id | The ID of the ECS security group |
| ecs_task_execution_role_arn | The ARN of the ECS task execution role |
| ecs_task_execution_role_name | The name of the ECS task execution role |
| ecs_task_role_access_policy_arn | The ARN of the ECS task role access policy |

## Resources Created

- ECS Cluster
- ECS Cluster Capacity Providers
- Security Group for ECS Tasks
- IAM Role for ECS Task Execution
- IAM Policy for ECS Task Access
- IAM Role Policy Attachments

## Notes

- The ECS cluster uses both FARGATE and FARGATE_SPOT capacity providers
- The default capacity provider strategy uses 40% FARGATE and 60% FARGATE_SPOT
- The security group allows traffic on ports 8000 (TCP), 2000 (UDP), and 8443 (TCP) from within the VPC
- The IAM role has permissions for ECS task execution, Secrets Manager, CloudWatch Logs, and S3
