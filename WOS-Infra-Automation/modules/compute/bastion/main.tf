/**
 * # AWS Bastion Host Module
 *
 * This module creates an AWS EC2 Bastion Host with associated resources including:
 * - EC2 Instance
 * - Security Group
 * - IAM Role and Instance Profile
 * - CloudWatch Alarms
 *
 * The bastion host serves as a secure gateway to access private resources within the VPC,
 * particularly the RDS PostgreSQL database instances.
 */

# Security Group for Bastion Host
resource "aws_security_group" "bastion_sg" {
  name        = "wos-${var.environment}-bastion-sg"
  description = "Security group for Bastion Host"
  vpc_id      = var.vpc_id

  # SSH ingress from specified IP ranges
  ingress {
    description = "SSH from allowed IP ranges"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = var.allowed_ssh_cidr_blocks
  }

  # ICMP ingress from VPC CIDR for network diagnostics
  ingress {
    description = "ICMP from VPC CIDR"
    from_port   = -1
    to_port     = -1
    protocol    = "icmp"
    cidr_blocks = [var.vpc_cidr]
  }

  # Allow all outbound traffic
  egress {
    description = "Allow all outbound traffic"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # Keep specific egress rules for documentation purposes (commented out)
  /*
  # PostgreSQL egress to RDS Security Group
  egress {
    description     = "PostgreSQL to RDS"
    from_port       = 5432
    to_port         = 5432
    protocol        = "tcp"
    security_groups = [var.rds_security_group_id]
  }

  # SSH egress to VPC CIDR
  egress {
    description = "SSH to VPC CIDR"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = [var.vpc_cidr]
  }

  # HTTP egress for package updates
  egress {
    description = "HTTP for updates"
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # HTTPS egress for package updates and API calls
  egress {
    description = "HTTPS for updates and API calls"
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # DNS egress for name resolution
  egress {
    description = "DNS for name resolution"
    from_port   = 53
    to_port     = 53
    protocol    = "udp"
    cidr_blocks = ["0.0.0.0/0"]
  }
  */

  tags = merge(
    var.tags,
    {
      Name = "wos-${var.environment}-bastion-sg"
    }
  )
}

# IAM Role for Bastion Host
resource "aws_iam_role" "bastion_role" {
  name = "wos-${var.environment}-bastion-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ec2.amazonaws.com"
        }
      }
    ]
  })

  tags = merge(
    var.tags,
    {
      Name = "wos-${var.environment}-bastion-role"
    }
  )
}

# Attach AWS managed policies to the IAM role
resource "aws_iam_role_policy_attachment" "ssm_policy_attachment" {
  role       = aws_iam_role.bastion_role.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
}

resource "aws_iam_role_policy_attachment" "cloudwatch_policy_attachment" {
  role       = aws_iam_role.bastion_role.name
  policy_arn = "arn:aws:iam::aws:policy/CloudWatchAgentServerPolicy"
}

# Custom policy for bastion host
resource "aws_iam_policy" "bastion_policy" {
  name        = "wos-${var.environment}-bastion-policy"
  description = "Custom policy for bastion host"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "ec2:DescribeInstances",
          "ec2:DescribeInstanceStatus"
        ]
        Effect   = "Allow"
        Resource = "*"
      },
      {
        Action = [
          "rds:DescribeDBInstances"
        ]
        Effect   = "Allow"
        Resource = "*"
      },
      {
        Action = [
          "ssm:GetParameter",
          "ssm:GetParameters"
        ]
        Effect   = "Allow"
        Resource = "arn:aws:ssm:${var.region}:${var.account_id}:parameter/wos/${var.environment}/*"
      },
      {
        Action = [
          "secretsmanager:GetSecretValue",
          "secretsmanager:ListSecrets",
          "secretsmanager:DescribeSecret"
        ]
        Effect   = "Allow"
        Resource = "*"
      }
    ]
  })

  tags = merge(
    var.tags,
    {
      Name = "wos-${var.environment}-bastion-policy"
    }
  )
}

# Attach custom policy to the IAM role
resource "aws_iam_role_policy_attachment" "bastion_policy_attachment" {
  role       = aws_iam_role.bastion_role.name
  policy_arn = aws_iam_policy.bastion_policy.arn
}

# Instance profile for the bastion host
resource "aws_iam_instance_profile" "bastion_profile" {
  name = "wos-${var.environment}-bastion-instance-profile"
  role = aws_iam_role.bastion_role.name

  tags = merge(
    var.tags,
    {
      Name = "wos-${var.environment}-bastion-instance-profile"
    }
  )
}

# Data source to get the latest Amazon Linux 2023 AMI
data "aws_ami" "amazon_linux_2023" {
  most_recent = true
  owners      = ["amazon"]

  filter {
    name   = "name"
    values = ["al2023-ami-2023.*-x86_64"]
  }

  filter {
    name   = "virtualization-type"
    values = ["hvm"]
  }

  filter {
    name   = "root-device-type"
    values = ["ebs"]
  }
}

# Bastion Host EC2 Instance
resource "aws_instance" "bastion" {
  ami                    = data.aws_ami.amazon_linux_2023.id
  instance_type          = var.instance_type
  subnet_id              = var.subnet_id
  vpc_security_group_ids = [aws_security_group.bastion_sg.id]
  key_name               = var.key_name
  iam_instance_profile   = aws_iam_instance_profile.bastion_profile.name

  associate_public_ip_address = true

  root_block_device {
    volume_type           = "gp3"
    volume_size           = var.root_volume_size
    delete_on_termination = true
    encrypted             = true
  }

  user_data = <<-EOF
    #!/bin/bash
    # Update system
    dnf update -y

    # Install database clients and tools
    dnf install -y postgresql15 mysql redis jq

    # Install MongoDB client
    cat > /etc/yum.repos.d/mongodb-org-6.0.repo << 'EOL'
[mongodb-org-6.0]
name=MongoDB Repository
baseurl=https://repo.mongodb.org/yum/amazon/2023/mongodb-org/6.0/x86_64/
gpgcheck=1
enabled=1
gpgkey=https://www.mongodb.org/static/pgp/server-6.0.asc
EOL
    dnf install -y mongodb-org-shell

    # Install useful utilities
    dnf install -y htop iotop tcpdump telnet nmap-ncat unzip git

    # Install monitoring tools
    dnf install -y amazon-cloudwatch-agent

    # Install security tools
    dnf install -y fail2ban

    # Configure SSH hardening
    sed -i 's/#PasswordAuthentication yes/PasswordAuthentication no/' /etc/ssh/sshd_config
    sed -i 's/#ClientAliveInterval 0/ClientAliveInterval 300/' /etc/ssh/sshd_config
    sed -i 's/#ClientAliveCountMax 3/ClientAliveCountMax 2/' /etc/ssh/sshd_config
    systemctl restart sshd

    # Configure fail2ban
    cat > /etc/fail2ban/jail.local <<'EOL'
    [sshd]
    enabled = true
    port = ssh
    filter = sshd
    logpath = /var/log/secure
    maxretry = 5
    findtime = 600
    bantime = 3600
    EOL

    systemctl enable fail2ban
    systemctl start fail2ban

    # Set hostname
    hostnamectl set-hostname bastion-${var.environment}

    # Configure CloudWatch agent
    cat > /opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent.json <<'EOL'
    {
      "agent": {
        "metrics_collection_interval": 60,
        "run_as_user": "root"
      },
      "logs": {
        "logs_collected": {
          "files": {
            "collect_list": [
              {
                "file_path": "/var/log/secure",
                "log_group_name": "/wos/${var.environment}/bastion/secure",
                "log_stream_name": "{instance_id}",
                "retention_in_days": 7
              },
              {
                "file_path": "/var/log/fail2ban.log",
                "log_group_name": "/wos/${var.environment}/bastion/fail2ban",
                "log_stream_name": "{instance_id}",
                "retention_in_days": 7
              }
            ]
          }
        }
      },
      "metrics": {
        "metrics_collected": {
          "cpu": {
            "measurement": [
              "cpu_usage_idle",
              "cpu_usage_iowait",
              "cpu_usage_user",
              "cpu_usage_system"
            ],
            "metrics_collection_interval": 60,
            "totalcpu": false
          },
          "disk": {
            "measurement": [
              "used_percent",
              "inodes_free"
            ],
            "metrics_collection_interval": 60,
            "resources": [
              "/"
            ]
          },
          "diskio": {
            "measurement": [
              "io_time"
            ],
            "metrics_collection_interval": 60
          },
          "mem": {
            "measurement": [
              "mem_used_percent"
            ],
            "metrics_collection_interval": 60
          },
          "swap": {
            "measurement": [
              "swap_used_percent"
            ],
            "metrics_collection_interval": 60
          }
        },
        "append_dimensions": {
          "InstanceId": "$${aws:InstanceId}"
        }
      }
    }
    EOL

    # Start CloudWatch agent
    systemctl enable amazon-cloudwatch-agent
    systemctl start amazon-cloudwatch-agent

    # Test RDS connectivity
    echo "Testing RDS connectivity..."
    timeout 5 bash -c "</dev/tcp/wos-${var.environment}-postgres.cl8g000io4f1.ap-southeast-1.rds.amazonaws.com/5432" && echo "RDS connectivity test successful" || echo "RDS connectivity test failed"

    # Create a test script to verify PostgreSQL connection
    cat > /home/<USER>/test-rds-connection.sh << 'EOL'
#!/bin/bash
echo "Testing PostgreSQL connection..."
# Try to connect without a password first (will fail, but tests if psql works)
psql -h wos-${var.environment}-postgres.cl8g000io4f1.ap-southeast-1.rds.amazonaws.com -U wos_${var.environment}_admin -d wos_${var.environment} -c "SELECT 1" 2>&1 || echo "Connection failed as expected (no password provided)"

# Try to get password from Secrets Manager
echo "Attempting to get password from Secrets Manager..."
PASSWORD=$(aws secretsmanager get-secret-value --secret-id ${var.environment}/wos/database/credentials --query SecretString --output text 2>/dev/null | jq -r '.password' 2>/dev/null)
if [ -n "$PASSWORD" ]; then
  echo "Got password from Secrets Manager, testing connection..."
  PGPASSWORD=$PASSWORD psql -h wos-${var.environment}-postgres.cl8g000io4f1.ap-southeast-1.rds.amazonaws.com -U wos_${var.environment}_admin -d wos_${var.environment} -c "SELECT 1" && echo "Connection successful!" || echo "Connection failed with password from Secrets Manager"
else
  echo "Failed to get password from Secrets Manager"
fi

echo "Testing network connectivity to RDS..."
nc -zv wos-${var.environment}-postgres.cl8g000io4f1.ap-southeast-1.rds.amazonaws.com 5432 || echo "Network connectivity test failed"
EOL
    chmod +x /home/<USER>/test-rds-connection.sh

    # Install netcat for network testing
    dnf install -y nc

    # Create a welcome message with usage instructions
    cat > /etc/motd << 'EOL'

=======================================================================
                WasteOS Bastion Host - ${var.environment} Environment
=======================================================================

Available Database Clients:
- PostgreSQL: psql -h <hostname> -U <username> -d <database>
- MySQL:      mysql -h <hostname> -u <username> -p <database>
- MongoDB:    mongosh "mongodb://<username>:<password>@<hostname>:<port>/<database>"
- Redis:      redis-cli -h <hostname> -p <port>

Common RDS Connection Examples:
1. Using password from Secrets Manager:
   PASSWORD=$(aws secretsmanager get-secret-value --secret-id ${var.environment}/wos/database/credentials --query SecretString --output text | jq -r '.password')
   PGPASSWORD=$PASSWORD psql -h wos-${var.environment}-postgres.cl8g000io4f1.ap-southeast-1.rds.amazonaws.com -U wos_${var.environment}_admin -d wos_${var.environment}

2. Interactive password prompt:
   psql -h wos-${var.environment}-postgres.cl8g000io4f1.ap-southeast-1.rds.amazonaws.com -U wos_${var.environment}_admin -d wos_${var.environment}

Useful Utilities:
- htop:     Interactive process viewer
- iotop:    I/O usage monitor
- tcpdump:  Network packet analyzer
- git:      Version control
- jq:       JSON processor

For support, contact the DevOps team.
=======================================================================
EOL
  EOF

  tags = merge(
    var.tags,
    {
      Name = "wos-${var.environment}-bastion"
    }
  )

  # Ensure the instance is replaced if user_data changes
  lifecycle {
    create_before_destroy = true
    ignore_changes        = [ami]
  }
}

# CloudWatch Alarm for CPU Utilization - Commented out
/*
resource "aws_cloudwatch_metric_alarm" "cpu_alarm" {
  alarm_name          = "wos-${var.environment}-bastion-cpu-alarm"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 5
  metric_name         = "CPUUtilization"
  namespace           = "AWS/EC2"
  period              = 60
  statistic           = "Average"
  threshold           = 80
  alarm_description   = "This metric monitors bastion host CPU utilization"
  alarm_actions       = var.alarm_actions
  ok_actions          = var.ok_actions

  dimensions = {
    InstanceId = aws_instance.bastion.id
  }

  tags = merge(
    var.tags,
    {
      Name = "wos-${var.environment}-bastion-cpu-alarm"
    }
  )
}
*/

# CloudWatch Alarm for Status Check - Commented out
/*
resource "aws_cloudwatch_metric_alarm" "status_check_alarm" {
  alarm_name          = "wos-${var.environment}-bastion-status-alarm"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 2
  metric_name         = "StatusCheckFailed"
  namespace           = "AWS/EC2"
  period              = 60
  statistic           = "Maximum"
  threshold           = 0
  alarm_description   = "This metric monitors bastion host status checks"
  alarm_actions       = var.alarm_actions
  ok_actions          = var.ok_actions

  dimensions = {
    InstanceId = aws_instance.bastion.id
  }

  tags = merge(
    var.tags,
    {
      Name = "wos-${var.environment}-bastion-status-alarm"
    }
  )
}
*/
