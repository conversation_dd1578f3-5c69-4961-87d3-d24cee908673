# AWS Bastion Host Terraform Module

This module creates an AWS EC2 Bastion Host with associated resources including:
- EC2 Instance
- Security Group
- IAM Role and Instance Profile
- CloudWatch Alarms

The bastion host serves as a secure gateway to access private resources within the VPC, particularly the RDS PostgreSQL database instances.

## Features

- Secure SSH access to private network resources
- PostgreSQL client for database access
- Security hardening with fail2ban and SSH configuration
- CloudWatch monitoring and alarms
- IAM role with least privilege access

## Usage

```hcl
module "bastion" {
  source = "../../modules/compute/bastion"

  environment            = "dev"
  region                 = "ap-southeast-1"
  account_id             = "************"
  vpc_id                 = module.vpc.vpc_id
  vpc_cidr               = module.vpc.vpc_cidr_block
  subnet_id              = module.vpc.public_subnets[0]
  allowed_ssh_cidr_blocks = ["10.0.0.0/8", "***********/24"]
  rds_security_group_id  = module.rds.db_security_group_id
  ami_id                 = "ami-0123456789abcdef0"
  instance_type          = "t3a.micro"
  key_name               = "my-key-pair"
  root_volume_size       = 20
  
  alarm_actions          = ["arn:aws:sns:ap-southeast-1:************:alerts"]
  
  tags = {
    Project     = "WasteOS"
    Environment = "Dev"
    Owner       = "Platform Team"
    CostCenter  = "Engineering"
    ManagedBy   = "Terraform"
  }
}
```

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|----------|
| environment | Environment name (e.g., dev, uat, prod) | `string` | n/a | yes |
| region | AWS region | `string` | n/a | yes |
| account_id | AWS account ID | `string` | n/a | yes |
| vpc_id | ID of the VPC where the bastion host will be deployed | `string` | n/a | yes |
| vpc_cidr | CIDR block of the VPC | `string` | n/a | yes |
| subnet_id | ID of the public subnet where the bastion host will be deployed | `string` | n/a | yes |
| allowed_ssh_cidr_blocks | List of CIDR blocks allowed to SSH to the bastion host | `list(string)` | n/a | yes |
| rds_security_group_id | ID of the RDS security group | `string` | n/a | yes |
| ami_id | ID of the AMI to use for the bastion host (Amazon Linux 2023 recommended) | `string` | n/a | yes |
| instance_type | EC2 instance type for the bastion host | `string` | `"t3a.micro"` | no |
| key_name | Name of the SSH key pair to use for the bastion host | `string` | n/a | yes |
| root_volume_size | Size of the root volume in GB | `number` | `20` | no |
| alarm_actions | List of ARNs to notify when the bastion host alarms transition to ALARM state | `list(string)` | `[]` | no |
| ok_actions | List of ARNs to notify when the bastion host alarms transition to OK state | `list(string)` | `[]` | no |
| tags | Tags to apply to all resources | `map(string)` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| bastion_instance_id | ID of the bastion host EC2 instance |
| bastion_public_ip | Public IP address of the bastion host |
| bastion_private_ip | Private IP address of the bastion host |
| bastion_security_group_id | ID of the bastion host security group |
| bastion_security_group_arn | ARN of the bastion host security group |
| bastion_iam_role_id | ID of the bastion host IAM role |
| bastion_iam_role_arn | ARN of the bastion host IAM role |
| bastion_instance_profile_id | ID of the bastion host instance profile |
| bastion_instance_profile_arn | ARN of the bastion host instance profile |
| bastion_ssh_command | SSH command to connect to the bastion host |
| bastion_tunnel_command | SSH tunnel command to connect to RDS through the bastion host |

## Security Features

1. **SSH Hardening**:
   - Password authentication disabled
   - Idle timeout configured
   - Root login restricted

2. **Security Utilities**:
   - Fail2ban to prevent brute force attacks
   - Regular system updates

3. **Monitoring**:
   - CloudWatch Agent for system and application monitoring
   - CloudWatch Alarms for CPU and status checks

4. **IAM Security**:
   - Least privilege IAM role
   - Access only to required AWS resources

## Accessing RDS via Bastion Host

1. SSH to the bastion host:
   ```
   ssh -i /path/to/key.pem ec2-user@<BASTION_PUBLIC_IP>
   ```

2. Connect to the RDS instance using psql:
   ```
   psql -h <RDS_ENDPOINT> -U <DB_USERNAME> -d <DB_NAME>
   ```

3. Alternatively, create an SSH tunnel and connect locally:
   ```
   ssh -i /path/to/key.pem -L 5432:<RDS_ENDPOINT>:5432 ec2-user@<BASTION_PUBLIC_IP>
   ```
   Then connect locally:
   ```
   psql -h localhost -U <DB_USERNAME> -d <DB_NAME>
   ```

## Notes

- The bastion host is deployed in a public subnet with a public IP
- SSH access is restricted to specified CIDR blocks
- The instance is configured with Amazon Linux 2023
- PostgreSQL client is installed for database access
- CloudWatch alarms are configured for monitoring
- Consider implementing a schedule to start/stop the bastion host during non-business hours to reduce costs
