/**
 * # AWS Bastion Host Module Outputs
 *
 * Outputs from the AWS Bastion Host module
 */

output "bastion_instance_id" {
  description = "ID of the bastion host EC2 instance"
  value       = aws_instance.bastion.id
}

output "bastion_public_ip" {
  description = "Public IP address of the bastion host"
  value       = aws_instance.bastion.public_ip
}

output "bastion_private_ip" {
  description = "Private IP address of the bastion host"
  value       = aws_instance.bastion.private_ip
}

output "bastion_security_group_id" {
  description = "ID of the bastion host security group"
  value       = aws_security_group.bastion_sg.id
}

output "bastion_security_group_arn" {
  description = "ARN of the bastion host security group"
  value       = aws_security_group.bastion_sg.arn
}

output "bastion_iam_role_id" {
  description = "ID of the bastion host IAM role"
  value       = aws_iam_role.bastion_role.id
}

output "bastion_iam_role_arn" {
  description = "ARN of the bastion host IAM role"
  value       = aws_iam_role.bastion_role.arn
}

output "bastion_instance_profile_id" {
  description = "ID of the bastion host instance profile"
  value       = aws_iam_instance_profile.bastion_profile.id
}

output "bastion_instance_profile_arn" {
  description = "ARN of the bastion host instance profile"
  value       = aws_iam_instance_profile.bastion_profile.arn
}

output "bastion_ssh_command" {
  description = "SSH command to connect to the bastion host"
  value       = "ssh -i /path/to/key.pem ec2-user@${aws_instance.bastion.public_ip}"
}

output "bastion_tunnel_command" {
  description = "SSH tunnel command to connect to RDS through the bastion host"
  value       = "ssh -i /path/to/key.pem -L 5432:rds-endpoint:5432 ec2-user@${aws_instance.bastion.public_ip}"
}
