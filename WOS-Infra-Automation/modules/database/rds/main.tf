/**
 * # AWS RDS PostgreSQL Module
 *
 * This module creates an RDS PostgreSQL instance with associated resources including:
 * - DB Subnet Group
 * - Parameter Group
 * - IAM Role for Enhanced Monitoring
 * - Secrets Manager integration for credentials
 */

# RDS Subnet Group
resource "aws_db_subnet_group" "rds_subnet_group" {
  name        = "${var.environment}-wos-rds-subnet-group"
  description = "WasteOS RDS subnet group for ${var.environment} environment"
  subnet_ids  = var.private_db_subnet_ids

  tags = merge(
    var.tags,
    {
      Name = "${var.environment}-wos-rds-subnet-group"
    }
  )
}

# RDS Parameter Group
resource "aws_db_parameter_group" "postgres_params" {
  name        = "${var.environment}-wos-postgres-params"
  family      = "postgres${var.postgres_family_version}"
  description = "WasteOS PostgreSQL parameter group for ${var.environment} environment"

  # Memory settings - these are static parameters that require a reboot
  parameter {
    name         = "shared_buffers"
    value        = var.environment == "prod" ? "262144" : "131072"
    apply_method = "pending-reboot"
  }

  parameter {
    name         = "work_mem"
    value        = var.environment == "prod" ? "8192" : "4096"
    apply_method = "pending-reboot"
  }

  parameter {
    name         = "maintenance_work_mem"
    value        = var.environment == "prod" ? "131072" : "65536"
    apply_method = "pending-reboot"
  }

  parameter {
    name         = "effective_cache_size"
    value        = var.environment == "prod" ? "786432" : "393216"
    apply_method = "pending-reboot"
  }

  # Logging settings - these can be applied immediately
  parameter {
    name         = "log_connections"
    value        = "1"
    apply_method = "immediate"
  }

  parameter {
    name         = "log_disconnections"
    value        = "1"
    apply_method = "immediate"
  }

  parameter {
    name         = "log_statement"
    value        = var.environment == "prod" ? "mod" : "all"
    apply_method = "immediate"
  }

  parameter {
    name         = "log_min_duration_statement"
    value        = var.environment == "prod" ? "1000" : "500"
    apply_method = "immediate"
  }

  tags = merge(
    var.tags,
    {
      Name = "${var.environment}-wos-postgres-params"
    }
  )
}

# IAM Role for RDS Enhanced Monitoring
resource "aws_iam_role" "rds_monitoring_role" {
  count = var.enable_enhanced_monitoring ? 1 : 0

  name = "${var.environment}-wos-rds-monitoring-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action = "sts:AssumeRole",
        Effect = "Allow",
        Principal = {
          Service = "monitoring.rds.amazonaws.com"
        }
      }
    ]
  })

  tags = merge(
    var.tags,
    {
      Name = "${var.environment}-wos-rds-monitoring-role"
    }
  )
}

resource "aws_iam_role_policy_attachment" "rds_monitoring_attachment" {
  count = var.enable_enhanced_monitoring ? 1 : 0

  role       = aws_iam_role.rds_monitoring_role[0].name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonRDSEnhancedMonitoringRole"
}

# Random password generation
resource "random_password" "rds_password" {
  length           = 16
  special          = true
  override_special = "!#$%&*()-_=+[]{}<>:?"
}

# Store master password in AWS Secrets Manager
resource "aws_secretsmanager_secret" "rds_credentials" {
  name        = "${var.environment}/wos/database/credentials"
  description = "RDS PostgreSQL credentials for ${var.environment} environment"

  tags = merge(
    var.tags,
    {
      Name = "${var.environment}-wos-rds-credentials"
    }
  )
}

# RDS PostgreSQL Instance
resource "aws_db_instance" "postgres" {
  identifier     = "wos-${var.environment}-postgres"
  engine         = "postgres"
  engine_version = var.engine_version
  instance_class = var.instance_class

  # Storage configuration
  allocated_storage     = var.allocated_storage
  max_allocated_storage = var.max_allocated_storage
  storage_type          = var.storage_type
  storage_encrypted     = true
  kms_key_id            = var.kms_key_id

  # Database configuration
  db_name  = "wos_${var.environment}"
  username = "wos_${var.environment}_admin"
  password = random_password.rds_password.result
  port     = 5432

  # Network configuration
  db_subnet_group_name   = aws_db_subnet_group.rds_subnet_group.name
  vpc_security_group_ids = [var.rds_security_group_id]
  publicly_accessible    = false

  # Parameter and option groups
  parameter_group_name = aws_db_parameter_group.postgres_params.name

  # Backup and maintenance
  backup_retention_period = var.backup_retention_period
  backup_window           = var.backup_window
  maintenance_window      = var.maintenance_window

  # Monitoring and logging
  monitoring_interval                   = var.enable_enhanced_monitoring ? 60 : 0
  monitoring_role_arn                   = var.enable_enhanced_monitoring ? aws_iam_role.rds_monitoring_role[0].arn : null
  performance_insights_enabled          = var.enable_performance_insights
  performance_insights_retention_period = var.performance_insights_retention_period
  enabled_cloudwatch_logs_exports       = ["postgresql", "upgrade"]

  # High availability
  multi_az = var.multi_az

  # Deletion protection
  deletion_protection       = var.deletion_protection
  skip_final_snapshot       = var.skip_final_snapshot
  final_snapshot_identifier = var.skip_final_snapshot ? null : "wos-${var.environment}-postgres-final-${formatdate("YYYYMMDDhhmmss", timestamp())}"

  # Apply immediately for non-prod environments
  apply_immediately = var.apply_immediately

  tags = merge(
    var.tags,
    {
      Name = "wos-${var.environment}-postgres"
    }
  )

  # Ignore password changes managed by Secrets Manager and monitoring settings
  lifecycle {
    ignore_changes = [
      password,
      monitoring_interval,
      monitoring_role_arn,
      final_snapshot_identifier
    ]
  }
}

# Store credentials in Secrets Manager
resource "aws_secretsmanager_secret_version" "rds_credentials" {
  secret_id = aws_secretsmanager_secret.rds_credentials.id
  secret_string = jsonencode({
    username = aws_db_instance.postgres.username
    password = random_password.rds_password.result
    engine   = "postgres"
    host     = aws_db_instance.postgres.address
    port     = aws_db_instance.postgres.port
    dbname   = aws_db_instance.postgres.db_name
    uri      = "postgresql://${aws_db_instance.postgres.username}:${urlencode(random_password.rds_password.result)}@${aws_db_instance.postgres.address}:${aws_db_instance.postgres.port}/${aws_db_instance.postgres.db_name}"
  })
}

# Create a connection URI secret for applications
resource "aws_secretsmanager_secret" "rds_uri" {
  name        = "${var.environment}/wos/database/uri"
  description = "RDS PostgreSQL connection URI for ${var.environment} environment"

  tags = merge(
    var.tags,
    {
      Name = "${var.environment}-wos-rds-uri"
    }
  )
}

resource "aws_secretsmanager_secret_version" "rds_uri" {
  secret_id     = aws_secretsmanager_secret.rds_uri.id
  secret_string = "postgresql://${aws_db_instance.postgres.username}:${urlencode(random_password.rds_password.result)}@${aws_db_instance.postgres.address}:${aws_db_instance.postgres.port}/${aws_db_instance.postgres.db_name}"
}

# Optionally create a read replica
resource "aws_db_instance" "postgres_replica" {
  count = var.create_read_replica ? 1 : 0

  identifier          = "wos-${var.environment}-postgres-replica"
  instance_class      = var.replica_instance_class
  replicate_source_db = aws_db_instance.postgres.identifier

  # Network configuration
  publicly_accessible = false

  # Monitoring and logging
  monitoring_interval                   = var.enable_enhanced_monitoring ? 60 : 0
  monitoring_role_arn                   = var.enable_enhanced_monitoring ? aws_iam_role.rds_monitoring_role[0].arn : null
  performance_insights_enabled          = var.enable_performance_insights
  performance_insights_retention_period = var.performance_insights_retention_period
  enabled_cloudwatch_logs_exports       = ["postgresql", "upgrade"]

  # Backup and maintenance
  backup_retention_period = 0 # No backups for read replica

  # Apply immediately for non-prod environments
  apply_immediately = var.apply_immediately

  tags = merge(
    var.tags,
    {
      Name = "wos-${var.environment}-postgres-replica"
    }
  )

  # Ignore monitoring settings changes
  lifecycle {
    ignore_changes = [
      monitoring_interval,
      monitoring_role_arn
    ]
  }
}
