/**
 * # AWS RDS PostgreSQL Module Outputs
 *
 * Outputs from the RDS PostgreSQL module
 */

output "db_instance_id" {
  description = "The RDS instance ID"
  value       = aws_db_instance.postgres.id
}

output "db_instance_address" {
  description = "The address of the RDS instance"
  value       = aws_db_instance.postgres.address
}

output "db_instance_endpoint" {
  description = "The connection endpoint of the RDS instance"
  value       = aws_db_instance.postgres.endpoint
}

output "db_instance_arn" {
  description = "The ARN of the RDS instance"
  value       = aws_db_instance.postgres.arn
}

output "db_instance_name" {
  description = "The database name"
  value       = aws_db_instance.postgres.db_name
}

output "db_instance_username" {
  description = "The master username for the database"
  value       = aws_db_instance.postgres.username
  sensitive   = true
}

output "db_instance_port" {
  description = "The database port"
  value       = aws_db_instance.postgres.port
}

output "db_subnet_group_id" {
  description = "The DB subnet group ID"
  value       = aws_db_subnet_group.rds_subnet_group.id
}

output "db_parameter_group_id" {
  description = "The DB parameter group ID"
  value       = aws_db_parameter_group.postgres_params.id
}

output "db_monitoring_role_arn" {
  description = "The ARN of the RDS monitoring IAM role"
  value       = var.enable_enhanced_monitoring ? aws_iam_role.rds_monitoring_role[0].arn : null
}

output "db_credentials_secret_arn" {
  description = "The ARN of the Secrets Manager secret containing database credentials"
  value       = aws_secretsmanager_secret.rds_credentials.arn
}

output "db_uri_secret_arn" {
  description = "The ARN of the Secrets Manager secret containing the database URI"
  value       = aws_secretsmanager_secret.rds_uri.arn
}

output "db_replica_instance_id" {
  description = "The ID of the RDS read replica instance"
  value       = var.create_read_replica ? aws_db_instance.postgres_replica[0].id : null
}

output "db_replica_instance_address" {
  description = "The address of the RDS read replica instance"
  value       = var.create_read_replica ? aws_db_instance.postgres_replica[0].address : null
}

output "db_replica_instance_endpoint" {
  description = "The connection endpoint of the RDS read replica instance"
  value       = var.create_read_replica ? aws_db_instance.postgres_replica[0].endpoint : null
}
