# AWS RDS PostgreSQL Module

This module creates an AWS RDS PostgreSQL instance with associated resources including:
- DB Subnet Group
- Parameter Group
- IAM Role for Enhanced Monitoring
- Secrets Manager integration for credentials
- Optional Read Replica

## Usage

```hcl
module "rds" {
  source = "../../modules/database/rds"

  environment           = "dev"
  private_db_subnet_ids = ["subnet-12345678", "subnet-87654321"]
  rds_security_group_id = "sg-12345678"
  
  # PostgreSQL configuration
  postgres_family_version = "17"
  engine_version          = "17.4"
  instance_class          = "db.t4g.large"
  
  # Storage configuration
  allocated_storage     = 50
  max_allocated_storage = 100
  storage_type          = "gp3"
  
  # Backup and maintenance
  backup_retention_period = 7
  backup_window           = "03:00-04:00"
  maintenance_window      = "sun:04:30-sun:05:30"
  
  # High availability
  multi_az = false
  
  # Monitoring
  enable_enhanced_monitoring = true
  enable_performance_insights = true
  
  # Read replica
  create_read_replica    = false
  replica_instance_class = "db.t4g.large"
  
  tags = {
    Project     = "WasteOS"
    Environment = "dev"
    ManagedBy   = "Terraform"
  }
}
```

## Requirements

| Name | Version |
|------|---------|
| terraform | >= 1.0.0 |
| aws | ~> 5.0 |

## Providers

| Name | Version |
|------|---------|
| aws | ~> 5.0 |
| random | ~> 3.0 |

## Resources

| Name | Type |
|------|------|
| aws_db_subnet_group.rds_subnet_group | resource |
| aws_db_parameter_group.postgres_params | resource |
| aws_iam_role.rds_monitoring_role | resource |
| aws_iam_role_policy_attachment.rds_monitoring_attachment | resource |
| aws_db_instance.postgres | resource |
| aws_db_instance.postgres_replica | resource |
| aws_secretsmanager_secret.rds_credentials | resource |
| aws_secretsmanager_secret_version.rds_credentials | resource |
| aws_secretsmanager_secret.rds_uri | resource |
| aws_secretsmanager_secret_version.rds_uri | resource |
| random_password.rds_password | resource |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| environment | Environment name (e.g., dev, uat, prod) | `string` | n/a | yes |
| private_db_subnet_ids | List of private database subnet IDs | `list(string)` | n/a | yes |
| rds_security_group_id | ID of the RDS security group | `string` | n/a | yes |
| postgres_family_version | PostgreSQL parameter group family version (e.g., 17) | `string` | `"17"` | no |
| engine_version | PostgreSQL engine version | `string` | `"17.4"` | no |
| instance_class | RDS instance class | `string` | `"db.t4g.large"` | no |
| allocated_storage | Allocated storage in GB | `number` | `50` | no |
| max_allocated_storage | Maximum allocated storage in GB for autoscaling | `number` | `100` | no |
| storage_type | Storage type (gp2, gp3, io1) | `string` | `"gp3"` | no |
| kms_key_id | KMS key ID for storage encryption | `string` | `null` | no |
| backup_retention_period | Backup retention period in days | `number` | `7` | no |
| backup_window | Preferred backup window | `string` | `"03:00-04:00"` | no |
| maintenance_window | Preferred maintenance window | `string` | `"sun:04:30-sun:05:30"` | no |
| multi_az | Whether to deploy a multi-AZ RDS instance | `bool` | `false` | no |
| deletion_protection | Whether to enable deletion protection | `bool` | `true` | no |
| skip_final_snapshot | Whether to skip the final snapshot when deleting the instance | `bool` | `false` | no |
| apply_immediately | Whether to apply changes immediately or during the maintenance window | `bool` | `true` | no |
| enable_enhanced_monitoring | Whether to enable enhanced monitoring | `bool` | `true` | no |
| enable_performance_insights | Whether to enable Performance Insights | `bool` | `true` | no |
| performance_insights_retention_period | Performance Insights retention period in days | `number` | `7` | no |
| create_read_replica | Whether to create a read replica | `bool` | `false` | no |
| replica_instance_class | Instance class for the read replica | `string` | `"db.t4g.large"` | no |
| tags | A map of tags to add to all resources | `map(string)` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| db_instance_id | The RDS instance ID |
| db_instance_address | The address of the RDS instance |
| db_instance_endpoint | The connection endpoint of the RDS instance |
| db_instance_arn | The ARN of the RDS instance |
| db_instance_name | The database name |
| db_instance_username | The master username for the database |
| db_instance_port | The database port |
| db_subnet_group_id | The DB subnet group ID |
| db_parameter_group_id | The DB parameter group ID |
| db_monitoring_role_arn | The ARN of the RDS monitoring IAM role |
| db_credentials_secret_arn | The ARN of the Secrets Manager secret containing database credentials |
| db_uri_secret_arn | The ARN of the Secrets Manager secret containing the database URI |
| db_replica_instance_id | The ID of the RDS read replica instance |
| db_replica_instance_address | The address of the RDS read replica instance |
| db_replica_instance_endpoint | The connection endpoint of the RDS read replica instance |
