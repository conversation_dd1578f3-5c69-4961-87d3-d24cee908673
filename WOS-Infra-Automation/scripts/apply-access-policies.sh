#!/bin/bash

# Script to create and apply access policies to a user
# This script addresses explicit deny issues for RDS and EC2 operations

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# Configuration
USER_ARN="arn:aws:iam::028864969427:user/<EMAIL>"
USER_NAME="<EMAIL>"
RDS_POLICY_NAME="WOS-RDS-Access-Policy"
EC2_POLICY_NAME="WOS-EC2-Access-Policy"
BEDROCK_POLICY_NAME="WOS-Bedrock-Access-Policy"
RDS_POLICY_FILE="policies/rds-access-policy.json"
EC2_POLICY_FILE="policies/ec2-access-policy.json"
BEDROCK_POLICY_FILE="policies/bedrock-access-policy.json"

echo -e "${YELLOW}Creating and applying access policies for user: ${USER_NAME}${NC}"

# Function to create or update a policy
create_or_update_policy() {
    local policy_name=$1
    local policy_file=$2
    local description=$3

    # Check if policy file exists
    if [ ! -f "$policy_file" ]; then
        echo -e "${RED}Policy file not found: $policy_file${NC}"
        return 1
    fi

    # Check if the policy already exists
    local existing_policy=$(aws iam list-policies --query "Policies[?PolicyName=='$policy_name'].Arn" --output text)

    if [ -z "$existing_policy" ]; then
        echo -e "${YELLOW}Creating new policy: $policy_name${NC}"

        # Create the policy
        local policy_arn=$(aws iam create-policy \
            --policy-name "$policy_name" \
            --policy-document file://$policy_file \
            --description "$description" \
            --query 'Policy.Arn' \
            --output text)

        if [ $? -ne 0 ]; then
            echo -e "${RED}Failed to create policy${NC}"
            return 1
        fi

        echo -e "${GREEN}Policy created successfully: $policy_arn${NC}"
    else
        echo -e "${YELLOW}Policy already exists: $existing_policy${NC}"
        local policy_arn=$existing_policy

        # Update the existing policy
        echo -e "${YELLOW}Updating existing policy...${NC}"
        aws iam create-policy-version \
            --policy-arn "$policy_arn" \
            --policy-document file://$policy_file \
            --set-as-default

        if [ $? -ne 0 ]; then
            echo -e "${RED}Failed to update policy${NC}"
            return 1
        fi

        echo -e "${GREEN}Policy updated successfully${NC}"
    fi

    # Check if the policy is already attached to the user
    local attached_policy=$(aws iam list-attached-user-policies --user-name "$USER_NAME" --query "AttachedPolicies[?PolicyArn=='$policy_arn'].PolicyArn" --output text)

    if [ -z "$attached_policy" ]; then
        echo -e "${YELLOW}Attaching policy to user: $USER_NAME${NC}"

        # Attach the policy to the user
        aws iam attach-user-policy \
            --user-name "$USER_NAME" \
            --policy-arn "$policy_arn"

        if [ $? -ne 0 ]; then
            echo -e "${RED}Failed to attach policy to user${NC}"
            return 1
        fi

        echo -e "${GREEN}Policy attached successfully to user: $USER_NAME${NC}"
    else
        echo -e "${GREEN}Policy is already attached to user: $USER_NAME${NC}"
    fi

    return 0
}

# Apply RDS access policy
echo -e "\n${YELLOW}Applying RDS access policy...${NC}"
create_or_update_policy "$RDS_POLICY_NAME" "$RDS_POLICY_FILE" "Policy to allow RDS describe operations for WasteOS platform"

# Apply EC2 access policy
echo -e "\n${YELLOW}Applying EC2 access policy...${NC}"
create_or_update_policy "$EC2_POLICY_NAME" "$EC2_POLICY_FILE" "Policy to allow EC2 describe operations for WasteOS platform"

# Apply Bedrock access policy
echo -e "\n${YELLOW}Applying Amazon Bedrock access policy...${NC}"
create_or_update_policy "$BEDROCK_POLICY_NAME" "$BEDROCK_POLICY_FILE" "Policy to allow Amazon Bedrock operations for WasteOS platform"

echo -e "\n${GREEN}Access policies setup completed!${NC}"
echo -e "${YELLOW}Note: If there's an explicit deny in an SCP (Service Control Policy) or in another policy with higher precedence, these policies may not override it.${NC}"
echo -e "${YELLOW}In that case, you'll need to modify the policy with the explicit deny statement.${NC}"
