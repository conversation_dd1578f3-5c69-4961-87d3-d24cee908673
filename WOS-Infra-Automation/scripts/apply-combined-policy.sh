#!/bin/bash

# <PERSON>ript to create and apply a combined access policy to a user
# This script addresses explicit deny issues for RDS, EC2, and Bedrock operations

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# Configuration
USER_ARN="arn:aws:iam::028864969427:user/<EMAIL>"
USER_NAME="<EMAIL>"
POLICY_NAME="WOS-Combined-Access-Policy"
POLICY_FILE="policies/combined-access-policy.json"

echo -e "${YELLOW}Creating and applying combined access policy for user: ${USER_NAME}${NC}"

# Check if policy file exists
if [ ! -f "$POLICY_FILE" ]; then
    echo -e "${RED}Policy file not found: $POLICY_FILE${NC}"
    exit 1
fi

# Check if the policy already exists
EXISTING_POLICY=$(aws iam list-policies --query "Policies[?PolicyName=='$POLICY_NAME'].Arn" --output text)

if [ -z "$EXISTING_POLICY" ]; then
    echo -e "${YELLOW}Creating new policy: $POLICY_NAME${NC}"
    
    # Create the policy
    POLICY_ARN=$(aws iam create-policy \
        --policy-name "$POLICY_NAME" \
        --policy-document file://$POLICY_FILE \
        --description "Combined policy to allow RDS, EC2, and Bedrock operations for WasteOS platform" \
        --query 'Policy.Arn' \
        --output text)
    
    if [ $? -ne 0 ]; then
        echo -e "${RED}Failed to create policy${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}Policy created successfully: $POLICY_ARN${NC}"
else
    echo -e "${YELLOW}Policy already exists: $EXISTING_POLICY${NC}"
    POLICY_ARN=$EXISTING_POLICY
    
    # Update the existing policy
    echo -e "${YELLOW}Updating existing policy...${NC}"
    aws iam create-policy-version \
        --policy-arn "$POLICY_ARN" \
        --policy-document file://$POLICY_FILE \
        --set-as-default
    
    if [ $? -ne 0 ]; then
        echo -e "${RED}Failed to update policy${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}Policy updated successfully${NC}"
fi

# Check if the policy is already attached to the user
ATTACHED_POLICY=$(aws iam list-attached-user-policies --user-name "$USER_NAME" --query "AttachedPolicies[?PolicyArn=='$POLICY_ARN'].PolicyArn" --output text)

if [ -z "$ATTACHED_POLICY" ]; then
    echo -e "${YELLOW}Attaching policy to user: $USER_NAME${NC}"
    
    # Attach the policy to the user
    aws iam attach-user-policy \
        --user-name "$USER_NAME" \
        --policy-arn "$POLICY_ARN"
    
    if [ $? -ne 0 ]; then
        echo -e "${RED}Failed to attach policy to user${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}Policy attached successfully to user: $USER_NAME${NC}"
else
    echo -e "${GREEN}Policy is already attached to user: $USER_NAME${NC}"
fi

# Check for and detach old individual policies if they exist
echo -e "\n${YELLOW}Checking for old individual policies...${NC}"

# Function to detach and optionally delete a policy
detach_policy() {
    local policy_name=$1
    local delete_policy=$2
    
    # Check if the policy exists
    local policy_arn=$(aws iam list-policies --query "Policies[?PolicyName=='$policy_name'].Arn" --output text)
    
    if [ -n "$policy_arn" ]; then
        # Check if the policy is attached to the user
        local is_attached=$(aws iam list-attached-user-policies --user-name "$USER_NAME" --query "AttachedPolicies[?PolicyArn=='$policy_arn'].PolicyArn" --output text)
        
        if [ -n "$is_attached" ]; then
            echo -e "${YELLOW}Detaching policy $policy_name from user...${NC}"
            
            # Detach the policy from the user
            aws iam detach-user-policy \
                --user-name "$USER_NAME" \
                --policy-arn "$policy_arn"
            
            if [ $? -ne 0 ]; then
                echo -e "${RED}Failed to detach policy $policy_name${NC}"
            else
                echo -e "${GREEN}Policy $policy_name detached successfully${NC}"
            fi
        else
            echo -e "${GREEN}Policy $policy_name is not attached to user${NC}"
        fi
        
        # Delete the policy if requested
        if [ "$delete_policy" = true ] && [ -n "$policy_arn" ]; then
            echo -e "${YELLOW}Deleting policy $policy_name...${NC}"
            
            # List all policy versions
            local versions=$(aws iam list-policy-versions --policy-arn "$policy_arn" --query "Versions[?IsDefaultVersion==\`false\`].VersionId" --output text)
            
            # Delete non-default versions first
            for version in $versions; do
                aws iam delete-policy-version --policy-arn "$policy_arn" --version-id "$version"
            done
            
            # Delete the policy
            aws iam delete-policy --policy-arn "$policy_arn"
            
            if [ $? -ne 0 ]; then
                echo -e "${RED}Failed to delete policy $policy_name${NC}"
            else
                echo -e "${GREEN}Policy $policy_name deleted successfully${NC}"
            fi
        fi
    else
        echo -e "${GREEN}Policy $policy_name does not exist${NC}"
    fi
}

# Detach old policies but don't delete them (set second parameter to true to delete)
detach_policy "WOS-RDS-Access-Policy" false
detach_policy "WOS-EC2-Access-Policy" false
detach_policy "WOS-Bedrock-Access-Policy" false

echo -e "\n${GREEN}Combined access policy setup completed successfully!${NC}"
echo -e "${YELLOW}Note: If there's an explicit deny in an SCP (Service Control Policy) or in another policy with higher precedence, this policy may not override it.${NC}"
echo -e "${YELLOW}In that case, you'll need to modify the policy with the explicit deny statement.${NC}"
