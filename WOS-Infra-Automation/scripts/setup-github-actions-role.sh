#!/bin/bash
# Script to set up the AWS IAM role for GitHub Actions

# Variables
ROLE_NAME="github-actions-terraform-role"
ACCOUNT_ID="************"
REPO_OWNER="sembwaste"
REPO_NAME="WOS-Infra-Automation"
REGION="ap-southeast-1"

# Create the OIDC provider for GitHub Actions if it doesn't exist
if ! aws iam list-open-id-connect-providers | grep -q "arn:aws:iam::${ACCOUNT_ID}:oidc-provider/token.actions.githubusercontent.com"; then
  echo "Creating OIDC provider for GitHub Actions..."
  aws iam create-open-id-connect-provider \
    --url "https://token.actions.githubusercontent.com" \
    --client-id-list "sts.amazonaws.com" \
    --thumbprint-list "6938fd4d98bab03faadb97b34396831e3780aea1"
fi

# Create the trust policy document
cat > trust-policy.json << EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Federated": "arn:aws:iam::${ACCOUNT_ID}:oidc-provider/token.actions.githubusercontent.com"
      },
      "Action": "sts:AssumeRoleWithWebIdentity",
      "Condition": {
        "StringEquals": {
          "token.actions.githubusercontent.com:aud": "sts.amazonaws.com"
        },
        "StringLike": {
          "token.actions.githubusercontent.com:sub": "repo:${REPO_OWNER}/${REPO_NAME}:*"
        }
      }
    }
  ]
}
EOF

# Create the IAM role or update it if it already exists
if aws iam get-role --role-name ${ROLE_NAME} 2>/dev/null; then
  echo "Updating existing IAM role: ${ROLE_NAME}"
  aws iam update-assume-role-policy \
    --role-name ${ROLE_NAME} \
    --policy-document file://trust-policy.json
else
  echo "Creating new IAM role: ${ROLE_NAME}"
  aws iam create-role \
    --role-name ${ROLE_NAME} \
    --assume-role-policy-document file://trust-policy.json
fi

# Create the permissions policy document
cat > permissions-policy.json << EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "s3:*",
        "dynamodb:*",
        "ec2:*",
        "ecs:*",
        "ecr:*",
        "elasticloadbalancing:*",
        "iam:*",
        "kms:*",
        "logs:*",
        "rds:*",
        "route53:*",
        "secretsmanager:*",
        "ssm:*",
        "sns:*",
        "sqs:*",
        "cloudwatch:*",
        "elasticache:*",
        "application-autoscaling:*"
      ],
      "Resource": "*"
    }
  ]
}
EOF

# Create the permissions policy or update it if it already exists
POLICY_ARN="arn:aws:iam::${ACCOUNT_ID}:policy/GithubActionsTerraformPolicy"
if aws iam get-policy --policy-arn ${POLICY_ARN} 2>/dev/null; then
  echo "Updating existing IAM policy: GithubActionsTerraformPolicy"
  POLICY_VERSION=$(aws iam list-policy-versions --policy-arn ${POLICY_ARN} --query 'Versions[0].VersionId' --output text)
  aws iam create-policy-version \
    --policy-arn ${POLICY_ARN} \
    --policy-document file://permissions-policy.json \
    --set-as-default
else
  echo "Creating new IAM policy: GithubActionsTerraformPolicy"
  aws iam create-policy \
    --policy-name GithubActionsTerraformPolicy \
    --policy-document file://permissions-policy.json
fi

# Attach the policy to the role
echo "Attaching policy to role..."
aws iam attach-role-policy \
  --role-name ${ROLE_NAME} \
  --policy-arn ${POLICY_ARN}

# Clean up temporary files
rm -f trust-policy.json permissions-policy.json

echo "GitHub Actions IAM role setup complete!"
echo "Role ARN: arn:aws:iam::${ACCOUNT_ID}:role/${ROLE_NAME}"
echo ""
echo "Make sure to configure the following GitHub repository secrets:"
echo "AWS_ROLE_ARN: arn:aws:iam::${ACCOUNT_ID}:role/${ROLE_NAME}"
echo "AWS_REGION: ${REGION}"
