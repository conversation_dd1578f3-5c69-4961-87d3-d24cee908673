#!/bin/bash

# Script to apply the updated Developers policy to users
# This script grants necessary permissions to developers while excluding Bedrock access

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# Configuration
POLICY_NAME="WOS-Developers-Policy"
POLICY_FILE="policies/Developers-Updated.json"

# Function to display usage information
usage() {
    echo "Usage: $0 <username>"
    echo "Example: $0 <EMAIL>"
    exit 1
}

# Check if username is provided
if [ $# -ne 1 ]; then
    usage
fi

USER_NAME="$1"

echo -e "${YELLOW}Creating and applying Developers policy for user: ${USER_NAME}${NC}"

# Check if policy file exists
if [ ! -f "$POLICY_FILE" ]; then
    echo -e "${RED}Policy file not found: $POLICY_FILE${NC}"
    exit 1
fi

# Check if the user exists
USER_EXISTS=$(aws iam get-user --user-name "$USER_NAME" --query 'User.UserName' --output text 2>/dev/null)

if [ -z "$USER_EXISTS" ]; then
    echo -e "${RED}User $USER_NAME does not exist${NC}"
    exit 1
fi

# Check if the policy already exists
EXISTING_POLICY=$(aws iam list-policies --query "Policies[?PolicyName=='$POLICY_NAME'].Arn" --output text)

if [ -z "$EXISTING_POLICY" ]; then
    echo -e "${YELLOW}Creating new policy: $POLICY_NAME${NC}"
    
    # Create the policy
    POLICY_ARN=$(aws iam create-policy \
        --policy-name "$POLICY_NAME" \
        --policy-document file://$POLICY_FILE \
        --description "Policy for developers in the WasteOS platform (excludes Bedrock access)" \
        --query 'Policy.Arn' \
        --output text)
    
    if [ $? -ne 0 ]; then
        echo -e "${RED}Failed to create policy${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}Policy created successfully: $POLICY_ARN${NC}"
else
    echo -e "${YELLOW}Policy already exists: $EXISTING_POLICY${NC}"
    POLICY_ARN=$EXISTING_POLICY
    
    # Update the existing policy
    echo -e "${YELLOW}Updating existing policy...${NC}"
    aws iam create-policy-version \
        --policy-arn "$POLICY_ARN" \
        --policy-document file://$POLICY_FILE \
        --set-as-default
    
    if [ $? -ne 0 ]; then
        echo -e "${RED}Failed to update policy${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}Policy updated successfully${NC}"
fi

# Check if the policy is already attached to the user
ATTACHED_POLICY=$(aws iam list-attached-user-policies --user-name "$USER_NAME" --query "AttachedPolicies[?PolicyArn=='$POLICY_ARN'].PolicyArn" --output text)

if [ -z "$ATTACHED_POLICY" ]; then
    echo -e "${YELLOW}Attaching policy to user: $USER_NAME${NC}"
    
    # Attach the policy to the user
    aws iam attach-user-policy \
        --user-name "$USER_NAME" \
        --policy-arn "$POLICY_ARN"
    
    if [ $? -ne 0 ]; then
        echo -e "${RED}Failed to attach policy to user${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}Policy attached successfully to user: $USER_NAME${NC}"
else
    echo -e "${GREEN}Policy is already attached to user: $USER_NAME${NC}"
fi

# Check for and detach any Bedrock-specific policies
echo -e "\n${YELLOW}Checking for Bedrock-specific policies...${NC}"

# Function to detach a policy if it exists and is attached
detach_policy() {
    local policy_name=$1
    
    # Check if the policy exists
    local policy_arn=$(aws iam list-policies --query "Policies[?PolicyName=='$policy_name'].Arn" --output text)
    
    if [ -n "$policy_arn" ]; then
        # Check if the policy is attached to the user
        local is_attached=$(aws iam list-attached-user-policies --user-name "$USER_NAME" --query "AttachedPolicies[?PolicyArn=='$policy_arn'].PolicyArn" --output text)
        
        if [ -n "$is_attached" ]; then
            echo -e "${YELLOW}Detaching policy $policy_name from user...${NC}"
            
            # Detach the policy from the user
            aws iam detach-user-policy \
                --user-name "$USER_NAME" \
                --policy-arn "$policy_arn"
            
            if [ $? -ne 0 ]; then
                echo -e "${RED}Failed to detach policy $policy_name${NC}"
            else
                echo -e "${GREEN}Policy $policy_name detached successfully${NC}"
            fi
        else
            echo -e "${GREEN}Policy $policy_name is not attached to user${NC}"
        fi
    else
        echo -e "${GREEN}Policy $policy_name does not exist${NC}"
    fi
}

# Detach Bedrock-specific policies
detach_policy "WOS-Bedrock-Access-Policy"
detach_policy "WOS-Combined-Access-Policy"

echo -e "\n${GREEN}Developers policy setup completed successfully!${NC}"
echo -e "${YELLOW}Note: The user now has all necessary permissions for development work except for Bedrock access.${NC}"
echo -e "${YELLOW}If the user needs to access other AWS services, additional policies may need to be attached.${NC}"
