{"Version": "2012-10-17", "Statement": [{"Sid": "ConsoleAccess", "Effect": "Allow", "Action": ["aws-portal:ViewBilling", "aws-portal:ViewUsage", "aws-portal:ViewAccount", "budgets:<PERSON><PERSON><PERSON><PERSON>", "ce:Get*", "ce:Describe*", "ce:List*", "support:DescribeTrustedAdvisorChecks", "support:DescribeTrustedAdvisorCheckResult", "support:DescribeTrustedAdvisorCheckSummaries"], "Resource": "*"}, {"Sid": "CodeCommitAccess", "Effect": "Allow", "Action": ["codecommit:BatchGet*", "codecommit:BatchDescribe*", "codecommit:Create*", "codecommit:DeleteBranch", "codecommit:DeleteFile", "codecommit:Get*", "codecommit:List*", "codecommit:Describe*", "codecommit:GitPull", "codecommit:GitPush", "codecommit:Merge*", "codecommit:Put*", "codecommit:Post*", "codecommit:Test*", "codecommit:Update*"], "Resource": "*"}, {"Sid": "S3Access", "Effect": "Allow", "Action": ["s3:GetObject", "s3:GetObjectVersion", "s3:GetObjectVersionTagging", "s3:GetObjectTagging", "s3:GetObjectAcl", "s3:GetBucketLocation", "s3:GetBucketPolicy", "s3:GetBucketVersioning", "s3:GetBucketAcl", "s3:GetBucketCORS", "s3:GetBucketWebsite", "s3:GetBucketVersioning", "s3:GetAccelerateConfiguration", "s3:GetBucketRequestPayment", "s3:GetBucketLogging", "s3:GetLifecycleConfiguration", "s3:GetReplicationConfiguration", "s3:GetEncryptionConfiguration", "s3:GetBucketObjectLockConfiguration", "s3:ListBucket", "s3:ListBucketVersions", "s3:ListAllMyBuckets", "s3:PutObject", "s3:PutObjectTagging", "s3:PutObjectVersionTagging", "s3:DeleteObject", "s3:DeleteObjectVersion", "s3:DeleteObjectTagging", "s3:DeleteObjectVersionTagging"], "Resource": "*"}, {"Sid": "CodeBuildAccess", "Effect": "Allow", "Action": ["codebuild:BatchGet*", "codebuild:GetResourcePolicy", "codebuild:List*", "codebuild:DescribeTestCases", "codebuild:StartBuild", "codebuild:StopBuild", "codebuild:RetryBuild"], "Resource": "*"}, {"Sid": "CloudWatchAccess", "Effect": "Allow", "Action": ["logs:GetLogEvents", "logs:GetLogRecord", "logs:GetLogGroupFields", "logs:GetQueryResults", "logs:DescribeLogGroups", "logs:DescribeLogStreams", "logs:DescribeQueries", "logs:StartQuery", "logs:StopQuery", "logs:TestMetricFilter", "logs:FilterLogEvents", "cloudwatch:Describe*", "cloudwatch:Get*", "cloudwatch:List*"], "Resource": "*"}, {"Sid": "IAMReadOnlyAccess", "Effect": "Allow", "Action": ["iam:Get<PERSON>ser", "iam:GetRole", "iam:ListUsers", "iam:ListRoles", "iam:ListGroups", "iam:ListPolicies", "iam:GetPolicy", "iam:GetPolicyVersion", "iam:ListAttachedUserPolicies", "iam:ListAttachedGroupPolicies", "iam:ListAttachedRolePolicies", "iam:ListUserPolicies", "iam:ListGroupPolicies", "iam:ListRolePolicies", "iam:Get<PERSON><PERSON>unt<PERSON><PERSON><PERSON><PERSON>", "iam:GetAccountPasswordPolicy"], "Resource": "*"}, {"Sid": "MFAManagement", "Effect": "Allow", "Action": ["iam:CreateVirtualMFADevice", "iam:DeleteVirtualMFADevice", "iam:EnableMFADevice", "iam:ResyncMFADevice", "iam:DeactivateMFADevice", "iam:ListMFADevices", "iam:ListVirtualMFADevices", "iam:ChangePassword"], "Resource": ["arn:aws:iam::*:mfa/${aws:username}", "arn:aws:iam::*:user/${aws:username}"]}, {"Sid": "DeveloperServicesReadAccess", "Effect": "Allow", "Action": ["ec2:Describe*", "elasticloadbalancing:Describe*", "autoscaling:Describe*", "rds:Describe*", "rds:ListTagsForResource", "dynamodb:List*", "dynamodb:Describe*", "lambda:List*", "lambda:Get*", "apigateway:GET", "sns:List*", "sns:Get*", "sqs:List*", "sqs:Get*", "cloudformation:List*", "cloudformation:Describe*", "cloudformation:Get*", "cloudfront:List*", "cloudfront:Get*", "route53:List*", "route53:Get*"], "Resource": "*"}, {"Sid": "CodePipelineAccess", "Effect": "Allow", "Action": ["codepipeline:List*", "codepipeline:Get*", "codepipeline:StartPipelineExecution", "codepipeline:StopPipelineExecution", "codepipeline:RetryStageExecution"], "Resource": "*"}]}