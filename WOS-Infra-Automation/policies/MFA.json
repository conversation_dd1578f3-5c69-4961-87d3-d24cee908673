{"Version": "2012-10-17", "Statement": [{"Sid": "AllowViewAccountInfo", "Effect": "Allow", "Action": ["iam:GetAccountPasswordPolicy", "iam:Get<PERSON><PERSON>unt<PERSON><PERSON><PERSON><PERSON>", "iam:ListVirtualMFADevices"], "Resource": "*"}, {"Sid": "AllowManageOwnPasswords", "Effect": "Allow", "Action": ["iam:ChangePassword", "iam:Get<PERSON>ser"], "Resource": "arn:aws:iam::*:user/${aws:username}"}, {"Sid": "AllowManageOwnAccessKeys", "Effect": "Allow", "Action": ["iam:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "iam:DeleteA<PERSON>ess<PERSON>ey", "iam:ListAccess<PERSON>eys", "iam:UpdateA<PERSON>ess<PERSON><PERSON>"], "Resource": "arn:aws:iam::*:user/${aws:username}"}, {"Sid": "AllowManageOwnSigningCertificates", "Effect": "Allow", "Action": ["iam:DeleteSigningCertificate", "iam:ListSigningCertificates", "iam:UpdateSigningCertificate", "iam:UploadSigningCertificate"], "Resource": "arn:aws:iam::*:user/${aws:username}"}, {"Sid": "AllowManageOwnSSHPublicKeys", "Effect": "Allow", "Action": ["iam:DeleteSSHPublicKey", "iam:GetSSHPublicKey", "iam:ListSSHPublicKeys", "iam:UpdateSSHPublicKey", "iam:UploadSSHPublicKey"], "Resource": "arn:aws:iam::*:user/${aws:username}"}, {"Sid": "AllowManageOwnGitCredentials", "Effect": "Allow", "Action": ["iam:CreateServiceSpecificCredential", "iam:DeleteServiceSpecificCredential", "iam:ListServiceSpecificCredentials", "iam:ResetServiceSpecificCredential", "iam:UpdateServiceSpecificCredential"], "Resource": "arn:aws:iam::*:user/${aws:username}"}, {"Sid": "AllowManageOwnVirtualMFADevice", "Effect": "Allow", "Action": "iam:CreateVirtualMFADevice", "Resource": "*"}, {"Sid": "AllowManageOwnExistingVirtualMFADevice", "Effect": "Allow", "Action": "iam:DeleteVirtualMFADevice", "Resource": "arn:aws:iam::*:mfa/${aws:username}"}, {"Sid": "AllowManageOwnUserMFA", "Effect": "Allow", "Action": ["iam:DeactivateMFADevice", "iam:EnableMFADevice", "iam:ListMFADevices", "iam:ResyncMFADevice"], "Resource": "arn:aws:iam::*:user/${aws:username}"}, {"Sid": "DenyAllExceptListedIfNoMFA", "Effect": "<PERSON><PERSON>", "NotAction": ["iam:CreateVirtualMFADevice", "iam:EnableMFADevice", "iam:Get<PERSON>ser", "iam:ListMFADevices", "iam:ListVirtualMFADevices", "iam:ResyncMFADevice", "iam:ChangePassword", "iam:GetAccountPasswordPolicy", "sts:GetSessionToken"], "Resource": "*", "Condition": {"BoolIfExists": {"aws:MultiFactorAuthPresent": "false"}}}]}