{"Version": "2012-10-17", "Statement": [{"Sid": "AllowBedrockAccess", "Effect": "Allow", "Action": ["bedrock:ListFoundationModels", "bedrock:GetFoundationModel", "bedrock:InvokeModel", "bedrock:InvokeModelWithResponseStream", "bedrock:ListCustomModels", "bedrock:GetCustomModel", "bedrock:ListModelCustomizationJobs", "bedrock:GetModelCustomizationJob", "bedrock:ListPrompts", "bedrock:GetPrompt", "bedrock:ListGuardrails", "bedrock:GetGuardrail", "bedrock:ApplyGuardrail", "bedrock:ListAgents", "bedrock:GetAgent", "bedrock:ListAgentAliases", "bedrock:GetAgentAlias", "bedrock:ListFlows", "bedrock:GetFlow", "bedrock:ListKnowledgeBases", "bedrock:GetKnowledgeBase"], "Resource": "*"}]}