{"Version": "2012-10-17", "Statement": [{"Sid": "AllowS3Access", "Effect": "Allow", "Action": ["s3:ListAllMyBuckets", "s3:GetBucketLocation", "s3:ListBucket", "s3:GetObject", "s3:PutObject", "s3:GetObjectVersion", "s3:DeleteObject", "s3:GetObjectTagging", "s3:PutObjectTagging"], "Resource": ["arn:aws:s3:::*", "arn:aws:s3:::*/*"]}, {"Sid": "AllowECRAccess", "Effect": "Allow", "Action": ["ecr:GetAuthorizationToken", "ecr:BatchCheckLayerAvailability", "ecr:GetDownloadUrlForLayer", "ecr:BatchGetImage", "ecr:DescribeRepositories", "ecr:ListImages", "ecr:DescribeImages", "ecr:InitiateLayerUpload", "ecr:UploadLayerPart", "ecr:CompleteLayerUpload", "ecr:PutImage", "ecr:CreateRepository", "ecr:TagResource"], "Resource": "*"}, {"Sid": "AllowRDSDescribeAccess", "Effect": "Allow", "Action": ["rds:DescribeDBInstances", "rds:DescribeDBClusters", "rds:DescribeGlobalClusters", "rds:DescribeDBParameterGroups", "rds:DescribeDBParameters", "rds:DescribeDBSubnetGroups", "rds:DescribeOptionGroups"], "Resource": "*"}, {"Sid": "AllowEC2DescribeAccess", "Effect": "Allow", "Action": ["ec2:DescribeInstances", "ec2:DescribeInstanceStatus", "ec2:DescribeInstanceTypes", "ec2:DescribeTags", "ec2:DescribeVolumes", "ec2:DescribeSecurityGroups", "ec2:DescribeNetworkInterfaces", "ec2:DescribeSubnets", "ec2:DescribeVpcs"], "Resource": "*"}, {"Sid": "AllowBedrockAccess", "Effect": "Allow", "Action": ["bedrock:ListFoundationModels", "bedrock:GetFoundationModel", "bedrock:GetFoundationModelAvailability", "bedrock:InvokeModel", "bedrock:InvokeModelWithResponseStream", "bedrock:ListCustomModels", "bedrock:GetCustomModel", "bedrock:ListModelCustomizationJobs", "bedrock:GetModelCustomizationJob", "bedrock:ListPrompts", "bedrock:GetPrompt", "bedrock:ListGuardrails", "bedrock:GetGuardrail", "bedrock:ApplyGuardrail", "bedrock:ListAgents", "bedrock:GetAgent", "bedrock:ListAgentAliases", "bedrock:GetAgentAlias", "bedrock:ListFlows", "bedrock:GetFlow", "bedrock:ListKnowledgeBases", "bedrock:GetKnowledgeBase"], "Resource": "*"}]}