{"Version": "2012-10-17", "Statement": [{"Sid": "ConsoleAccess", "Effect": "Allow", "Action": ["support:DescribeTrustedAdvisorChecks", "support:DescribeTrustedAdvisorCheckResult", "support:DescribeTrustedAdvisorCheckSummaries"], "Resource": "*"}, {"Sid": "S3Access", "Effect": "Allow", "Action": ["s3:GetObject", "s3:GetObjectVersion", "s3:GetObjectVersionTagging", "s3:GetObjectTagging", "s3:GetObjectAcl", "s3:GetBucketLocation", "s3:GetBucketPolicy", "s3:GetBucketVersioning", "s3:GetBucketAcl", "s3:GetBucketCORS", "s3:GetBucketWebsite", "s3:GetBucketVersioning", "s3:GetAccelerateConfiguration", "s3:GetBucketRequestPayment", "s3:GetBucketLogging", "s3:GetLifecycleConfiguration", "s3:GetReplicationConfiguration", "s3:GetEncryptionConfiguration", "s3:GetBucketObjectLockConfiguration", "s3:ListBucket", "s3:ListBucketVersions", "s3:ListAllMyBuckets", "s3:PutObject", "s3:PutObjectTagging", "s3:PutObjectVersionTagging", "s3:DeleteObject", "s3:DeleteObjectVersion", "s3:DeleteObjectTagging", "s3:DeleteObjectVersionTagging"], "Resource": "*"}, {"Sid": "CloudWatchAccess", "Effect": "Allow", "Action": ["logs:GetLogEvents", "logs:GetLogRecord", "logs:GetLogGroupFields", "logs:GetQueryResults", "logs:DescribeLogGroups", "logs:DescribeLogStreams", "logs:DescribeQueries", "logs:StartQuery", "logs:StopQuery", "logs:TestMetricFilter", "logs:FilterLogEvents", "cloudwatch:Describe*", "cloudwatch:Get*", "cloudwatch:List*"], "Resource": "*"}, {"Sid": "IAMReadOnlyAccess", "Effect": "Allow", "Action": ["iam:Get<PERSON>ser", "iam:GetRole", "iam:ListUsers", "iam:ListRoles", "iam:ListGroups", "iam:ListPolicies", "iam:GetPolicy", "iam:GetPolicyVersion", "iam:ListAttachedUserPolicies", "iam:ListAttachedGroupPolicies", "iam:ListAttachedRolePolicies", "iam:ListUserPolicies", "iam:ListGroupPolicies", "iam:ListRolePolicies", "iam:Get<PERSON><PERSON>unt<PERSON><PERSON><PERSON><PERSON>", "iam:GetAccountPasswordPolicy"], "Resource": "*"}, {"Sid": "MFAManagement", "Effect": "Allow", "Action": ["iam:CreateVirtualMFADevice", "iam:DeleteVirtualMFADevice", "iam:EnableMFADevice", "iam:ResyncMFADevice", "iam:DeactivateMFADevice", "iam:ListMFADevices", "iam:ListVirtualMFADevices", "iam:ChangePassword"], "Resource": ["arn:aws:iam::*:mfa/${aws:username}", "arn:aws:iam::*:user/${aws:username}"]}, {"Sid": "DeveloperServicesReadAccess", "Effect": "Allow", "Action": ["ec2:Describe*", "elasticloadbalancing:Describe*", "autoscaling:Describe*", "rds:Describe*", "rds:ListTagsForResource", "dynamodb:List*", "dynamodb:Describe*", "lambda:List*", "lambda:Get*", "apigateway:GET", "sns:List*", "sns:Get*", "sqs:List*", "sqs:Get*", "cloudformation:List*", "cloudformation:Describe*", "cloudformation:Get*", "cloudfront:List*", "cloudfront:Get*", "route53:List*", "route53:Get*"], "Resource": "*"}, {"Sid": "ECRAccess", "Effect": "Allow", "Action": ["ecr:GetAuthorizationToken", "ecr:BatchCheckLayerAvailability", "ecr:GetDownloadUrlForLayer", "ecr:BatchGetImage", "ecr:DescribeRepositories", "ecr:ListImages", "ecr:DescribeImages", "ecr:InitiateLayerUpload", "ecr:UploadLayerPart", "ecr:CompleteLayerUpload", "ecr:PutImage", "ecr:CreateRepository", "ecr:TagResource"], "Resource": "*"}, {"Sid": "ECSFullAccess", "Effect": "Allow", "Action": ["ecs:*", "application-autoscaling:DeleteScalingPolicy", "application-autoscaling:DeregisterScalableTarget", "application-autoscaling:DescribeScalableTargets", "application-autoscaling:DescribeScalingActivities", "application-autoscaling:DescribeScalingPolicies", "application-autoscaling:PutScalingPolicy", "application-autoscaling:RegisterScalableTarget", "cloudwatch:DeleteAlarms", "cloudwatch:DescribeAlarms", "cloudwatch:PutMetricAlarm", "iam:AttachRolePolicy", "iam:CreateRole", "iam:DeleteRole", "iam:DetachRolePolicy", "iam:GetRole", "iam:ListAttachedRolePolicies", "iam:ListRoles", "iam:PassRole"], "Resource": "*"}]}