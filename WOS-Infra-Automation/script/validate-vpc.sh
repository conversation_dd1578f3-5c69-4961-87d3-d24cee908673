#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print section headers
print_header() {
    echo -e "\n${BLUE}======================================================================${NC}"
    echo -e "${BLUE}  $1${NC}"
    echo -e "${BLUE}======================================================================${NC}"
}

# Function to print subsection headers
print_subheader() {
    echo -e "\n${CYAN}----------------------------------------------------------------------${NC}"
    echo -e "${CYAN}  $1${NC}"
    echo -e "${CYAN}----------------------------------------------------------------------${NC}"
}

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check if required commands exist
if ! command_exists aws; then
    echo -e "${RED}Error: AWS CLI is not installed. Please install it first.${NC}"
    exit 1
fi

if ! command_exists jq; then
    echo -e "${YELLOW}Warning: jq is not installed. Some output formatting may be limited.${NC}"
fi

# Check if AWS credentials are configured
print_header "Checking AWS Credentials"
if ! aws sts get-caller-identity &>/dev/null; then
    echo -e "${RED}Error: AWS credentials are not configured or are invalid.${NC}"
    echo -e "${YELLOW}Please run 'aws configure' or set up your credentials.${NC}"
    exit 1
else
    CALLER_IDENTITY=$(aws sts get-caller-identity)
    ACCOUNT_ID=$(echo "$CALLER_IDENTITY" | grep -o '"Account": "[^"]*' | cut -d'"' -f4)
    USER_ARN=$(echo "$CALLER_IDENTITY" | grep -o '"Arn": "[^"]*' | cut -d'"' -f4)
    
    echo -e "${GREEN}AWS credentials are valid.${NC}"
    echo -e "Account ID: ${ACCOUNT_ID}"
    echo -e "User ARN: ${USER_ARN}"
fi

# Get VPC ID by tag name
print_header "Retrieving VPC Information"
echo -e "${YELLOW}Searching for VPC with tag Name=wos-dev-vpc...${NC}"

VPC_ID=$(aws ec2 describe-vpcs --filters "Name=tag:Name,Values=wos-dev-vpc" --query "Vpcs[0].VpcId" --output text)

if [ "$VPC_ID" == "None" ] || [ -z "$VPC_ID" ]; then
    echo -e "${RED}Error: VPC with tag Name=wos-dev-vpc not found.${NC}"
    
    # Ask if user wants to provide VPC ID manually
    echo -e "${YELLOW}Do you want to provide a VPC ID manually? (y/n)${NC}"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        echo -e "${YELLOW}Enter VPC ID:${NC}"
        read -r VPC_ID
    else
        echo -e "${YELLOW}Listing all VPCs in the account...${NC}"
        aws ec2 describe-vpcs --query "Vpcs[*].[VpcId,Tags[?Key=='Name'].Value|[0],CidrBlock]" --output table
        exit 1
    fi
fi

echo -e "${GREEN}Found VPC: ${VPC_ID}${NC}"

# Get VPC details
print_subheader "VPC Details"
aws ec2 describe-vpcs --vpc-ids "$VPC_ID" --query "Vpcs[0]" | jq 'del(.Tags[] | select(.Key != "Name"))'

# Get Internet Gateway
print_subheader "Internet Gateway"
IGW_ID=$(aws ec2 describe-internet-gateways --filters "Name=attachment.vpc-id,Values=$VPC_ID" --query "InternetGateways[0].InternetGatewayId" --output text)

if [ "$IGW_ID" == "None" ] || [ -z "$IGW_ID" ]; then
    echo -e "${RED}No Internet Gateway attached to VPC ${VPC_ID}${NC}"
else
    echo -e "${GREEN}Internet Gateway: ${IGW_ID}${NC}"
    aws ec2 describe-internet-gateways --internet-gateway-ids "$IGW_ID" --query "InternetGateways[0].Tags[?Key=='Name'].Value" --output text
fi

# Get Subnets
print_subheader "Subnets"
echo -e "${YELLOW}Retrieving subnets for VPC ${VPC_ID}...${NC}"

# Public Subnets
echo -e "\n${CYAN}Public Subnets:${NC}"
aws ec2 describe-subnets --filters "Name=vpc-id,Values=$VPC_ID" "Name=tag:Name,Values=*public*" \
    --query "Subnets[*].[SubnetId,CidrBlock,AvailabilityZone,Tags[?Key=='Name'].Value|[0]]" --output table

# Private App Subnets
echo -e "\n${CYAN}Private App Subnets:${NC}"
aws ec2 describe-subnets --filters "Name=vpc-id,Values=$VPC_ID" "Name=tag:Name,Values=*private-app*" \
    --query "Subnets[*].[SubnetId,CidrBlock,AvailabilityZone,Tags[?Key=='Name'].Value|[0]]" --output table

# Private DB Subnets
echo -e "\n${CYAN}Private DB Subnets:${NC}"
aws ec2 describe-subnets --filters "Name=vpc-id,Values=$VPC_ID" "Name=tag:Name,Values=*private-db*" \
    --query "Subnets[*].[SubnetId,CidrBlock,AvailabilityZone,Tags[?Key=='Name'].Value|[0]]" --output table

# Get NAT Gateways
print_subheader "NAT Gateways"
echo -e "${YELLOW}Retrieving NAT Gateways for VPC ${VPC_ID}...${NC}"
NAT_GATEWAYS=$(aws ec2 describe-nat-gateways --filter "Name=vpc-id,Values=$VPC_ID" --query "NatGateways[*]")

if [ "$NAT_GATEWAYS" == "[]" ]; then
    echo -e "${RED}No NAT Gateways found for VPC ${VPC_ID}${NC}"
else
    aws ec2 describe-nat-gateways --filter "Name=vpc-id,Values=$VPC_ID" \
        --query "NatGateways[*].[NatGatewayId,SubnetId,State,Tags[?Key=='Name'].Value|[0]]" --output table
    
    # Get Elastic IPs associated with NAT Gateways
    echo -e "\n${CYAN}Elastic IPs for NAT Gateways:${NC}"
    NAT_EIP_IDS=$(aws ec2 describe-nat-gateways --filter "Name=vpc-id,Values=$VPC_ID" \
        --query "NatGateways[*].NatGatewayAddresses[0].AllocationId" --output text)
    
    for EIP_ID in $NAT_EIP_IDS; do
        aws ec2 describe-addresses --allocation-ids "$EIP_ID" \
            --query "Addresses[*].[AllocationId,PublicIp,Tags[?Key=='Name'].Value|[0]]" --output table
    done
fi

# Get Route Tables
print_subheader "Route Tables"
echo -e "${YELLOW}Retrieving Route Tables for VPC ${VPC_ID}...${NC}"

# Public Route Tables
echo -e "\n${CYAN}Public Route Tables:${NC}"
PUBLIC_RTS=$(aws ec2 describe-route-tables --filters "Name=vpc-id,Values=$VPC_ID" "Name=tag:Name,Values=*public*" \
    --query "RouteTables[*].[RouteTableId,Tags[?Key=='Name'].Value|[0]]" --output text)

if [ -z "$PUBLIC_RTS" ]; then
    echo -e "${RED}No Public Route Tables found${NC}"
else
    aws ec2 describe-route-tables --filters "Name=vpc-id,Values=$VPC_ID" "Name=tag:Name,Values=*public*" \
        --query "RouteTables[*].[RouteTableId,Tags[?Key=='Name'].Value|[0]]" --output table
    
    # Show routes for the first public route table
    FIRST_PUBLIC_RT=$(echo "$PUBLIC_RTS" | head -n 1 | cut -f1)
    echo -e "\n${CYAN}Routes for ${FIRST_PUBLIC_RT}:${NC}"
    aws ec2 describe-route-tables --route-table-ids "$FIRST_PUBLIC_RT" \
        --query "RouteTables[0].Routes[*].[DestinationCidrBlock,GatewayId,NatGatewayId,State]" --output table
fi

# Private App Route Tables
echo -e "\n${CYAN}Private App Route Tables:${NC}"
PRIVATE_APP_RTS=$(aws ec2 describe-route-tables --filters "Name=vpc-id,Values=$VPC_ID" "Name=tag:Name,Values=*private-app*" \
    --query "RouteTables[*].[RouteTableId,Tags[?Key=='Name'].Value|[0]]" --output text)

if [ -z "$PRIVATE_APP_RTS" ]; then
    echo -e "${RED}No Private App Route Tables found${NC}"
else
    aws ec2 describe-route-tables --filters "Name=vpc-id,Values=$VPC_ID" "Name=tag:Name,Values=*private-app*" \
        --query "RouteTables[*].[RouteTableId,Tags[?Key=='Name'].Value|[0]]" --output table
    
    # Show routes for the first private app route table
    FIRST_PRIVATE_APP_RT=$(echo "$PRIVATE_APP_RTS" | head -n 1 | cut -f1)
    echo -e "\n${CYAN}Routes for ${FIRST_PRIVATE_APP_RT}:${NC}"
    aws ec2 describe-route-tables --route-table-ids "$FIRST_PRIVATE_APP_RT" \
        --query "RouteTables[0].Routes[*].[DestinationCidrBlock,GatewayId,NatGatewayId,State]" --output table
fi

# Private DB Route Tables
echo -e "\n${CYAN}Private DB Route Tables:${NC}"
PRIVATE_DB_RTS=$(aws ec2 describe-route-tables --filters "Name=vpc-id,Values=$VPC_ID" "Name=tag:Name,Values=*private-db*" \
    --query "RouteTables[*].[RouteTableId,Tags[?Key=='Name'].Value|[0]]" --output text)

if [ -z "$PRIVATE_DB_RTS" ]; then
    echo -e "${RED}No Private DB Route Tables found${NC}"
else
    aws ec2 describe-route-tables --filters "Name=vpc-id,Values=$VPC_ID" "Name=tag:Name,Values=*private-db*" \
        --query "RouteTables[*].[RouteTableId,Tags[?Key=='Name'].Value|[0]]" --output table
    
    # Show routes for the first private db route table
    FIRST_PRIVATE_DB_RT=$(echo "$PRIVATE_DB_RTS" | head -n 1 | cut -f1)
    echo -e "\n${CYAN}Routes for ${FIRST_PRIVATE_DB_RT}:${NC}"
    aws ec2 describe-route-tables --route-table-ids "$FIRST_PRIVATE_DB_RT" \
        --query "RouteTables[0].Routes[*].[DestinationCidrBlock,GatewayId,NatGatewayId,State]" --output table
fi

# Get Security Groups
print_subheader "Security Groups"
echo -e "${YELLOW}Retrieving Security Groups for VPC ${VPC_ID}...${NC}"
aws ec2 describe-security-groups --filters "Name=vpc-id,Values=$VPC_ID" \
    --query "SecurityGroups[*].[GroupId,GroupName,Description]" --output table

# Get details for specific security groups
SG_TYPES=("alb" "eks" "rds" "redis" "vpc-endpoints")

for SG_TYPE in "${SG_TYPES[@]}"; do
    echo -e "\n${CYAN}${SG_TYPE} Security Group Rules:${NC}"
    SG_ID=$(aws ec2 describe-security-groups --filters "Name=vpc-id,Values=$VPC_ID" "Name=tag:Name,Values=*${SG_TYPE}*" \
        --query "SecurityGroups[0].GroupId" --output text)
    
    if [ "$SG_ID" == "None" ] || [ -z "$SG_ID" ]; then
        echo -e "${RED}No ${SG_TYPE} Security Group found${NC}"
    else
        echo -e "${GREEN}Security Group ID: ${SG_ID}${NC}"
        
        # Inbound rules
        echo -e "\n${CYAN}Inbound Rules:${NC}"
        aws ec2 describe-security-groups --group-ids "$SG_ID" \
            --query "SecurityGroups[0].IpPermissions[*].[IpProtocol,FromPort,ToPort,IpRanges[0].CidrIp,UserIdGroupPairs[0].GroupId]" --output table
        
        # Outbound rules
        echo -e "\n${CYAN}Outbound Rules:${NC}"
        aws ec2 describe-security-groups --group-ids "$SG_ID" \
            --query "SecurityGroups[0].IpPermissionsEgress[*].[IpProtocol,FromPort,ToPort,IpRanges[0].CidrIp]" --output table
    fi
done

# Get VPC Endpoints
print_subheader "VPC Endpoints"
echo -e "${YELLOW}Retrieving VPC Endpoints for VPC ${VPC_ID}...${NC}"
VPC_ENDPOINTS=$(aws ec2 describe-vpc-endpoints --filters "Name=vpc-id,Values=$VPC_ID" --query "VpcEndpoints")

if [ "$VPC_ENDPOINTS" == "[]" ]; then
    echo -e "${RED}No VPC Endpoints found for VPC ${VPC_ID}${NC}"
else
    aws ec2 describe-vpc-endpoints --filters "Name=vpc-id,Values=$VPC_ID" \
        --query "VpcEndpoints[*].[VpcEndpointId,ServiceName,VpcEndpointType,State,Tags[?Key=='Name'].Value|[0]]" --output table
fi

# Get Network ACLs
print_subheader "Network ACLs"
echo -e "${YELLOW}Retrieving Network ACLs for VPC ${VPC_ID}...${NC}"
aws ec2 describe-network-acls --filters "Name=vpc-id,Values=$VPC_ID" \
    --query "NetworkAcls[*].[NetworkAclId,IsDefault,Tags[?Key=='Name'].Value|[0]]" --output table

# Get DHCP Options
print_subheader "DHCP Options"
echo -e "${YELLOW}Retrieving DHCP Options for VPC ${VPC_ID}...${NC}"
DHCP_OPTIONS_ID=$(aws ec2 describe-vpcs --vpc-ids "$VPC_ID" --query "Vpcs[0].DhcpOptionsId" --output text)

if [ "$DHCP_OPTIONS_ID" == "None" ] || [ -z "$DHCP_OPTIONS_ID" ]; then
    echo -e "${RED}No DHCP Options found for VPC ${VPC_ID}${NC}"
else
    echo -e "${GREEN}DHCP Options ID: ${DHCP_OPTIONS_ID}${NC}"
    aws ec2 describe-dhcp-options --dhcp-options-ids "$DHCP_OPTIONS_ID" \
        --query "DhcpOptions[0].DhcpConfigurations[*].[Key,Values[0].Value]" --output table
fi

# Summary
print_header "VPC Infrastructure Summary"
echo -e "${GREEN}VPC ID: ${VPC_ID}${NC}"
echo -e "${GREEN}Internet Gateway: ${IGW_ID}${NC}"

# Count subnets
PUBLIC_SUBNET_COUNT=$(aws ec2 describe-subnets --filters "Name=vpc-id,Values=$VPC_ID" "Name=tag:Name,Values=*public*" --query "length(Subnets)" --output text)
PRIVATE_APP_SUBNET_COUNT=$(aws ec2 describe-subnets --filters "Name=vpc-id,Values=$VPC_ID" "Name=tag:Name,Values=*private-app*" --query "length(Subnets)" --output text)
PRIVATE_DB_SUBNET_COUNT=$(aws ec2 describe-subnets --filters "Name=vpc-id,Values=$VPC_ID" "Name=tag:Name,Values=*private-db*" --query "length(Subnets)" --output text)

echo -e "${GREEN}Subnets: ${PUBLIC_SUBNET_COUNT} public, ${PRIVATE_APP_SUBNET_COUNT} private app, ${PRIVATE_DB_SUBNET_COUNT} private db${NC}"

# Count NAT Gateways
NAT_GATEWAY_COUNT=$(aws ec2 describe-nat-gateways --filter "Name=vpc-id,Values=$VPC_ID" "Name=state,Values=available" --query "length(NatGateways)" --output text)
echo -e "${GREEN}NAT Gateways: ${NAT_GATEWAY_COUNT}${NC}"

# Count Route Tables
ROUTE_TABLE_COUNT=$(aws ec2 describe-route-tables --filters "Name=vpc-id,Values=$VPC_ID" --query "length(RouteTables)" --output text)
echo -e "${GREEN}Route Tables: ${ROUTE_TABLE_COUNT}${NC}"

# Count Security Groups
SECURITY_GROUP_COUNT=$(aws ec2 describe-security-groups --filters "Name=vpc-id,Values=$VPC_ID" --query "length(SecurityGroups)" --output text)
echo -e "${GREEN}Security Groups: ${SECURITY_GROUP_COUNT}${NC}"

# Count VPC Endpoints
VPC_ENDPOINT_COUNT=$(aws ec2 describe-vpc-endpoints --filters "Name=vpc-id,Values=$VPC_ID" --query "length(VpcEndpoints)" --output text)
echo -e "${GREEN}VPC Endpoints: ${VPC_ENDPOINT_COUNT}${NC}"

echo -e "\n${BLUE}======================================================================${NC}"
echo -e "${GREEN}VPC validation completed successfully!${NC}"
echo -e "${BLUE}======================================================================${NC}"
