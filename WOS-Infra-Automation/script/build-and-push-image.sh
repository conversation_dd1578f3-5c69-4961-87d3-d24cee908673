#!/bin/bash
# <PERSON>ript to build and push the Docker image with the correct platform for ECS

# Set variables
REGION="ap-southeast-1"
ACCOUNT_ID="************"
REPOSITORY_NAME="wos-dev-sample-app"
IMAGE_TAG="latest"
APP_DIR="/Users/<USER>/Documents/wos-terraform/modules/compute/ecs-sample-app/app"

echo "Building and pushing Docker image for ECS compatibility..."

# Check if Docker is available
if ! command -v docker &> /dev/null; then
    echo "Error: Docker is not installed or not in PATH"
    exit 1
fi

# Navigate to the app directory
cd "$APP_DIR" || { echo "Error: Could not navigate to $APP_DIR"; exit 1; }

# Check if ECR repository exists, create if it doesn't
echo "Checking if ECR repository exists..."
aws ecr describe-repositories --repository-names "$REPOSITORY_NAME" --region "$REGION" > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "Creating ECR repository $REPOSITORY_NAME..."
    aws ecr create-repository --repository-name "$REPOSITORY_NAME" --region "$REGION"
    if [ $? -ne 0 ]; then
        echo "Error: Failed to create ECR repository."
        exit 1
    fi
fi

# Log in to ECR
echo "Logging in to Amazon ECR..."
aws ecr get-login-password --region "$REGION" | docker login --username AWS --password-stdin "$ACCOUNT_ID.dkr.ecr.$REGION.amazonaws.com"

if [ $? -ne 0 ]; then
    echo "Error: Failed to log in to ECR. Check your AWS credentials."
    exit 1
fi

# Build the Docker image with platform specification
echo "Building Docker image for linux/amd64 platform..."

# Create a buildx builder if it doesn't exist
docker buildx inspect multiplatform-builder > /dev/null 2>&1 || docker buildx create --name multiplatform-builder --use

# Build and push in one step to ensure the manifest is properly created
echo "Building and pushing image to ECR..."
docker buildx build \
    --platform linux/amd64 \
    --tag "$ACCOUNT_ID.dkr.ecr.$REGION.amazonaws.com/$REPOSITORY_NAME:$IMAGE_TAG" \
    --push \
    .

if [ $? -ne 0 ]; then
    echo "Error: Failed to build and push the Docker image."
    echo "If you're on an M1/M2 Mac, make sure Docker Desktop has the 'Use Rosetta for x86/amd64 emulation on Apple Silicon' option enabled."
    exit 1
fi

echo "Successfully built and pushed the Docker image with linux/amd64 platform compatibility."
echo "Image: $ACCOUNT_ID.dkr.ecr.$REGION.amazonaws.com/$REPOSITORY_NAME:$IMAGE_TAG"
echo ""
echo "Your ECS task should now be able to pull this image successfully."
echo "To verify the image manifest, run:"
echo "docker buildx imagetools inspect $ACCOUNT_ID.dkr.ecr.$REGION.amazonaws.com/$REPOSITORY_NAME:$IMAGE_TAG"
