#!/bin/bash
# <PERSON><PERSON>t to update the ECS task definition with environment variables instead of Secrets Manager

# Set variables
TASK_FAMILY="wos-dev-sample-app"
REGION="ap-southeast-1"
CLUSTER_NAME="wos-dev-cluster"
SERVICE_NAME="wos-dev-sample-app-service"

echo "Retrieving current task definition for $TASK_FAMILY..."

# Get the current task definition
TASK_DEFINITION=$(aws ecs describe-task-definition --task-definition $TASK_FAMILY --region $REGION 2>/dev/null)

if [ $? -ne 0 ]; then
  echo "Error: Failed to retrieve task definition. This might be due to SCP restrictions."
  echo "Please run this script with appropriate permissions or ask your AWS administrator to run it."
  exit 1
fi

# Extract the container definition
CONTAINER_DEF=$(echo $TASK_DEFINITION | jq '.taskDefinition.containerDefinitions[0]')

# Remove any existing secrets
CONTAINER_DEF=$(echo $CONTAINER_DEF | jq 'del(.secrets)')

# Add environment variables for database credentials
ENV_VARS=$(echo $CONTAINER_DEF | jq '.environment')
if [ "$ENV_VARS" == "null" ]; then
  ENV_VARS="[]"
fi

# Add DB_USERNAME environment variable if it doesn't exist
if ! echo $ENV_VARS | jq -e '.[] | select(.name == "DB_USERNAME")' > /dev/null; then
  ENV_VARS=$(echo $ENV_VARS | jq '. += [{"name": "DB_USERNAME", "value": "postgres"}]')
fi

# Add DB_PASSWORD environment variable if it doesn't exist
if ! echo $ENV_VARS | jq -e '.[] | select(.name == "DB_PASSWORD")' > /dev/null; then
  ENV_VARS=$(echo $ENV_VARS | jq '. += [{"name": "DB_PASSWORD", "value": "postgres"}]')
fi

# Add USE_SECRETS_MANAGER environment variable or update it
if echo $ENV_VARS | jq -e '.[] | select(.name == "USE_SECRETS_MANAGER")' > /dev/null; then
  ENV_VARS=$(echo $ENV_VARS | jq 'map(if .name == "USE_SECRETS_MANAGER" then .value = "false" else . end)')
else
  ENV_VARS=$(echo $ENV_VARS | jq '. += [{"name": "USE_SECRETS_MANAGER", "value": "false"}]')
fi

# Update the container definition with the new environment variables
CONTAINER_DEF=$(echo $CONTAINER_DEF | jq --argjson env "$ENV_VARS" '.environment = $env')

# Get the task definition without the container definitions
TASK_DEF_WITHOUT_CONTAINERS=$(echo $TASK_DEFINITION | jq '.taskDefinition | del(.containerDefinitions)')

# Create a new task definition with the updated container definition
NEW_TASK_DEF=$(echo $TASK_DEF_WITHOUT_CONTAINERS | jq --argjson container "[$CONTAINER_DEF]" '. + {containerDefinitions: $container}')

# Remove fields that can't be specified in RegisterTaskDefinition
NEW_TASK_DEF=$(echo $NEW_TASK_DEF | jq 'del(.taskDefinitionArn, .revision, .status, .requiresAttributes, .compatibilities, .registeredAt, .registeredBy)')

# Register the new task definition
echo "Registering new task definition..."
NEW_TASK_DEF_ARN=$(aws ecs register-task-definition --region $REGION --cli-input-json "$(echo $NEW_TASK_DEF | jq -c)" | jq -r '.taskDefinition.taskDefinitionArn')

if [ $? -ne 0 ]; then
  echo "Error: Failed to register new task definition. This might be due to SCP restrictions."
  echo "Please run this script with appropriate permissions or ask your AWS administrator to run it."
  exit 1
fi

echo "Successfully registered new task definition: $NEW_TASK_DEF_ARN"

# Update the service to use the new task definition
echo "Updating service $SERVICE_NAME to use the new task definition..."
aws ecs update-service --cluster $CLUSTER_NAME --service $SERVICE_NAME --task-definition $NEW_TASK_DEF_ARN --region $REGION

if [ $? -ne 0 ]; then
  echo "Error: Failed to update service. This might be due to SCP restrictions."
  echo "Please run this script with appropriate permissions or ask your AWS administrator to run it."
  exit 1
fi

echo "Successfully updated service $SERVICE_NAME to use the new task definition."
echo "The task should now start without trying to access Secrets Manager."
