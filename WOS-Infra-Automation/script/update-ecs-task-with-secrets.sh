#!/bin/bash
# <PERSON><PERSON>t to update the ECS task definition to properly reference AWS Secrets Manager secrets
# This script addresses the SCP restrictions by using the correct format for secret references

# Set variables
TASK_FAMILY="wos-dev-sample-app"
REGION="ap-southeast-1"
CLUSTER_NAME="wos-dev-cluster"
SERVICE_NAME="wos-dev-sample-app-service"
SECRET_ARN="arn:aws:secretsmanager:ap-southeast-1:028864969427:secret:dev/wos/database/credentials-I8PSjK"

echo "Updating ECS task definition to properly reference Secrets Manager secrets..."

# Check if AWS CLI is available
if ! command -v aws &> /dev/null; then
    echo "Error: AWS CLI is not installed or not in PATH"
    exit 1
fi

# Try to get the current task definition
echo "Retrieving current task definition for $TASK_FAMILY..."
TASK_DEF=$(aws ecs describe-task-definition --task-definition $TASK_FAMILY --region $REGION 2>/dev/null)
TASK_DEF_STATUS=$?

if [ $TASK_DEF_STATUS -ne 0 ]; then
    echo "Warning: Could not retrieve task definition due to permissions. Will create a template instead."
    
    # Create a basic task definition template
    CONTAINER_DEF=$(cat <<EOF
{
  "name": "sample-app",
  "image": "028864969427.dkr.ecr.$REGION.amazonaws.com/wos-dev-repository:latest",
  "essential": true,
  "portMappings": [
    {
      "containerPort": 8080,
      "hostPort": 8080,
      "protocol": "tcp"
    }
  ],
  "environment": [
    {
      "name": "AWS_REGION",
      "value": "$REGION"
    },
    {
      "name": "USE_SECRETS_MANAGER",
      "value": "true"
    }
  ],
  "secrets": [
    {
      "name": "DB_USERNAME",
      "valueFrom": "${SECRET_ARN}:username::"
    },
    {
      "name": "DB_PASSWORD",
      "valueFrom": "${SECRET_ARN}:password::"
    },
    {
      "name": "DB_HOST",
      "valueFrom": "${SECRET_ARN}:host::"
    },
    {
      "name": "DB_PORT",
      "valueFrom": "${SECRET_ARN}:port::"
    },
    {
      "name": "DB_NAME",
      "valueFrom": "${SECRET_ARN}:dbname::"
    }
  ],
  "logConfiguration": {
    "logDriver": "awslogs",
    "options": {
      "awslogs-group": "/ecs/wos-dev-sample-app",
      "awslogs-region": "$REGION",
      "awslogs-stream-prefix": "ecs"
    }
  }
}
EOF
)

    # Create a new task definition JSON
    TASK_DEF_JSON=$(cat <<EOF
{
  "family": "$TASK_FAMILY",
  "executionRoleArn": "arn:aws:iam::028864969427:role/dev-wos-ecs-task-execution-role",
  "taskRoleArn": "arn:aws:iam::028864969427:role/dev-wos-ecs-task-execution-role",
  "networkMode": "awsvpc",
  "containerDefinitions": [$CONTAINER_DEF],
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "256",
  "memory": "512"
}
EOF
)

    echo "Task definition template created."
    echo "$TASK_DEF_JSON" > task-definition.json
    echo "Task definition template saved to task-definition.json"
    echo "You can register it with: aws ecs register-task-definition --cli-input-json file://task-definition.json --region $REGION"
    exit 0
else
    # Extract the container definition
    CONTAINER_DEF=$(echo $TASK_DEF | jq '.taskDefinition.containerDefinitions[0]')
    
    # Update the secrets configuration
    SECRETS=$(cat <<EOF
[
  {
    "name": "DB_USERNAME",
    "valueFrom": "${SECRET_ARN}:username::"
  },
  {
    "name": "DB_PASSWORD",
    "valueFrom": "${SECRET_ARN}:password::"
  },
  {
    "name": "DB_HOST",
    "valueFrom": "${SECRET_ARN}:host::"
  },
  {
    "name": "DB_PORT",
    "valueFrom": "${SECRET_ARN}:port::"
  },
  {
    "name": "DB_NAME",
    "valueFrom": "${SECRET_ARN}:dbname::"
  }
]
EOF
)
    
    # Get existing environment variables or initialize empty array
    ENV_VARS=$(echo $CONTAINER_DEF | jq '.environment')
    if [ "$ENV_VARS" == "null" ]; then
        ENV_VARS="[]"
    fi
    
    # Update or add USE_SECRETS_MANAGER environment variable
    if echo $ENV_VARS | jq -e '.[] | select(.name == "USE_SECRETS_MANAGER")' > /dev/null; then
        ENV_VARS=$(echo $ENV_VARS | jq 'map(if .name == "USE_SECRETS_MANAGER" then .value = "true" else . end)')
    else
        ENV_VARS=$(echo $ENV_VARS | jq '. += [{"name": "USE_SECRETS_MANAGER", "value": "true"}]')
    fi
    
    # Update the container definition with the new secrets and environment variables
    CONTAINER_DEF=$(echo $CONTAINER_DEF | jq --argjson secrets "$SECRETS" '.secrets = $secrets')
    CONTAINER_DEF=$(echo $CONTAINER_DEF | jq --argjson env "$ENV_VARS" '.environment = $env')
    
    # Get the task definition without the container definitions
    TASK_DEF_WITHOUT_CONTAINERS=$(echo $TASK_DEF | jq '.taskDefinition | del(.containerDefinitions)')
    
    # Create a new task definition with the updated container definition
    NEW_TASK_DEF=$(echo $TASK_DEF_WITHOUT_CONTAINERS | jq --argjson container "[$CONTAINER_DEF]" '. + {containerDefinitions: $container}')
    
    # Remove fields that can't be specified in RegisterTaskDefinition
    NEW_TASK_DEF=$(echo $NEW_TASK_DEF | jq 'del(.taskDefinitionArn, .revision, .status, .requiresAttributes, .compatibilities, .registeredAt, .registeredBy)')
    
    # Register the new task definition
    echo "Registering new task definition..."
    echo "$NEW_TASK_DEF" > task-definition.json
    echo "Task definition saved to task-definition.json"
    
    echo "To register the task definition, run:"
    echo "aws ecs register-task-definition --cli-input-json file://task-definition.json --region $REGION"
    
    echo "After registering, update the service with:"
    echo "aws ecs update-service --cluster $CLUSTER_NAME --service $SERVICE_NAME --task-definition \$NEW_TASK_DEF_ARN --region $REGION"
    
    echo "Note: You may need to request an exception to the SCP restrictions to allow access to Secrets Manager."
fi
