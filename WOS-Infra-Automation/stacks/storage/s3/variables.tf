variable "aws_account_id" {
  description = "The AWS account ID"
  type        = string
}

variable "user_name" {
  description = "The IAM user name to grant access to the bucket"
  type        = string
  default     = "<EMAIL>"
}

variable "cicd_role_name" {
  description = "The IAM role name for CI/CD to grant access to the bucket"
  type        = string
  default     = "cicd-role"
}

variable "tags" {
  description = "A map of tags to add to all resources"
  type        = map(string)
  default     = {}
}
