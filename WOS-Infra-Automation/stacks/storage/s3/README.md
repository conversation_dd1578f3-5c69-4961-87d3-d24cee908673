# AWS S3 Bucket Stack

This stack creates an S3 bucket for Terraform state storage using the S3 bucket module.

## Features

- Creates an S3 bucket for storing Terraform state files
- Configures bucket policy with appropriate permissions for users and CI/CD role
- Enables versioning, encryption, and public access blocking
- Applies standard tags

## Usage

```hcl
module "terraform_state_storage" {
  source = "../../stacks/storage/s3"

  aws_account_id = "************"
  user_name      = "<EMAIL>"
  cicd_role_name = "cicd-role"
  
  tags = {
    Project     = "WasteOS"
    Environment = "Management"
    ManagedBy   = "Terraform"
  }
}
```

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| aws_account_id | The AWS account ID | `string` | n/a | yes |
| user_name | The IAM user name to grant access to the bucket | `string` | `"<EMAIL>"` | no |
| cicd_role_name | The IAM role name for CI/CD to grant access to the bucket | `string` | `"cicd-role"` | no |
| tags | A map of tags to add to all resources | `map(string)` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| bucket_id | The name of the bucket |
| bucket_arn | The ARN of the bucket |
| bucket_domain_name | The domain name of the bucket |

## Notes

- The bucket name follows the format `wos-terraform-state-bucket-{aws_account_id}`
- The bucket policy grants specific permissions to the specified IAM user and CI/CD role
- All public access is blocked
- Versioning is enabled to protect against accidental deletion of state files
- Server-side encryption is enabled using AES256
