/**
 * # AWS S3 Bucket Stack
 *
 * This stack creates an S3 bucket for Terraform state storage using the S3 bucket module.
 */

locals {
  bucket_name = "wos-terraform-state-bucket-${var.aws_account_id}"

  # Define the bucket policy
  bucket_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "AllowUserAccess"
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::${var.aws_account_id}:user/${var.user_name}"
        }
        Action = [
          "s3:ListBucket",
          "s3:GetBucketLocation",
          "s3:GetBucketVersioning",
          "s3:GetBucketEncryption",
          "s3:GetBucketPolicy",
          "s3:GetBucketAcl",
          "s3:GetBucketTagging"
        ]
        Resource = "arn:aws:s3:::${local.bucket_name}"
      },
      {
        Sid    = "AllowUserObjectAccess"
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::${var.aws_account_id}:user/${var.user_name}"
        }
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:DeleteObject",
          "s3:GetObjectVersion",
          "s3:GetObjectAcl",
          "s3:GetObjectTagging",
          "s3:PutObjectTagging"
        ]
        Resource = "arn:aws:s3:::${local.bucket_name}/*"
      },
      {
        Sid    = "AllowCICDRoleAccess"
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::${var.aws_account_id}:role/${var.cicd_role_name}"
        }
        Action = [
          "s3:ListBucket",
          "s3:GetBucketLocation",
          "s3:GetBucketVersioning",
          "s3:GetBucketEncryption",
          "s3:GetBucketPolicy",
          "s3:GetBucketAcl",
          "s3:GetBucketTagging"
        ]
        Resource = "arn:aws:s3:::${local.bucket_name}"
      },
      {
        Sid    = "AllowCICDRoleObjectAccess"
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::${var.aws_account_id}:role/${var.cicd_role_name}"
        }
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:DeleteObject",
          "s3:GetObjectVersion",
          "s3:GetObjectAcl",
          "s3:GetObjectTagging",
          "s3:PutObjectTagging"
        ]
        Resource = "arn:aws:s3:::${local.bucket_name}/*"
      },
      {
        Sid       = "DenyInsecureTransport"
        Effect    = "Deny"
        Principal = "*"
        Action    = "s3:*"
        Resource = [
          "arn:aws:s3:::${local.bucket_name}",
          "arn:aws:s3:::${local.bucket_name}/*"
        ]
        Condition = {
          Bool = {
            "aws:SecureTransport" = "false"
          }
        }
      }
    ]
  })
}

module "terraform_state_bucket" {
  source = "../../../modules/storage/s3"

  bucket_name   = local.bucket_name
  bucket_policy = local.bucket_policy

  tags = merge(
    var.tags,
    {
      Name        = local.bucket_name
      Project     = "WasteOS"
      Environment = "Management"
      ManagedBy   = "Terraform"
    }
  )
}
