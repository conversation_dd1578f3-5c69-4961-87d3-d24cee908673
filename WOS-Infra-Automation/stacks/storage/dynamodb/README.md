# AWS DynamoDB Tables Stack

This stack creates DynamoDB tables for Terraform state locking and deployment history.

## Features

- Creates a DynamoDB table for Terraform state locking
- Creates a DynamoDB table for deployment history tracking
- Configures appropriate attributes and key schema for each table
- Uses PAY_PER_REQUEST billing mode for cost optimization
- Enables point-in-time recovery and server-side encryption
- Applies standard tags

## Usage

```hcl
module "dynamodb_tables" {
  source = "../../stacks/storage/dynamodb"

  terraform_lock_table_name    = "terraform-statefile-locks-wos"
  deployment_history_table_name = "wos-deployment-history"
  
  tags = {
    Project     = "WasteOS"
    Environment = "Management"
    ManagedBy   = "Terraform"
  }
}
```

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| terraform_lock_table_name | Name of the DynamoDB table for Terraform state locking | `string` | `"terraform-statefile-locks-wos"` | no |
| deployment_history_table_name | Name of the DynamoDB table for deployment history | `string` | `"wos-deployment-history"` | no |
| tags | A map of tags to add to all resources | `map(string)` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| terraform_lock_table_id | The name of the Terraform lock table |
| terraform_lock_table_arn | The ARN of the Terraform lock table |
| deployment_history_table_id | The name of the deployment history table |
| deployment_history_table_arn | The ARN of the deployment history table |

## Notes

- The Terraform lock table uses a simple key schema with just a hash key (LockID)
- The deployment history table uses a composite key with a hash key (Environment) and a range key (Timestamp)
- Both tables use PAY_PER_REQUEST billing mode to avoid charges when not in use
- Point-in-time recovery is enabled to protect against accidental data loss
- Server-side encryption is enabled for security
