variable "terraform_lock_table_name" {
  description = "Name of the DynamoDB table for Terraform state locking"
  type        = string
  default     = "terraform-statefile-locks-wos"
}

variable "deployment_history_table_name" {
  description = "Name of the DynamoDB table for deployment history"
  type        = string
  default     = "wos-deployment-history"
}

variable "tags" {
  description = "A map of tags to add to all resources"
  type        = map(string)
  default     = {}
}
