/**
 * # AWS DynamoDB Tables Stack
 *
 * This stack creates DynamoDB tables for Terraform state locking and deployment history.
 */

# Create DynamoDB table for Terraform state locking
module "terraform_lock_table" {
  source = "../../../modules/storage/dynamodb"

  table_name = var.terraform_lock_table_name
  hash_key   = "LockID"

  attributes = [
    {
      name = "LockID"
      type = "S"
    }
  ]

  tags = merge(
    var.tags,
    {
      Name        = var.terraform_lock_table_name
      Project     = "WasteOS"
      Environment = "Management"
      ManagedBy   = "Terraform"
    }
  )
}

# Create DynamoDB table for deployment history
module "deployment_history_table" {
  source = "../../../modules/storage/dynamodb"

  table_name = var.deployment_history_table_name
  hash_key   = "Environment"
  range_key  = "Timestamp"

  attributes = [
    {
      name = "Environment"
      type = "S"
    },
    {
      name = "Timestamp"
      type = "S"
    }
  ]

  tags = merge(
    var.tags,
    {
      Name        = var.deployment_history_table_name
      Project     = "WasteOS"
      Environment = "Management"
      ManagedBy   = "Terraform"
    }
  )
}
