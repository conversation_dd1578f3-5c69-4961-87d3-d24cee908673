/**
 * # AWS RDS PostgreSQL Stack Variables
 *
 * Variables for the RDS PostgreSQL stack
 */

variable "environment" {
  description = "Environment name (e.g., dev, uat, prod)"
  type        = string
}

variable "private_db_subnet_ids" {
  description = "List of private database subnet IDs"
  type        = list(string)
}

variable "rds_security_group_id" {
  description = "ID of the RDS security group"
  type        = string
}

variable "postgres_family_version" {
  description = "PostgreSQL parameter group family version (e.g., 17)"
  type        = string
  default     = "17"
}

variable "engine_version" {
  description = "PostgreSQL engine version"
  type        = string
  default     = "17.4"
}

variable "instance_class" {
  description = "RDS instance class"
  type        = string
  default     = null # Will be set based on environment in the module
}

variable "allocated_storage" {
  description = "Allocated storage in GB"
  type        = number
  default     = null # Will be set based on environment in the module
}

variable "max_allocated_storage" {
  description = "Maximum allocated storage in GB for autoscaling"
  type        = number
  default     = null # Will be set based on environment in the module
}

variable "storage_type" {
  description = "Storage type (gp2, gp3, io1)"
  type        = string
  default     = null # Will be set based on environment in the module
}

variable "kms_key_id" {
  description = "KMS key ID for storage encryption"
  type        = string
  default     = null
}

variable "backup_retention_period" {
  description = "Backup retention period in days"
  type        = number
  default     = null # Will be set based on environment in the module
}

variable "backup_window" {
  description = "Preferred backup window"
  type        = string
  default     = "03:00-04:00"
}

variable "maintenance_window" {
  description = "Preferred maintenance window"
  type        = string
  default     = "sun:04:30-sun:05:30"
}

variable "multi_az" {
  description = "Whether to deploy a multi-AZ RDS instance"
  type        = bool
  default     = null # Will be set based on environment in the module
}

variable "deletion_protection" {
  description = "Whether to enable deletion protection"
  type        = bool
  default     = null # Will be set based on environment in the module
}

variable "skip_final_snapshot" {
  description = "Whether to skip the final snapshot when deleting the instance"
  type        = bool
  default     = null # Will be set based on environment in the module
}

variable "apply_immediately" {
  description = "Whether to apply changes immediately or during the maintenance window"
  type        = bool
  default     = null # Will be set based on environment in the module
}

variable "enable_enhanced_monitoring" {
  description = "Whether to enable enhanced monitoring"
  type        = bool
  default     = true
}

variable "enable_performance_insights" {
  description = "Whether to enable Performance Insights"
  type        = bool
  default     = true
}

variable "performance_insights_retention_period" {
  description = "Performance Insights retention period in days"
  type        = number
  default     = 7
}

variable "create_read_replica" {
  description = "Whether to create a read replica"
  type        = bool
  default     = false
}

variable "replica_instance_class" {
  description = "Instance class for the read replica"
  type        = string
  default     = null # Will use the same as primary if not specified
}

variable "tags" {
  description = "A map of tags to add to all resources"
  type        = map(string)
  default     = {}
}
