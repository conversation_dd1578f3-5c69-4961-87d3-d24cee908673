/**
 * # AWS RDS PostgreSQL Stack
 *
 * This stack creates an RDS PostgreSQL instance using the RDS module.
 */

module "rds" {
  source = "../../../modules/database/rds"

  # Environment
  environment = var.environment

  # Network configuration
  private_db_subnet_ids = var.private_db_subnet_ids
  rds_security_group_id = var.rds_security_group_id

  # PostgreSQL configuration
  postgres_family_version = var.postgres_family_version
  engine_version          = var.engine_version
  instance_class          = var.instance_class

  # Storage configuration
  allocated_storage     = var.allocated_storage
  max_allocated_storage = var.max_allocated_storage
  storage_type          = var.storage_type
  kms_key_id            = var.kms_key_id

  # Backup and maintenance
  backup_retention_period = var.backup_retention_period
  backup_window           = var.backup_window
  maintenance_window      = var.maintenance_window

  # High availability
  multi_az = var.multi_az

  # Security
  deletion_protection = var.deletion_protection
  skip_final_snapshot = var.skip_final_snapshot

  # Monitoring
  enable_enhanced_monitoring            = var.enable_enhanced_monitoring
  enable_performance_insights           = var.enable_performance_insights
  performance_insights_retention_period = var.performance_insights_retention_period

  # Read replica
  create_read_replica    = var.create_read_replica
  replica_instance_class = var.replica_instance_class

  # Operations
  apply_immediately = var.apply_immediately

  # Tags
  tags = var.tags
}
