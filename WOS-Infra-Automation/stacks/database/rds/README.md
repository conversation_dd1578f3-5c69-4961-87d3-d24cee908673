# AWS RDS PostgreSQL Stack

This stack creates an AWS RDS PostgreSQL instance using the RDS module. It provides a layer of abstraction over the module, allowing for environment-specific configurations.

## Usage

```hcl
module "rds_stack" {
  source = "../../stacks/database/rds"

  environment           = var.environment
  private_db_subnet_ids = module.vpc.private_db_subnet_ids
  rds_security_group_id = module.vpc.rds_security_group_id
  
  # PostgreSQL configuration
  postgres_family_version = "17"
  engine_version          = "17.4"
  
  # Environment-specific settings
  instance_class          = "db.t4g.large"
  allocated_storage       = 50
  max_allocated_storage   = 100
  storage_type            = "gp3"
  backup_retention_period = 7
  multi_az                = false
  deletion_protection     = true
  skip_final_snapshot     = false
  
  # Read replica
  create_read_replica    = false
  
  tags = var.tags
}
```

## Requirements

| Name | Version |
|------|---------|
| terraform | >= 1.0.0 |
| aws | ~> 5.0 |

## Providers

| Name | Version |
|------|---------|
| aws | ~> 5.0 |

## Modules

| Name | Source | Version |
|------|--------|---------|
| rds | ../../../modules/database/rds | n/a |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| environment | Environment name (e.g., dev, uat, prod) | `string` | n/a | yes |
| private_db_subnet_ids | List of private database subnet IDs | `list(string)` | n/a | yes |
| rds_security_group_id | ID of the RDS security group | `string` | n/a | yes |
| postgres_family_version | PostgreSQL parameter group family version (e.g., 17) | `string` | `"17"` | no |
| engine_version | PostgreSQL engine version | `string` | `"17.4"` | no |
| instance_class | RDS instance class | `string` | `null` | no |
| allocated_storage | Allocated storage in GB | `number` | `null` | no |
| max_allocated_storage | Maximum allocated storage in GB for autoscaling | `number` | `null` | no |
| storage_type | Storage type (gp2, gp3, io1) | `string` | `null` | no |
| kms_key_id | KMS key ID for storage encryption | `string` | `null` | no |
| backup_retention_period | Backup retention period in days | `number` | `null` | no |
| backup_window | Preferred backup window | `string` | `"03:00-04:00"` | no |
| maintenance_window | Preferred maintenance window | `string` | `"sun:04:30-sun:05:30"` | no |
| multi_az | Whether to deploy a multi-AZ RDS instance | `bool` | `null` | no |
| deletion_protection | Whether to enable deletion protection | `bool` | `null` | no |
| skip_final_snapshot | Whether to skip the final snapshot when deleting the instance | `bool` | `null` | no |
| apply_immediately | Whether to apply changes immediately or during the maintenance window | `bool` | `null` | no |
| enable_enhanced_monitoring | Whether to enable enhanced monitoring | `bool` | `true` | no |
| enable_performance_insights | Whether to enable Performance Insights | `bool` | `true` | no |
| performance_insights_retention_period | Performance Insights retention period in days | `number` | `7` | no |
| create_read_replica | Whether to create a read replica | `bool` | `false` | no |
| replica_instance_class | Instance class for the read replica | `string` | `null` | no |
| tags | A map of tags to add to all resources | `map(string)` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| db_instance_id | The RDS instance ID |
| db_instance_address | The address of the RDS instance |
| db_instance_endpoint | The connection endpoint of the RDS instance |
| db_instance_arn | The ARN of the RDS instance |
| db_instance_name | The database name |
| db_instance_port | The database port |
| db_subnet_group_id | The DB subnet group ID |
| db_parameter_group_id | The DB parameter group ID |
| db_monitoring_role_arn | The ARN of the RDS monitoring IAM role |
| db_credentials_secret_arn | The ARN of the Secrets Manager secret containing database credentials |
| db_uri_secret_arn | The ARN of the Secrets Manager secret containing the database URI |
| db_replica_instance_id | The ID of the RDS read replica instance |
| db_replica_instance_address | The address of the RDS read replica instance |
| db_replica_instance_endpoint | The connection endpoint of the RDS read replica instance |
