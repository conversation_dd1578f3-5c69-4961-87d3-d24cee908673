/**
 * # AWS RDS PostgreSQL Stack Outputs
 *
 * Outputs from the RDS PostgreSQL stack
 */

output "db_instance_id" {
  description = "The RDS instance ID"
  value       = module.rds.db_instance_id
}

output "db_instance_address" {
  description = "The address of the RDS instance"
  value       = module.rds.db_instance_address
}

output "db_instance_endpoint" {
  description = "The connection endpoint of the RDS instance"
  value       = module.rds.db_instance_endpoint
}

output "db_instance_arn" {
  description = "The ARN of the RDS instance"
  value       = module.rds.db_instance_arn
}

output "db_instance_name" {
  description = "The database name"
  value       = module.rds.db_instance_name
}

output "db_instance_port" {
  description = "The database port"
  value       = module.rds.db_instance_port
}

output "db_subnet_group_id" {
  description = "The DB subnet group ID"
  value       = module.rds.db_subnet_group_id
}

output "db_parameter_group_id" {
  description = "The DB parameter group ID"
  value       = module.rds.db_parameter_group_id
}

output "db_monitoring_role_arn" {
  description = "The ARN of the RDS monitoring IAM role"
  value       = module.rds.db_monitoring_role_arn
}

output "db_credentials_secret_arn" {
  description = "The ARN of the Secrets Manager secret containing database credentials"
  value       = module.rds.db_credentials_secret_arn
}

output "db_uri_secret_arn" {
  description = "The ARN of the Secrets Manager secret containing the database URI"
  value       = module.rds.db_uri_secret_arn
}

output "db_replica_instance_id" {
  description = "The ID of the RDS read replica instance"
  value       = module.rds.db_replica_instance_id
}

output "db_replica_instance_address" {
  description = "The address of the RDS read replica instance"
  value       = module.rds.db_replica_instance_address
}

output "db_replica_instance_endpoint" {
  description = "The connection endpoint of the RDS read replica instance"
  value       = module.rds.db_replica_instance_endpoint
}
