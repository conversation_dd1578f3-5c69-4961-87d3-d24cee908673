# Secrets Manager Stack for THECHATBOT - main.tf
# This stack creates secrets for the THECHATBOT app

module "thechatbot_openai_secret" {
  source = "../../../modules/security/secrets_manager"

  name        = "wos/${var.environment}/thechatbot/openai"
  description = "OpenAI API key for THECHATBOT"

  create_secret_version = true
  secret_string = jsonencode({
    OPENAI_API_KEY = var.openai_api_key
  })

  tags = merge(
    var.tags,
    {
      Name        = "wos-${var.environment}-thechatbot-openai"
      Environment = var.environment
    }
  )
}
