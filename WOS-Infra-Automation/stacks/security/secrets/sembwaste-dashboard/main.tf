# Secrets for the sembwaste-dashboard application

resource "aws_secretsmanager_secret" "nextauth_secret" {
  name        = "/wos/dev/sembwaste-dashboard/NEXTAUTH_SECRET"
  description = "NextAuth secret key for sembwaste-dashboard application"

  tags = {
    Name        = "sembwaste-dashboard-nextauth-secret"
    Environment = "dev"
    Application = "sembwaste-dashboard"
    ManagedBy   = "Terraform"
  }
}

# Generate a random secure string for the NextAuth secret
resource "random_password" "nextauth_secret" {
  length           = 32
  special          = true
  override_special = "!#$%&*()-_=+[]{}<>:?"
}

# Store the generated secret in AWS Secrets Manager
resource "aws_secretsmanager_secret_version" "nextauth_secret" {
  secret_id     = aws_secretsmanager_secret.nextauth_secret.id
  secret_string = random_password.nextauth_secret.result
}

# Output the ARN of the secret for reference in other modules
output "nextauth_secret_arn" {
  description = "ARN of the NextAuth secret"
  value       = aws_secretsmanager_secret.nextauth_secret.arn
}
