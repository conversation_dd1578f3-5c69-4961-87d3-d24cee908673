# Bedrock IAM Stack - main.tf
# This stack creates IAM resources for Amazon Bedrock access

locals {
  name_prefix = "wos-${var.environment}"

  # Format the Bedrock model ARNs with the provided region and account ID
  bedrock_model_arns = [
    for model_id in var.bedrock_model_ids :
    "arn:aws:bedrock:${var.region}:${var.account_id}:foundation-model/${model_id}"
  ]
}

# Create the Bedrock IAM policy
module "bedrock_policy" {
  source = "../../../modules/iam/policies/bedrock"

  policy_name        = "${local.name_prefix}-bedrock-policy"
  policy_description = "Allows access to Amazon Bedrock models for ${var.environment} environment"
  bedrock_actions    = var.bedrock_actions
  bedrock_model_arns = local.bedrock_model_arns

  tags = merge(
    var.tags,
    {
      Environment = var.environment
    }
  )
}

# Create the Bedrock IAM role
module "bedrock_role" {
  source = "../../../modules/iam/roles/bedrock"

  role_name        = "${local.name_prefix}-bedrock-role"
  role_description = "Role for accessing Amazon Bedrock models in ${var.environment} environment"
  trusted_services = var.trusted_services
  policy_arns      = concat([module.bedrock_policy.policy_arn], var.additional_policy_arns)

  tags = merge(
    var.tags,
    {
      Environment = var.environment
    }
  )
}
