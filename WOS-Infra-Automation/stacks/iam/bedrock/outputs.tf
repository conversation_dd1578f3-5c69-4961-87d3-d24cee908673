# Bedrock IAM Stack - outputs.tf
# Outputs for the Bedrock IAM stack

output "bedrock_policy_arn" {
  description = "ARN of the IAM policy for Bedrock access"
  value       = module.bedrock_policy.policy_arn
}

output "bedrock_policy_name" {
  description = "Name of the IAM policy for Bedrock access"
  value       = module.bedrock_policy.policy_name
}

output "bedrock_role_arn" {
  description = "ARN of the IAM role for Bedrock access"
  value       = module.bedrock_role.role_arn
}

output "bedrock_role_name" {
  description = "Name of the IAM role for Bedrock access"
  value       = module.bedrock_role.role_name
}
