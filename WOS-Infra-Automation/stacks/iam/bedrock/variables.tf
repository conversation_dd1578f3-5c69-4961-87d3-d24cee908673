# Bedrock IAM Stack - variables.tf
# Variables for the Bedrock IAM stack

variable "environment" {
  description = "Environment name (e.g., dev, staging, prod)"
  type        = string
}

variable "region" {
  description = "AWS region"
  type        = string
}

variable "account_id" {
  description = "AWS account ID"
  type        = string
}

variable "bedrock_model_ids" {
  description = "List of Bedrock model IDs to allow access to"
  type        = list(string)
  default     = ["anthropic.claude-3-sonnet-********-v1:0"]
}

variable "bedrock_actions" {
  description = "List of Bedrock API actions to allow"
  type        = list(string)
  default     = ["bedrock:InvokeModel"]
}

variable "trusted_services" {
  description = "List of AWS services that can assume the Bedrock role"
  type        = list(string)
  default     = ["ecs-tasks.amazonaws.com"]
}

variable "additional_policy_arns" {
  description = "List of additional IAM policy ARNs to attach to the Bedrock role"
  type        = list(string)
  default     = []
}

variable "tags" {
  description = "A map of tags to add to all resources"
  type        = map(string)
  default     = {}
}
