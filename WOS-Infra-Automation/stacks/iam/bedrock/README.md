# Bedrock IAM Stack

This stack creates IAM resources for Amazon Bedrock access. It includes:

1. An IAM policy that grants permissions to invoke specific Bedrock models
2. An IAM role that can be assumed by specified AWS services (e.g., ECS tasks)

## Usage

```hcl
module "bedrock_iam" {
  source = "../../stacks/iam/bedrock"

  environment      = "dev"
  region           = "ap-southeast-1"
  account_id       = "************"
  bedrock_model_ids = ["anthropic.claude-3-sonnet-********-v1:0"]
  trusted_services = ["ecs-tasks.amazonaws.com"]
  
  tags = {
    Project     = "WasteOS"
    Environment = "dev"
    ManagedBy   = "Terraform"
  }
}
```

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| environment | Environment name (e.g., dev, staging, prod) | `string` | n/a | yes |
| region | AWS region | `string` | n/a | yes |
| account_id | AWS account ID | `string` | n/a | yes |
| bedrock_model_ids | List of Bedrock model IDs to allow access to | `list(string)` | `["anthropic.claude-3-sonnet-********-v1:0"]` | no |
| bedrock_actions | List of Bedrock API actions to allow | `list(string)` | `["bedrock:InvokeModel"]` | no |
| trusted_services | List of AWS services that can assume the Bedrock role | `list(string)` | `["ecs-tasks.amazonaws.com"]` | no |
| additional_policy_arns | List of additional IAM policy ARNs to attach to the Bedrock role | `list(string)` | `[]` | no |
| tags | A map of tags to add to all resources | `map(string)` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| bedrock_policy_arn | ARN of the IAM policy for Bedrock access |
| bedrock_policy_name | Name of the IAM policy for Bedrock access |
| bedrock_role_arn | ARN of the IAM role for Bedrock access |
| bedrock_role_name | Name of the IAM role for Bedrock access |

## Notes

- The policy grants the minimum required permission (`bedrock:InvokeModel`) for specific Bedrock models
- The role can be assumed by the specified trusted services (default: ECS tasks)
- Additional policies can be attached to the role if needed
- The resources are named with the environment prefix (e.g., `wos-dev-bedrock-policy`)
