# IAM Stack for ECS - variables.tf
# Variables for the IAM stack

variable "environment" {
  description = "Environment name (e.g., dev, staging, prod)"
  type        = string
}

variable "project" {
  description = "Project name"
  type        = string
  default     = "wos"
}

variable "ecr_repository_arns" {
  description = "List of ECR repository ARNs to grant access to"
  type        = list(string)
}

variable "secret_arns" {
  description = "List of Secrets Manager secret ARNs to grant access to"
  type        = list(string)
}

variable "tags" {
  description = "Tags to apply to all resources"
  type        = map(string)
  default     = {}
}

variable "scp_restrictions" {
  description = "Whether there are SCP restrictions that prevent access to certain AWS services"
  type        = bool
  default     = true
}
