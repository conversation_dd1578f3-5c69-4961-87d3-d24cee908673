# IAM Stack for ECS - main.tf
# This stack creates IAM policies and roles for ECS deployments

# Create ECR access policy
module "ecr_access_policy" {
  source = "../../../modules/iam/policies"

  name_prefix         = "${var.environment}-${var.project}"
  create_ecr_policy   = true
  ecr_repository_arns = var.ecr_repository_arns

  tags = merge(
    var.tags,
    {
      Environment = var.environment
    }
  )
}

# Create Secrets Manager access policy
module "secrets_manager_policy" {
  source = "../../../modules/iam/policies"

  name_prefix                   = "${var.environment}-${var.project}"
  create_secrets_manager_policy = true
  secret_arns                   = var.secret_arns

  tags = merge(
    var.tags,
    {
      Environment = var.environment
    }
  )
}

# Create combined ECS task execution policy
module "ecs_task_execution_policy" {
  source = "../../../modules/iam/policies"

  name_prefix                      = "${var.environment}-${var.project}"
  create_ecs_task_execution_policy = true
  ecr_repository_arns              = var.ecr_repository_arns
  secret_arns                      = var.secret_arns
  scp_restrictions                 = var.scp_restrictions

  tags = merge(
    var.tags,
    {
      Environment = var.environment
    }
  )
}

# Create ECS task execution role
module "ecs_task_execution_role" {
  source = "../../../modules/iam/roles"

  name_prefix                    = "${var.environment}-${var.project}"
  create_ecs_task_execution_role = true
  # Only attach Secrets Manager policy when there are no SCP restrictions
  custom_policy_arns = var.scp_restrictions ? [
    module.ecr_access_policy.ecr_access_policy_arn
    ] : [
    module.ecr_access_policy.ecr_access_policy_arn,
    module.secrets_manager_policy.secrets_manager_policy_arn
  ]

  tags = merge(
    var.tags,
    {
      Environment = var.environment
    }
  )
}
