# IAM Stack for ECS - outputs.tf
# Outputs from the IAM stack

output "ecr_access_policy_arn" {
  description = "ARN of the ECR access policy"
  value       = module.ecr_access_policy.ecr_access_policy_arn
}

output "secrets_manager_policy_arn" {
  description = "ARN of the Secrets Manager access policy"
  value       = module.secrets_manager_policy.secrets_manager_policy_arn
}

output "ecs_task_execution_policy_arn" {
  description = "ARN of the combined ECS task execution policy"
  value       = module.ecs_task_execution_policy.ecs_task_execution_policy_arn
}

output "ecs_task_execution_role_arn" {
  description = "ARN of the ECS task execution role"
  value       = module.ecs_task_execution_role.ecs_task_execution_role_arn
}

output "ecs_task_execution_role_name" {
  description = "Name of the ECS task execution role"
  value       = module.ecs_task_execution_role.ecs_task_execution_role_name
}
