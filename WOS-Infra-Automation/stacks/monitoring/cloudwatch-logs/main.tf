# CloudWatch Logs Stack - main.tf
# This stack creates CloudWatch log groups for applications

resource "aws_cloudwatch_log_group" "sembwaste_dashboard" {
  name              = "/aws/ecs/wos-${var.environment}-sembwaste-dashboard"
  retention_in_days = var.log_retention_days

  tags = merge(
    var.tags,
    {
      Name        = "/aws/ecs/wos-${var.environment}-sembwaste-dashboard"
      Environment = var.environment
      Application = "sembwaste-dashboard"
    }
  )
}
