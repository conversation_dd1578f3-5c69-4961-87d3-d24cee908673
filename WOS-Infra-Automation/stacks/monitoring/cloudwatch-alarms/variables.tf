# CloudWatch Alarms Stack - variables.tf
# Variables for the CloudWatch alarms stack

variable "environment" {
  description = "Environment name"
  type        = string
}

variable "target_group_arn_suffix" {
  description = "The ARN suffix of the target group"
  type        = string
}

variable "load_balancer_arn_suffix" {
  description = "The ARN suffix of the load balancer"
  type        = string
}

variable "tags" {
  description = "Tags to apply to all resources"
  type        = map(string)
  default     = {}
}
