# CloudWatch Alarms Stack - main.tf
# This stack creates CloudWatch alarms for applications

# ALB 5XX Error Rate Alarm
resource "aws_cloudwatch_metric_alarm" "alb_5xx_errors" {
  alarm_name          = "wos-${var.environment}-sembwaste-dashboard-alb-5xx"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 2
  metric_name         = "HTTPCode_Target_5XX_Count"
  namespace           = "AWS/ApplicationELB"
  period              = 300
  statistic           = "Sum"
  threshold           = 5
  alarm_description   = "This alarm monitors for high 5XX error rates from the sembwaste-dashboard ALB target group"
  treat_missing_data  = "notBreaching"

  dimensions = {
    TargetGroup  = var.target_group_arn_suffix
    LoadBalancer = var.load_balancer_arn_suffix
  }

  tags = merge(
    var.tags,
    {
      Name        = "wos-${var.environment}-sembwaste-dashboard-alb-5xx"
      Environment = var.environment
      Application = "sembwaste-dashboard"
    }
  )
}

# Target Group Health Alarm
resource "aws_cloudwatch_metric_alarm" "target_group_health" {
  alarm_name          = "wos-${var.environment}-sembwaste-dashboard-target-health"
  comparison_operator = "LessThanThreshold"
  evaluation_periods  = 2
  metric_name         = "HealthyHostCount"
  namespace           = "AWS/ApplicationELB"
  period              = 300
  statistic           = "Average"
  threshold           = 1
  alarm_description   = "This alarm monitors for unhealthy hosts in the sembwaste-dashboard target group"
  treat_missing_data  = "breaching"

  dimensions = {
    TargetGroup  = var.target_group_arn_suffix
    LoadBalancer = var.load_balancer_arn_suffix
  }

  tags = merge(
    var.tags,
    {
      Name        = "wos-${var.environment}-sembwaste-dashboard-target-health"
      Environment = var.environment
      Application = "sembwaste-dashboard"
    }
  )
}

# High CPU Utilization Alarm
resource "aws_cloudwatch_metric_alarm" "high_cpu" {
  alarm_name          = "wos-${var.environment}-sembwaste-dashboard-high-cpu"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 3
  metric_name         = "CPUUtilization"
  namespace           = "AWS/ECS"
  period              = 300
  statistic           = "Average"
  threshold           = 80
  alarm_description   = "This alarm monitors for high CPU utilization in the sembwaste-dashboard ECS service"
  treat_missing_data  = "notBreaching"

  dimensions = {
    ClusterName = "wos-${var.environment}-ecs-cluster"
    ServiceName = "wos-${var.environment}-sembwaste-dashboard"
  }

  tags = merge(
    var.tags,
    {
      Name        = "wos-${var.environment}-sembwaste-dashboard-high-cpu"
      Environment = var.environment
      Application = "sembwaste-dashboard"
    }
  )
}

# High Memory Utilization Alarm
resource "aws_cloudwatch_metric_alarm" "high_memory" {
  alarm_name          = "wos-${var.environment}-sembwaste-dashboard-high-memory"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 3
  metric_name         = "MemoryUtilization"
  namespace           = "AWS/ECS"
  period              = 300
  statistic           = "Average"
  threshold           = 80
  alarm_description   = "This alarm monitors for high memory utilization in the sembwaste-dashboard ECS service"
  treat_missing_data  = "notBreaching"

  dimensions = {
    ClusterName = "wos-${var.environment}-ecs-cluster"
    ServiceName = "wos-${var.environment}-sembwaste-dashboard"
  }

  tags = merge(
    var.tags,
    {
      Name        = "wos-${var.environment}-sembwaste-dashboard-high-memory"
      Environment = var.environment
      Application = "sembwaste-dashboard"
    }
  )
}
