# CloudWatch Logs Stack for THECHATBOT - main.tf
# This stack creates a CloudWatch log group for the THECHATBOT app

module "thechatbot_logs" {
  source = "../../../modules/monitoring/cloudwatch_logs"

  name              = "/ecs/wos-${var.environment}-thechatbot"
  retention_in_days = var.log_retention_days

  tags = merge(
    var.tags,
    {
      Name        = "/ecs/wos-${var.environment}-thechatbot"
      Environment = var.environment
    }
  )
}
