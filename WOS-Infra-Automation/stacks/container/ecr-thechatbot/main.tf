# ECR Stack for THECHATBOT - main.tf
# This stack creates an ECR repository for the THECHATBOT app

module "ecr_thechatbot" {
  source = "../../../modules/container/ecr"

  repository_name      = "wos-${var.environment}-thechatbot"
  image_tag_mutability = "MUTABLE"
  scan_on_push         = true

  lifecycle_policy = jsonencode({
    rules = [
      {
        rulePriority = 1
        description  = "Keep only the last 10 images"
        selection = {
          tagStatus   = "any"
          countType   = "imageCountMoreThan"
          countNumber = 10
        }
        action = {
          type = "expire"
        }
      }
    ]
  })

  tags = merge(
    var.tags,
    {
      Name        = "wos-${var.environment}-thechatbot"
      Environment = var.environment
    }
  )
}
