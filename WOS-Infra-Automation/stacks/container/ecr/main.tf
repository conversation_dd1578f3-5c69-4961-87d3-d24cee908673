# ECR Repository Stack - main.tf
# This stack creates an ECR repository for the sample app

module "sample_app_ecr" {
  source = "../../../modules/container/ecr"

  repository_name      = "wos-${var.environment}-${var.repository_name}"
  image_tag_mutability = var.image_tag_mutability
  scan_on_push         = var.scan_on_push
  encryption_type      = var.encryption_type

  # Default lifecycle policy to keep only the latest 10 images
  lifecycle_policy = jsonencode({
    rules = [
      {
        rulePriority = 1
        description  = "Keep only the latest 10 images"
        selection = {
          tagStatus   = "any"
          countType   = "imageCountMoreThan"
          countNumber = 10
        }
        action = {
          type = "expire"
        }
      }
    ]
  })

  tags = merge(
    var.tags,
    {
      Environment = var.environment
    }
  )
}
