# ALB Stack for API Services - outputs.tf
# Outputs from the ALB stack for API services

output "api_target_group_arn" {
  description = "ARN of the API target group"
  value       = aws_lb_target_group.api.arn
}

output "api_target_group_name" {
  description = "Name of the API target group"
  value       = aws_lb_target_group.api.name
}


# Output aliases for backward compatibility
output "target_group_arn" {
  description = "ARN of the target group (alias for api_target_group_arn)"
  value       = aws_lb_target_group.api.arn
}

output "target_group_name" {
  description = "Name of the target group (alias for api_target_group_name)"
  value       = aws_lb_target_group.api.name
}

# Add wos target group outputs that reference the api target group
output "wos_target_group_arn" {
  description = "ARN of the WOS target group (same as API target group)"
  value       = aws_lb_target_group.api.arn
}

output "wos_target_group_name" {
  description = "Name of the WOS target group (same as API target group)"
  value       = aws_lb_target_group.api.name
}

output "api_https_listener_rule_arn" {
  description = "ARN of the API HTTPS listener rule"
  value       = var.https_listener_arn != "" ? aws_lb_listener_rule.api_host_https[0].arn : null
}

output "wos_https_listener_rule_arn" {
  description = "ARN of the WOS HTTPS listener rule"
  value       = var.https_listener_arn != "" ? aws_lb_listener_rule.wos_host_https[0].arn : null
}
