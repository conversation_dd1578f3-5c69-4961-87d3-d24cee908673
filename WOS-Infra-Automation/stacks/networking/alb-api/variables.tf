# ALB Stack for API Services - variables.tf
# Variables for the ALB stack for API services

variable "environment" {
  description = "Environment name"
  type        = string
}

variable "vpc_id" {
  description = "VPC ID where the ALB will be created"
  type        = string
}

variable "http_listener_arn" {
  description = "ARN of the HTTP listener to attach the rule to"
  type        = string
}

variable "https_listener_arn" {
  description = "ARN of the HTTPS listener to attach the rule to"
  type        = string
  default     = ""
}

variable "domain_name" {
  description = "Base domain name for the environment (e.g., dev.wasteos.net)"
  type        = string
  default     = "wasteos.net"
}

variable "enable_http_to_https_redirect" {
  description = "Whether to create a redirect rule from HTTP to HTTPS"
  type        = bool
  default     = true
}

variable "health_check_settings" {
  description = "Settings for the health check"
  type        = object({
    enabled             = bool
    interval            = number
    path                = string
    port                = string
    protocol            = string
    timeout             = number
    healthy_threshold   = number
    unhealthy_threshold = number
    matcher             = string
  })
  default = {
    enabled             = true
    interval            = 30
    path                = "/"
    port                = "traffic-port"
    protocol            = "HTTP"
    timeout             = 5
    healthy_threshold   = 2
    unhealthy_threshold = 3
    matcher             = "200-299"
  }
}

variable "alb_security_group_id" {
  description = "Security group ID of the ALB for adding ingress rules"
  type        = string
}

variable "alb_dns_name" {
  description = "DNS name of the ALB for Route53 alias records"
  type        = string
}

variable "alb_zone_id" {
  description = "Hosted zone ID of the ALB for Route53 alias records"
  type        = string
}

variable "route53_zone_id" {
  description = "Hosted zone ID for the domain to create DNS records in"
  type        = string
}

variable "tags" {
  description = "Tags to apply to all resources"
  type        = map(string)
  default     = {}
}

variable "listener_rule_priority_api" {
  description = "Priority for the API listener rule"
  type        = number
  default     = 30
}

variable "listener_rule_priority_wos" {
  description = "Priority for the WOS listener rule"
  type        = number
  default     = 40
}

variable "target_group_port" {
  description = "Port for the target group"
  type        = number
  default     = 8080
}

variable "https_port" {
  description = "Port for HTTPS traffic"
  type        = number
  default     = 443
}

variable "allowed_cidr_blocks" {
  description = "CIDR blocks to allow traffic from"
  type        = list(string)
  default     = ["0.0.0.0/0"]
}

variable "deregistration_delay" {
  description = "Amount of time for Elastic Load Balancing to wait before changing the state of a deregistering target from draining to unused"
  type        = number
  default     = 300
}
