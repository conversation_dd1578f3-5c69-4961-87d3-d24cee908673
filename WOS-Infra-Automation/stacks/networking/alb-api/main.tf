
resource "aws_lb_target_group" "api" {
  name                 = "wos-${var.environment}-api-tg"
  port                 = 3000  # Update port to match actual ECS service port
  protocol             = "HTTP"
  vpc_id               = var.vpc_id
  target_type          = "ip"
  deregistration_delay = var.deregistration_delay

  health_check {
    enabled             = var.health_check_settings.enabled
    interval            = var.health_check_settings.interval
    path                = var.health_check_settings.path
    port                = var.health_check_settings.port
    protocol            = var.health_check_settings.protocol
    timeout             = var.health_check_settings.timeout
    healthy_threshold   = var.health_check_settings.healthy_threshold
    unhealthy_threshold = var.health_check_settings.unhealthy_threshold
    matcher             = var.health_check_settings.matcher
  }

  tags = merge(
    var.tags,
    {
      Name        = "wos-${var.environment}-api-tg"
      Environment = var.environment
    }
  )
}

# Create a host-based listener rule for api.<env>.wasteos.net on HTTPS listener
resource "aws_lb_listener_rule" "api_host_https" {
  count        = var.https_listener_arn != "" ? 1 : 0
  listener_arn = var.https_listener_arn
  priority     = var.listener_rule_priority_api

  action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.api.arn
  }

  condition {
    host_header {
      values = ["api.${var.environment}.${var.domain_name}"]
    }
  }

  tags = merge(
    var.tags,
    {
      Name        = "wos-${var.environment}-api-host-rule-https"
      Environment = var.environment
    }
  )
}

# Create a host-based listener rule for api.<env>.wasteos.net on HTTP listener (kept for backward compatibility)
resource "aws_lb_listener_rule" "api_host_http" {
  listener_arn = var.http_listener_arn
  priority     = var.listener_rule_priority_api

  dynamic "action" {
    for_each = var.enable_http_to_https_redirect && var.https_listener_arn != "" ? [1] : []
    content {
      type = "redirect"
      redirect {
        port        = tostring(var.https_port)
        protocol    = "HTTPS"
        status_code = "HTTP_301"
      }
    }
  }

  dynamic "action" {
    for_each = !var.enable_http_to_https_redirect || var.https_listener_arn == "" ? [1] : []
    content {
      type             = "forward"
      target_group_arn = aws_lb_target_group.api.arn
    }
  }

  condition {
    host_header {
      values = ["api.${var.environment}.${var.domain_name}"]
    }
  }

  tags = merge(
    var.tags,
    {
      Name        = "wos-${var.environment}-api-host-rule-http"
      Environment = var.environment
    }
  )
}

# Create a host-based listener rule for wos.<env>.wasteos.net on HTTPS listener
resource "aws_lb_listener_rule" "wos_host_https" {
  count        = var.https_listener_arn != "" ? 1 : 0
  listener_arn = var.https_listener_arn
  priority     = var.listener_rule_priority_wos

  action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.api.arn
  }

  condition {
    host_header {
      values = ["wos.${var.environment}.${var.domain_name}"]
    }
  }

  tags = merge(
    var.tags,
    {
      Name        = "wos-${var.environment}-wos-host-rule-https"
      Environment = var.environment
    }
  )
}

# Create a host-based listener rule for wos.<env>.wasteos.net on HTTP listener (kept for backward compatibility)
resource "aws_lb_listener_rule" "wos_host_http" {
  listener_arn = var.http_listener_arn
  priority     = var.listener_rule_priority_wos

  dynamic "action" {
    for_each = var.enable_http_to_https_redirect && var.https_listener_arn != "" ? [1] : []
    content {
      type = "redirect"
      redirect {
        port        = tostring(var.https_port)
        protocol    = "HTTPS"
        status_code = "HTTP_301"
      }
    }
  }

  dynamic "action" {
    for_each = !var.enable_http_to_https_redirect || var.https_listener_arn == "" ? [1] : []
    content {
      type             = "forward"
      target_group_arn = aws_lb_target_group.api.arn
    }
  }

  condition {
    host_header {
      values = ["wos.${var.environment}.${var.domain_name}"]
    }
  }

  tags = merge(
    var.tags,
    {
      Name        = "wos-${var.environment}-wos-host-rule-http"
      Environment = var.environment
    }
  )
}

# Add HTTPS ingress rule to ALB security group

# Create Route53 alias record for api subdomain
resource "aws_route53_record" "api" {
  zone_id = var.route53_zone_id
  name    = "api.${var.environment}.${var.domain_name}"
  type    = "A"

  alias {
    name                   = var.alb_dns_name
    zone_id                = var.alb_zone_id
    evaluate_target_health = true
  }
}

# Create Route53 alias record for wos subdomain
resource "aws_route53_record" "wos" {
  zone_id = var.route53_zone_id
  name    = "wos.${var.environment}.${var.domain_name}"
  type    = "A"

  alias {
    name                   = var.alb_dns_name
    zone_id                = var.alb_zone_id
    evaluate_target_health = true
  }
}
