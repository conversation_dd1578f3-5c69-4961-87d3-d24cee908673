# ALB Stack for THECHATBOT - main.tf
# This stack creates a target group and listener rule for the THECHATBOT app

resource "aws_lb_target_group" "thechatbot" {
  name                 = "wos-${var.environment}-thechatbot-tg"
  port                 = 8000
  protocol             = "HTTP"
  vpc_id               = var.vpc_id
  target_type          = "ip"
  deregistration_delay = 300

  health_check {
    enabled             = true
    interval            = 60
    path                = "/healthz" # Use lightweight health endpoint
    port                = "traffic-port"
    protocol            = "HTTP"
    timeout             = 30
    healthy_threshold   = 2
    unhealthy_threshold = 5
    matcher             = "200"
  }

  tags = merge(
    var.tags,
    {
      Name        = "wos-${var.environment}-thechatbot-tg"
      Environment = var.environment
    }
  )
}

# Create a host-based listener rule for the THECHATBOT app
resource "aws_lb_listener_rule" "thechatbot_host" {
  listener_arn = var.http_listener_arn
  priority     = 20 # Adjust priority as needed

  action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.thechatbot.arn
  }

  condition {
    host_header {
      values = ["chatbot.${var.environment}.wasteos.net"]
    }
  }

  tags = merge(
    var.tags,
    {
      Name        = "wos-${var.environment}-thechatbot-host-rule"
      Environment = var.environment
    }
  )
}
