# ALB Stack for THECHATBOT - variables.tf
# Variables for the ALB stack for THECHATBOT

variable "environment" {
  description = "Environment name"
  type        = string
}

variable "vpc_id" {
  description = "VPC ID where the ALB will be created"
  type        = string
}

variable "http_listener_arn" {
  description = "ARN of the HTTP listener to attach the rule to"
  type        = string
}

variable "tags" {
  description = "Tags to apply to all resources"
  type        = map(string)
  default     = {}
}
