# Route53 Stack - main.tf
# This stack creates Route53 records for the WasteOS platform

module "route53" {
  source = "../../../modules/networking/route53"

  environment             = var.environment
  domain_name             = var.domain_name
  hosted_zone_id          = var.hosted_zone_id
  alb_dns_name            = var.alb_dns_name
  alb_zone_id             = var.alb_zone_id
  create_api_record       = var.create_api_record
  create_dashboard_record = var.create_dashboard_record
  create_chatbot_record   = var.create_chatbot_record
}
