# ALB Stack for Sembwaste Dashboard - main.tf
# This stack creates a target group and listener rule for the sembwaste-dashboard app

resource "aws_lb_target_group" "sembwaste_dashboard" {
  name                 = "wos-${var.environment}-sembwaste-dashboard-tg"
  port                 = 3000
  protocol             = "HTTP"
  vpc_id               = var.vpc_id
  target_type          = "ip"
  deregistration_delay = 300

  health_check {
    enabled             = true
    interval            = var.sembwaste_dashboard_health_check.interval
    path                = var.sembwaste_dashboard_health_check.path
    port                = "traffic-port"
    protocol            = "HTTP"
    timeout             = 30
    healthy_threshold   = 2
    unhealthy_threshold = var.sembwaste_dashboard_health_check.unhealthy_threshold
    matcher             = var.sembwaste_dashboard_health_check.matcher
  }

  lifecycle {
    ignore_changes = [tags]
  }

  tags = merge(
    var.tags,
    {
      Name        = "wos-${var.environment}-sembwaste-dashboard-tg"
      Environment = var.environment
    }
  )
}

# Create a host-based listener rule for the sembwaste-dashboard app
resource "aws_lb_listener_rule" "sembwaste_dashboard_host" {
  listener_arn = var.http_listener_arn
  priority     = 10

  action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.sembwaste_dashboard.arn
  }

  condition {
    host_header {
      values = ["dashboard.${var.environment}.wasteos.net"]
    }
  }

  tags = merge(
    var.tags,
    {
      Name        = "wos-${var.environment}-sembwaste-dashboard-host-rule"
      Environment = var.environment
    }
  )
}

# Path-based listener rule for the sembwaste-dashboard app
# This rule routes all traffic to the sembwaste-dashboard target group regardless of the host header
resource "aws_lb_listener_rule" "sembwaste_dashboard_path" {
  listener_arn = var.http_listener_arn
  priority     = 5 # Higher priority (lower number) than the host-based rule

  action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.sembwaste_dashboard.arn
  }

  condition {
    path_pattern {
      values = ["/*"]
    }
  }

  tags = merge(
    var.tags,
    {
      Name        = "wos-${var.environment}-sembwaste-dashboard-path-rule"
      Environment = var.environment
    }
  )
}
