# ALB Stack for Sembwaste Dashboard - variables.tf
# Variables for the ALB stack for sembwaste-dashboard

variable "environment" {
  description = "Environment name"
  type        = string
}

variable "vpc_id" {
  description = "VPC ID where the ALB will be created"
  type        = string
}

variable "http_listener_arn" {
  description = "ARN of the HTTP listener to attach the rule to"
  type        = string
}

variable "tags" {
  description = "Tags to apply to all resources"
  type        = map(string)
  default     = {}
}

variable "sembwaste_dashboard_health_check" {
  description = "Health check settings for sembwaste dashboard target group"
  type = object({
    interval            = number
    matcher             = string
    path                = string
    unhealthy_threshold = number
  })
  default = {
    interval            = 60
    matcher             = "200"
    path                = "/api/health"
    unhealthy_threshold = 5
  }
}
