# VPC Stack - main.tf
# This stack creates a VPC using the VPC module

module "vpc" {
  source = "../../../modules/networking/vpc"

  name_prefix = "wos-${var.environment}"
  vpc_cidr    = var.vpc_cidr
  region      = var.region

  public_subnets      = var.public_subnets
  private_app_subnets = var.private_app_subnets
  private_db_subnets  = var.private_db_subnets

  azs         = var.azs
  az_suffixes = var.az_suffixes

  enable_dns_hostnames    = var.enable_dns_hostnames
  enable_dns_support      = var.enable_dns_support
  map_public_ip_on_launch = var.map_public_ip_on_launch

  create_nat_gateway   = var.create_nat_gateway
  create_vpc_endpoints = var.create_vpc_endpoints
  create_vpn_access_sg = var.create_vpn_access_sg

  tags = merge(
    var.tags,
    {
      Environment = var.environment
    }
  )
}
