# VPC Stack - outputs.tf
# Outputs from the VPC stack

output "vpc_id" {
  description = "The ID of the VPC"
  value       = module.vpc.vpc_id
}

output "vpc_cidr_block" {
  description = "The CIDR block of the VPC"
  value       = module.vpc.vpc_cidr_block
}

output "vpc_arn" {
  description = "The ARN of the VPC"
  value       = module.vpc.vpc_arn
}

output "internet_gateway_id" {
  description = "The ID of the Internet Gateway"
  value       = module.vpc.internet_gateway_id
}

output "public_subnet_ids" {
  description = "List of IDs of public subnets"
  value       = module.vpc.public_subnet_ids
}

output "public_subnet_arns" {
  description = "List of ARNs of public subnets"
  value       = module.vpc.public_subnet_arns
}

output "public_subnet_cidrs" {
  description = "List of CIDR blocks of public subnets"
  value       = module.vpc.public_subnet_cidrs
}

output "private_app_subnet_ids" {
  description = "List of IDs of private application subnets"
  value       = module.vpc.private_app_subnet_ids
}

output "private_app_subnet_arns" {
  description = "List of ARNs of private application subnets"
  value       = module.vpc.private_app_subnet_arns
}

output "private_app_subnet_cidrs" {
  description = "List of CIDR blocks of private application subnets"
  value       = module.vpc.private_app_subnet_cidrs
}

output "private_db_subnet_ids" {
  description = "List of IDs of private database subnets"
  value       = module.vpc.private_db_subnet_ids
}

output "private_db_subnet_arns" {
  description = "List of ARNs of private database subnets"
  value       = module.vpc.private_db_subnet_arns
}

output "private_db_subnet_cidrs" {
  description = "List of CIDR blocks of private database subnets"
  value       = module.vpc.private_db_subnet_cidrs
}

output "nat_gateway_ids" {
  description = "List of NAT Gateway IDs"
  value       = module.vpc.nat_gateway_ids
}

output "nat_gateway_public_ips" {
  description = "List of public Elastic IPs created for NAT Gateways"
  value       = module.vpc.nat_gateway_public_ips
}

output "public_route_table_ids" {
  description = "List of IDs of public route tables"
  value       = module.vpc.public_route_table_ids
}

output "private_app_route_table_ids" {
  description = "List of IDs of private application route tables"
  value       = module.vpc.private_app_route_table_ids
}

output "private_db_route_table_ids" {
  description = "List of IDs of private database route tables"
  value       = module.vpc.private_db_route_table_ids
}

output "alb_security_group_id" {
  description = "ID of the ALB security group"
  value       = module.vpc.alb_security_group_id
}

output "eks_cluster_security_group_id" {
  description = "ID of the EKS cluster security group"
  value       = module.vpc.eks_cluster_security_group_id
}

output "rds_security_group_id" {
  description = "ID of the RDS security group"
  value       = module.vpc.rds_security_group_id
}

output "redis_security_group_id" {
  description = "ID of the Redis security group"
  value       = module.vpc.redis_security_group_id
}

output "vpn_access_security_group_id" {
  description = "ID of the VPN access security group"
  value       = module.vpc.vpn_access_security_group_id
}

output "vpc_endpoint_s3_id" {
  description = "ID of the S3 VPC endpoint"
  value       = module.vpc.vpc_endpoint_s3_id
}

output "vpc_endpoint_dynamodb_id" {
  description = "ID of the DynamoDB VPC endpoint"
  value       = module.vpc.vpc_endpoint_dynamodb_id
}

output "vpc_endpoint_ids" {
  description = "Map of VPC endpoint IDs"
  value       = module.vpc.vpc_endpoint_ids
}
