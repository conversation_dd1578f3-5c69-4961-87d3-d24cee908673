# ALB Stack - main.tf
# This stack creates an Application Load Balancer for the sample app

module "alb" {
  source = "../../../modules/networking/alb"

  name               = "wos-${var.environment}-alb"
  internal           = false
  security_group_ids = [var.alb_security_group_id]
  subnet_ids         = var.public_subnet_ids
  vpc_id             = var.vpc_id

  target_group_port     = 8080
  target_group_protocol = "HTTP"
  target_type           = "ip"

  health_check_path                = "/health"
  health_check_matcher             = "200"
  health_check_interval            = 30
  health_check_timeout             = 5
  health_check_healthy_threshold   = 2
  health_check_unhealthy_threshold = 3

  # Enable these if you have a certificate
  certificate_arn        = var.certificate_arn
  http_to_https_redirect = var.certificate_arn != "" ? true : false
  ssl_policy             = "ELBSecurityPolicy-TLS-1-2-2017-01"

  # Access logs
  access_logs_bucket = var.access_logs_bucket
  access_logs_prefix = "alb-logs/${var.environment}"

  # Additional settings
  idle_timeout               = 60
  enable_deletion_protection = var.environment == "prod" ? true : false
  drop_invalid_header_fields = true

  tags = merge(
    var.tags,
    {
      Environment = var.environment
    }
  )
}
