# ECS Stack - outputs.tf
# Outputs from the ECS stack

output "cluster_id" {
  description = "The ID of the ECS cluster"
  value       = module.ecs.cluster_id
}

output "cluster_arn" {
  description = "The ARN of the ECS cluster"
  value       = module.ecs.cluster_arn
}

output "cluster_name" {
  description = "The name of the ECS cluster"
  value       = module.ecs.cluster_name
}

output "ecs_security_group_id" {
  description = "The ID of the ECS security group"
  value       = module.ecs.ecs_security_group_id
}

output "ecs_task_execution_role_arn" {
  description = "The ARN of the ECS task execution role"
  value       = module.ecs.ecs_task_execution_role_arn
}

output "ecs_task_execution_role_name" {
  description = "The name of the ECS task execution role"
  value       = module.ecs.ecs_task_execution_role_name
}

output "ecs_task_role_access_policy_arn" {
  description = "The ARN of the ECS task role access policy"
  value       = module.ecs.ecs_task_role_access_policy_arn
}

output "ecs_cloudwatch_logs_policy_arn" {
  description = "The ARN of the ECS CloudWatch Logs policy"
  value       = module.ecs.ecs_cloudwatch_logs_policy_arn
}

output "ecs_ssm_policy_arn" {
  description = "The ARN of the ECS SSM policy"
  value       = module.ecs.ecs_ssm_policy_arn
}

output "ecs_kms_policy_arn" {
  description = "The ARN of the ECS KMS policy"
  value       = module.ecs.ecs_kms_policy_arn
}
