# ECS Stack - main.tf
# This stack creates an ECS cluster using the ECS module

module "ecs" {
  source = "../../../modules/compute/ecs"

  name_prefix             = "wos-${var.environment}"
  vpc_id                  = var.vpc_id
  vpc_cidr                = var.vpc_cidr
  secret_arns             = var.secret_arns
  s3_bucket_arns          = var.s3_bucket_arns
  task_execution_role_arn = var.task_execution_role_arn

  tags = merge(
    var.tags,
    {
      Environment = var.environment
    }
  )
}
