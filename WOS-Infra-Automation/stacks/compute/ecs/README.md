# AWS ECS Cluster Stack

This stack creates an Amazon Elastic Container Service (ECS) cluster for the WasteOS platform.

## Features

- Creates an ECS cluster with Container Insights enabled
- Configures capacity providers (FARGATE and FARGATE_SPOT)
- Creates security group for ECS tasks with appropriate ingress/egress rules
- Creates IAM role for ECS task execution with necessary permissions
- Creates custom IAM policy for ECS tasks to access AWS services

## Usage

```hcl
module "ecs" {
  source = "../../stacks/compute/ecs"

  environment   = var.environment
  vpc_id        = module.vpc.vpc_id
  vpc_cidr      = var.vpc_cidr
  secret_arns   = var.secret_arns
  s3_bucket_arns = var.s3_bucket_arns

  tags = var.tags
}
```

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| environment | Environment name | `string` | n/a | yes |
| vpc_id | ID of the VPC where the ECS cluster will be created | `string` | n/a | yes |
| vpc_cidr | CIDR block of the VPC | `string` | n/a | yes |
| secret_arns | ARNs of secrets to grant access to | `list(string)` | `["*"]` | no |
| s3_bucket_arns | ARNs of S3 buckets to grant access to | `list(string)` | `["*"]` | no |
| tags | Tags to apply to all resources | `map(string)` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| cluster_id | The ID of the ECS cluster |
| cluster_arn | The ARN of the ECS cluster |
| cluster_name | The name of the ECS cluster |
| ecs_security_group_id | The ID of the ECS security group |
| ecs_task_execution_role_arn | The ARN of the ECS task execution role |
| ecs_task_execution_role_name | The name of the ECS task execution role |
| ecs_task_role_access_policy_arn | The ARN of the ECS task role access policy |

## Resources Created

This stack uses the ECS module to create the following resources:

- ECS Cluster
- ECS Cluster Capacity Providers
- Security Group for ECS Tasks
- IAM Role for ECS Task Execution
- IAM Policy for ECS Task Access
- IAM Role Policy Attachments

## Notes

- The ECS cluster uses both FARGATE and FARGATE_SPOT capacity providers
- The default capacity provider strategy uses 40% FARGATE and 60% FARGATE_SPOT
- The security group allows traffic on ports 8000 (TCP), 2000 (UDP), and 8443 (TCP) from within the VPC
- The IAM role has permissions for ECS task execution, Secrets Manager, CloudWatch Logs, and S3
