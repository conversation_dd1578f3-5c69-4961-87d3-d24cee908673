/**
 * # Bastion Host Stack Outputs
 *
 * Outputs from the Bastion Host stack
 */

output "bastion_instance_id" {
  description = "ID of the bastion host EC2 instance"
  value       = module.bastion.bastion_instance_id
}

output "bastion_public_ip" {
  description = "Public IP address of the bastion host"
  value       = module.bastion.bastion_public_ip
}

output "bastion_private_ip" {
  description = "Private IP address of the bastion host"
  value       = module.bastion.bastion_private_ip
}

output "bastion_security_group_id" {
  description = "ID of the bastion host security group"
  value       = module.bastion.bastion_security_group_id
}

output "bastion_iam_role_arn" {
  description = "ARN of the bastion host IAM role"
  value       = module.bastion.bastion_iam_role_arn
}

output "bastion_ssh_command" {
  description = "SSH command to connect to the bastion host"
  value       = module.bastion.bastion_ssh_command
}

output "bastion_tunnel_command" {
  description = "SSH tunnel command to connect to RDS through the bastion host"
  value       = module.bastion.bastion_tunnel_command
}

output "bastion_alarms_topic_arn" {
  description = "ARN of the SNS topic for bastion host alarms"
  value       = aws_sns_topic.bastion_alarms.arn
}
