/**
 * # Bastion Host Stack
 *
 * This stack deploys a bastion host for secure access to private resources.
 */


# Data source to get the RDS security group
data "aws_security_group" "rds" {
  filter {
    name   = "tag:Name"
    values = ["wos-${var.environment}-rds-sg"]
  }
}

# Create an SNS topic for bastion host alarms
resource "aws_sns_topic" "bastion_alarms" {
  name = "wos-${var.environment}-bastion-alarms"

  tags = merge(
    var.tags,
    {
      Name = "wos-${var.environment}-bastion-alarms"
    }
  )
}

# Create the bastion host using the module
module "bastion" {
  source = "../../../modules/compute/bastion"

  environment             = var.environment
  region                  = var.region
  account_id              = var.account_id
  vpc_id                  = var.vpc_id
  vpc_cidr                = var.vpc_cidr
  subnet_id               = var.public_subnet_id
  allowed_ssh_cidr_blocks = var.allowed_ssh_cidr_blocks
  rds_security_group_id   = data.aws_security_group.rds.id
  instance_type           = var.instance_type
  key_name                = var.key_name
  root_volume_size        = var.root_volume_size

  alarm_actions = [aws_sns_topic.bastion_alarms.arn]
  ok_actions    = [aws_sns_topic.bastion_alarms.arn]

  tags = var.tags
}

# Store the bastion host public IP in SSM Parameter Store for easy access
resource "aws_ssm_parameter" "bastion_public_ip" {
  name        = "/wos/${var.environment}/bastion/public_ip"
  description = "Public IP address of the bastion host"
  type        = "String"
  value       = module.bastion.bastion_public_ip

  tags = merge(
    var.tags,
    {
      Name = "wos-${var.environment}-bastion-public-ip"
    }
  )
}

# Store the SSH command in SSM Parameter Store for easy access
resource "aws_ssm_parameter" "bastion_ssh_command" {
  name        = "/wos/${var.environment}/bastion/ssh_command"
  description = "SSH command to connect to the bastion host"
  type        = "String"
  value       = "ssh -i /path/to/${var.key_name}.pem ec2-user@${module.bastion.bastion_public_ip}"

  tags = merge(
    var.tags,
    {
      Name = "wos-${var.environment}-bastion-ssh-command"
    }
  )
}
