# Bastion Host Stack

This stack deploys a bastion host for secure access to private resources, particularly RDS instances. It uses the bastion host module to create the necessary AWS resources.

## Features

- Automatically selects the latest Amazon Linux 2023 AMI
- Creates an SNS topic for bastion host alarms
- Stores the bastion host public IP and SSH command in SSM Parameter Store
- Configures security groups, IAM roles, and CloudWatch alarms

## Usage

To deploy this stack, include it in your environment's main.tf file:

```hcl
module "bastion" {
  source = "../../stacks/compute/bastion"

  environment            = local.environment
  region                 = local.region
  account_id             = local.account_id
  vpc_id                 = module.networking.vpc_id
  vpc_cidr               = module.networking.vpc_cidr
  public_subnet_id       = module.networking.public_subnets[0]
  allowed_ssh_cidr_blocks = ["10.0.0.0/8", "***********/24"]
  key_name               = "wos-${local.environment}-key"
  
  tags = local.tags
}
```

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|----------|
| environment | Environment name (e.g., dev, uat, prod) | `string` | n/a | yes |
| region | AWS region | `string` | n/a | yes |
| account_id | AWS account ID | `string` | n/a | yes |
| vpc_id | ID of the VPC where the bastion host will be deployed | `string` | n/a | yes |
| vpc_cidr | CIDR block of the VPC | `string` | n/a | yes |
| public_subnet_id | ID of the public subnet where the bastion host will be deployed | `string` | n/a | yes |
| allowed_ssh_cidr_blocks | List of CIDR blocks allowed to SSH to the bastion host | `list(string)` | n/a | yes |
| instance_type | EC2 instance type for the bastion host | `string` | `"t3a.micro"` | no |
| key_name | Name of the SSH key pair to use for the bastion host | `string` | n/a | yes |
| root_volume_size | Size of the root volume in GB | `number` | `20` | no |
| tags | Tags to apply to all resources | `map(string)` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| bastion_instance_id | ID of the bastion host EC2 instance |
| bastion_public_ip | Public IP address of the bastion host |
| bastion_private_ip | Private IP address of the bastion host |
| bastion_security_group_id | ID of the bastion host security group |
| bastion_iam_role_arn | ARN of the bastion host IAM role |
| bastion_ssh_command | SSH command to connect to the bastion host |
| bastion_tunnel_command | SSH tunnel command to connect to RDS through the bastion host |
| bastion_alarms_topic_arn | ARN of the SNS topic for bastion host alarms |

## Accessing the Bastion Host

After deploying the stack, you can access the bastion host using the following methods:

1. **Using the SSM Parameter Store**:
   ```bash
   aws ssm get-parameter --name "/wos/dev/bastion/ssh_command" --query "Parameter.Value" --output text
   ```

2. **Using the Terraform output**:
   ```bash
   terraform output -module=bastion bastion_ssh_command
   ```

3. **Directly using the public IP**:
   ```bash
   ssh -i /path/to/key.pem ec2-user@<BASTION_PUBLIC_IP>
   ```

## Connecting to RDS via the Bastion Host

To connect to an RDS instance through the bastion host:

1. **Create an SSH tunnel**:
   ```bash
   ssh -i /path/to/key.pem -L 5432:<RDS_ENDPOINT>:5432 ec2-user@<BASTION_PUBLIC_IP>
   ```

2. **Connect to the database locally**:
   ```bash
   psql -h localhost -U <DB_USERNAME> -d <DB_NAME>
   ```

## Notes

- The bastion host is deployed in a public subnet with a public IP
- SSH access is restricted to specified CIDR blocks
- The instance is configured with Amazon Linux 2023
- PostgreSQL client is installed for database access
- CloudWatch alarms are configured for monitoring
- Consider implementing a schedule to start/stop the bastion host during non-business hours to reduce costs
