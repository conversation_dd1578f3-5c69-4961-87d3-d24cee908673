# GitHub Runner EC2 Instance
module "github_runner_ec2" {
  source = "../../modules/compute/ec2"

  name_prefix            = "wos-${var.environment}"
  vpc_id                 = module.vpc.vpc_id
  subnet_id              = module.vpc.private_app_subnet_ids[0]  # Place in private subnet
#   ami_id                 = data.aws_ami.amazon_linux_2023.id
  instance_type          = var.instance_type
  key_name               = var.bastion_key_name
  root_volume_size       = 20  
  allowed_ssh_cidr_blocks = [var.vpc_cidr]  # Only allow SSH from within VPC
  rds_security_group_id  = module.vpc.rds_security_group_id
  secret_arns            = var.secret_arns

  tags = merge(
    var.tags,
    {
      Environment = var.environment
      Purpose     = "GitHub Runner for DB Migrations"
    }
  )
}