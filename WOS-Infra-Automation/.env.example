# AWS Credentials - DO NOT COMMIT THIS FILE
# Copy this file to .env and fill in your values

# AWS Access Credentials (only needed for initial bootstrap)
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
# AWS_SESSION_TOKEN=your_session_token  # Uncomment if using temporary credentials

# AWS Configuration
AWS_REGION=ap-southeast-1
AWS_ACCOUNT_ID=your_account_id

# MFA Configuration (for assuming roles after bootstrap)
# This will be set up during the first run of assume-role.sh
# MFA_SERIAL=arn:aws:iam::your_account_id:mfa/your_username
