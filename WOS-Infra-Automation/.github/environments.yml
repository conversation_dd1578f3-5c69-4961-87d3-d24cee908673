# GitHub Environments Configuration

# Development Environment
# No approval required, automatically deployed
name: dev
on:
  deployment:
    environments: [dev]

# Staging Environment
# Requires approval from one reviewer
name: staging
on:
  deployment:
    environments: [staging]
environment_protection_rules:
  required_reviewers:
    - Rupeebw

# Production Environment
# Requires approval from two reviewers
name: prod
on:
  deployment:
    environments: [prod]
environment_protection_rules:
  required_reviewers:
    - Rupeebw
  wait_timer: 60 # 1 hour wait time before deployment

# Destroy Environments
# Always require approval
name: dev-destroy
on:
  deployment:
    environments: [dev-destroy]
environment_protection_rules:
  required_reviewers:
    - Rupeebw

name: staging-destroy
on:
  deployment:
    environments: [staging-destroy]
environment_protection_rules:
  required_reviewers:
    - Rupeebw

name: prod-destroy
on:
  deployment:
    environments: [prod-destroy]
environment_protection_rules:
  required_reviewers:
    - Rupeebw
  wait_timer: 60 # 1 hour wait time before destruction
