name: WasteOS Infrastructure Pipeline

on:
  push:
    branches:
      - main
      - 'feature/**'
    paths:
      - 'modules/**'
      - 'stacks/**'
      - 'environments/**'
      - '.github/workflows/terraform-infra-pipeline.yml'
  pull_request:
    branches:
      - main
    paths:
      - 'modules/**'
      - 'stacks/**'
      - 'environments/**'
      - '.github/workflows/terraform-infra-pipeline.yml'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy'
        required: true
        default: 'dev'
        type: choice
        options:
          - dev
          - staging
          - prod
      action:
        description: 'Action to perform'
        required: true
        default: 'plan'
        type: choice
        options:
          - plan
          - apply
          - destroy
      component:
        description: 'Infrastructure component to deploy (leave empty for all)'
        required: false
        default: 'all'
        type: choice
        options:
          - all
          - vpc
          - ecs
          - rds
          - alb
          - bastion
          - route53
          - monitoring

env:
  AWS_REGION: ap-southeast-1
  TF_VERSION: 1.5.7
  TFLINT_VERSION: v0.46.1
  AWS_ACCOUNT_ID: "************"

permissions:
  id-token: write
  contents: read
  pull-requests: write
  issues: write

jobs:
  terraform-validate:
    name: Terraform Validate
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v3

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v2
        with:
          terraform_version: ${{ env.TF_VERSION }}

      - name: Setup TFLint
        run: |
          curl -s https://raw.githubusercontent.com/terraform-linters/tflint/master/install_linux.sh | bash

      - name: Terraform Format Check
        id: fmt
        run: terraform fmt -check -recursive
        continue-on-error: true

      - name: Terraform Init
        run: |
          cd environments/dev
          terraform init -backend=false

      - name: Terraform Validate
        id: validate
        run: |
          cd environments/dev
          terraform validate

      - name: TFLint
        run: |
          cd environments/dev
          tflint --recursive

  terraform-plan:
    name: Terraform Plan
    needs: terraform-validate
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request' || github.event_name == 'push' || (github.event_name == 'workflow_dispatch' && github.event.inputs.action == 'plan')
    strategy:
      matrix:
        environment:
          - ${{ github.event_name == 'workflow_dispatch' && github.event.inputs.environment || 'dev' }}

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v3

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          role-to-assume: arn:aws:iam::${{ env.AWS_ACCOUNT_ID }}:role/github-actions-terraform-role
          aws-region: ${{ env.AWS_REGION }}

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v2
        with:
          terraform_version: ${{ env.TF_VERSION }}

      - name: Terraform Init
        id: init
        run: |
          cd environments/${{ matrix.environment }}
          terraform init

      - name: Set Terraform Target
        id: set-target
        run: |
          if [[ "${{ github.event.inputs.component }}" != "all" && "${{ github.event.inputs.component }}" != "" ]]; then
            echo "TF_TARGET=-target=module.${{ github.event.inputs.component }}" >> $GITHUB_ENV
          else
            echo "TF_TARGET=" >> $GITHUB_ENV
          fi

      - name: Terraform Plan
        id: plan
        run: |
          cd environments/${{ matrix.environment }}
          terraform plan ${{ env.TF_TARGET }} -out=tfplan -input=false
        continue-on-error: true

      - name: Upload Terraform Plan
        uses: actions/upload-artifact@v3
        with:
          name: terraform-plan-${{ matrix.environment }}
          path: environments/${{ matrix.environment }}/tfplan
          retention-days: 5

      - name: Terraform Plan Status
        if: steps.plan.outcome == 'failure'
        run: exit 1

      - name: Comment Plan on PR
        uses: actions/github-script@v6
        if: github.event_name == 'pull_request'
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const output = `#### Terraform Plan for ${{ matrix.environment }} 📝

            <details><summary>Show Plan</summary>

            \`\`\`terraform
            ${process.env.PLAN}
            \`\`\`

            </details>

            *Pushed by: @${{ github.actor }}, Action: \`${{ github.event_name }}\`*`;

            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: output
            })
        env:
          PLAN: ${{ steps.plan.outputs.stdout }}

  terraform-apply:
    name: Terraform Apply
    needs: [terraform-validate, terraform-plan]
    runs-on: ubuntu-latest
    if: (github.event_name == 'push' && github.ref == 'refs/heads/main') || (github.event_name == 'workflow_dispatch' && github.event.inputs.action == 'apply')
    strategy:
      matrix:
        environment:
          - ${{ github.event_name == 'workflow_dispatch' && github.event.inputs.environment || 'dev' }}

    environment:
      name: ${{ matrix.environment }}

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v3

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          role-to-assume: arn:aws:iam::${{ env.AWS_ACCOUNT_ID }}:role/github-actions-terraform-role
          aws-region: ${{ env.AWS_REGION }}

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v2
        with:
          terraform_version: ${{ env.TF_VERSION }}

      - name: Terraform Init
        id: init
        run: |
          cd environments/${{ matrix.environment }}
          terraform init

      - name: Set Terraform Target
        id: set-target
        run: |
          if [[ "${{ github.event.inputs.component }}" != "all" && "${{ github.event.inputs.component }}" != "" ]]; then
            echo "TF_TARGET=-target=module.${{ github.event.inputs.component }}" >> $GITHUB_ENV
          else
            echo "TF_TARGET=" >> $GITHUB_ENV
          fi

      - name: Download Terraform Plan
        uses: actions/download-artifact@v3
        with:
          name: terraform-plan-${{ matrix.environment }}
          path: environments/${{ matrix.environment }}

      - name: Terraform Apply
        id: apply
        run: |
          cd environments/${{ matrix.environment }}
          terraform apply -auto-approve tfplan

      - name: Record Deployment in DynamoDB
        run: |
          aws dynamodb put-item \
            --table-name terraform-deployment-history-wos \
            --item '{
              "Environment": {"S": "${{ matrix.environment }}"},
              "Timestamp": {"S": "'$(date -u +"%Y-%m-%dT%H:%M:%SZ")'"},
              "Component": {"S": "${{ github.event.inputs.component || 'all' }}"},
              "Actor": {"S": "${{ github.actor }}"},
              "CommitSHA": {"S": "${{ github.sha }}"},
              "Status": {"S": "success"}
            }'
        if: success()

  terraform-destroy:
    name: Terraform Destroy
    needs: terraform-validate
    runs-on: ubuntu-latest
    if: github.event_name == 'workflow_dispatch' && github.event.inputs.action == 'destroy'
    strategy:
      matrix:
        environment:
          - ${{ github.event.inputs.environment }}

    environment:
      name: ${{ matrix.environment }}-destroy

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v3

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          role-to-assume: arn:aws:iam::${{ env.AWS_ACCOUNT_ID }}:role/github-actions-terraform-role
          aws-region: ${{ env.AWS_REGION }}

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v2
        with:
          terraform_version: ${{ env.TF_VERSION }}

      - name: Terraform Init
        id: init
        run: |
          cd environments/${{ matrix.environment }}
          terraform init

      - name: Set Terraform Target
        id: set-target
        run: |
          if [[ "${{ github.event.inputs.component }}" != "all" && "${{ github.event.inputs.component }}" != "" ]]; then
            echo "TF_TARGET=-target=module.${{ github.event.inputs.component }}" >> $GITHUB_ENV
          else
            echo "TF_TARGET=" >> $GITHUB_ENV
          fi

      - name: Terraform Destroy
        id: destroy
        run: |
          cd environments/${{ matrix.environment }}
          terraform destroy ${{ env.TF_TARGET }} -auto-approve

      - name: Record Destruction in DynamoDB
        run: |
          aws dynamodb put-item \
            --table-name terraform-deployment-history-wos \
            --item '{
              "Environment": {"S": "${{ matrix.environment }}"},
              "Timestamp": {"S": "'$(date -u +"%Y-%m-%dT%H:%M:%SZ")'"},
              "Component": {"S": "${{ github.event.inputs.component || 'all' }}"},
              "Actor": {"S": "${{ github.actor }}"},
              "CommitSHA": {"S": "${{ github.sha }}"},
              "Status": {"S": "destroyed"}
            }'
        if: success()

  terraform-output:
    name: Terraform Output
    needs: terraform-validate
    runs-on: ubuntu-latest
    if: github.event_name == 'workflow_dispatch' && github.event.inputs.action == 'plan'
    strategy:
      matrix:
        environment:
          - ${{ github.event.inputs.environment }}

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v3

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          role-to-assume: arn:aws:iam::${{ env.AWS_ACCOUNT_ID }}:role/github-actions-terraform-role
          aws-region: ${{ env.AWS_REGION }}

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v2
        with:
          terraform_version: ${{ env.TF_VERSION }}

      - name: Terraform Init
        id: init
        run: |
          cd environments/${{ matrix.environment }}
          terraform init

      - name: Terraform Output
        id: output
        run: |
          cd environments/${{ matrix.environment }}
          terraform output -json > terraform_output.json

      - name: Upload Terraform Output
        uses: actions/upload-artifact@v3
        with:
          name: terraform-output-${{ matrix.environment }}
          path: environments/${{ matrix.environment }}/terraform_output.json
          retention-days: 5
