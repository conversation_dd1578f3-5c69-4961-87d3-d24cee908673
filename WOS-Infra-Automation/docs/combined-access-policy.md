# Combined AWS Access Policy

This document explains the combined access policy for AWS services in the WasteOS platform.

## Background

To simplify IAM policy management, we've combined multiple service-specific policies into a single comprehensive policy. This approach reduces the number of policies attached to users and makes it easier to manage permissions.

## Combined Access Policy

The combined policy (`policies/combined-access-policy.json`) includes permissions for:
- Amazon S3 (Simple Storage Service)
- Amazon ECR (Elastic Container Registry)
- Amazon RDS (Relational Database Service)
- Amazon EC2 (Elastic Compute Cloud)
- Amazon Bedrock

### Policy Structure

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "AllowS3Access",
            "Effect": "Allow",
            "Action": [
                "s3:ListAllMyBuckets",
                "s3:GetBucketLocation",
                "s3:ListBucket",
                "s3:GetObject",
                "s3:PutObject",
                "s3:GetObjectVersion",
                "s3:DeleteObject",
                "s3:GetObjectTagging",
                "s3:PutObjectTagging"
            ],
            "Resource": [
                "arn:aws:s3:::*",
                "arn:aws:s3:::*/*"
            ]
        },
        {
            "Sid": "AllowECRAccess",
            "Effect": "Allow",
            "Action": [
                "ecr:GetAuthorizationToken",
                "ecr:BatchCheckLayerAvailability",
                "ecr:GetDownloadUrlForLayer",
                "ecr:BatchGetImage",
                "ecr:DescribeRepositories",
                "ecr:ListImages",
                "ecr:DescribeImages",
                "ecr:InitiateLayerUpload",
                "ecr:UploadLayerPart",
                "ecr:CompleteLayerUpload",
                "ecr:PutImage",
                "ecr:CreateRepository",
                "ecr:TagResource"
            ],
            "Resource": "*"
        },
        {
            "Sid": "AllowRDSDescribeAccess",
            "Effect": "Allow",
            "Action": [
                "rds:DescribeDBInstances",
                "rds:DescribeDBClusters",
                "rds:DescribeGlobalClusters",
                "rds:DescribeDBParameterGroups",
                "rds:DescribeDBParameters",
                "rds:DescribeDBSubnetGroups",
                "rds:DescribeOptionGroups"
            ],
            "Resource": "*"
        },
        {
            "Sid": "AllowEC2DescribeAccess",
            "Effect": "Allow",
            "Action": [
                "ec2:DescribeInstances",
                "ec2:DescribeInstanceStatus",
                "ec2:DescribeInstanceTypes",
                "ec2:DescribeTags",
                "ec2:DescribeVolumes",
                "ec2:DescribeSecurityGroups",
                "ec2:DescribeNetworkInterfaces",
                "ec2:DescribeSubnets",
                "ec2:DescribeVpcs"
            ],
            "Resource": "*"
        },
        {
            "Sid": "AllowBedrockAccess",
            "Effect": "Allow",
            "Action": [
                "bedrock:ListFoundationModels",
                "bedrock:GetFoundationModel",
                "bedrock:GetFoundationModelAvailability",
                "bedrock:InvokeModel",
                "bedrock:InvokeModelWithResponseStream",
                "bedrock:ListCustomModels",
                "bedrock:GetCustomModel",
                "bedrock:ListModelCustomizationJobs",
                "bedrock:GetModelCustomizationJob",
                "bedrock:ListPrompts",
                "bedrock:GetPrompt",
                "bedrock:ListGuardrails",
                "bedrock:GetGuardrail",
                "bedrock:ApplyGuardrail",
                "bedrock:ListAgents",
                "bedrock:GetAgent",
                "bedrock:ListAgentAliases",
                "bedrock:GetAgentAlias",
                "bedrock:ListFlows",
                "bedrock:GetFlow",
                "bedrock:ListKnowledgeBases",
                "bedrock:GetKnowledgeBase"
            ],
            "Resource": "*"
        }
    ]
}
```

## Applying the Combined Policy

We've created a script to apply the combined policy to users:

```bash
# Make the script executable
chmod +x scripts/apply-combined-policy.sh

# Run the script
./scripts/apply-combined-policy.sh
```

The script will:
1. Create the combined policy if it doesn't exist
2. Update the policy if it already exists
3. Attach the policy to the specified user
4. Detach any old individual policies that might be attached to the user

## Service-Specific Permissions

### S3 Permissions

The combined policy includes permissions to:
- List all S3 buckets
- Get bucket location and list bucket contents
- Get, put, and delete objects
- Get and put object tags
- Access object versions

These permissions allow users to fully interact with S3 buckets and objects.

### ECR Permissions

The combined policy includes permissions to:
- Get authorization tokens for Docker client authentication
- Check layer availability and download layers
- Describe repositories and list images
- Upload layers and complete image uploads
- Push images to repositories
- Create new repositories
- Tag resources

These permissions allow users to pull, build, and push Docker images to ECR repositories.

### RDS Permissions

The combined policy includes permissions to:
- List and describe RDS instances
- List and describe RDS clusters
- List and describe RDS parameter groups and subnet groups

These permissions allow users to view RDS resources but not modify them.

### EC2 Permissions

The combined policy includes permissions to:
- Describe EC2 instances and their status
- Describe instance types
- Describe volumes, security groups, and network interfaces
- Describe VPC resources like subnets and VPCs

These permissions allow users to view EC2 resources but not modify them.

### Amazon Bedrock Permissions

The combined policy includes permissions to:
- List and get foundation models
- Invoke models
- Work with custom models
- Manage prompts, guardrails, agents, and knowledge bases
- Use flows and other Bedrock features

These permissions allow users to use Amazon Bedrock services.

## Policy Precedence in AWS IAM

AWS IAM follows these rules for policy evaluation:
1. By default, all requests are implicitly denied
2. An explicit allow overrides this default
3. An explicit deny overrides any allows

This means that if there's an explicit deny in any policy that applies to a user, it will take precedence over any allow statements, unless the deny policy has a condition that doesn't apply in the specific case.

## Service Control Policies (SCPs)

If your AWS account is part of an AWS Organization, Service Control Policies might be in effect. SCPs are a type of organization policy that you can use to manage permissions across your organization.

SCPs offer central control over the maximum available permissions for all accounts in your organization. They help ensure your accounts stay within your organization's access control guidelines.

If an SCP is denying access to operations, you'll need to work with your AWS Organization administrators to modify the SCP or request an exception.

## MFA Policy Interaction

This account has an MFA policy (`policies/MFA.json`) that requires users to authenticate with multi-factor authentication before accessing most AWS services. The policy includes a statement with Sid "DenyAllExceptListedIfNoMFA" that denies all actions except a few IAM-related ones if MFA is not present.

When using the combined access policy, users must:
1. Set up MFA for their IAM user
2. Authenticate with MFA when signing in to the AWS console or using the AWS CLI
3. Use temporary credentials obtained with MFA when making API calls

Without MFA authentication, users will be denied access to the services included in the combined policy, regardless of the permissions granted.

## Troubleshooting

If you encounter permission issues even after applying the combined policy, check for:

1. **MFA Authentication**: Ensure the user has set up MFA and is authenticated with it. The MFA policy will deny access to all services if MFA is not present.

2. **Missing Permissions**: If you encounter an "Access Denied" error for a specific action, check if that action is included in the combined policy. You may need to add additional permissions as services evolve.

3. **Explicit Deny Policies**: Look for other policies attached to the user or their groups that might have explicit deny statements.

4. **Service Control Policies**: Check if there are SCPs at the organization level that restrict access.

5. **Resource-Based Policies**: Some resources might have their own policies that restrict access.

6. **Permission Boundaries**: Check if there are permission boundaries applied to the user that might restrict access.

7. **Session Policies**: If the user is assuming a role, check if there are session policies that restrict access.
