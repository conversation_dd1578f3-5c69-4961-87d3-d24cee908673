# RDS Access Policies

This document explains how to manage RDS access policies for the WasteOS platform.

## Background

AWS RDS (Relational Database Service) requires specific IAM permissions to perform operations such as:
- Describing DB instances
- Describing DB clusters
- Describing global clusters
- Modifying DB instances
- Creating and deleting DB instances
- And more

By default, IAM users do not have permissions to perform these operations unless explicitly granted. Additionally, Service Control Policies (SCPs) at the organization level may restrict certain operations regardless of IAM policies.

## Common Issues

### Explicit Deny in Identity-Based Policies

If you encounter an error message like:

```
User: arn:aws:iam::028864969427:user/<EMAIL> is not authorized to perform: rds:DescribeDBInstances on resource: arn:aws:rds:ap-southeast-1:028864969427:db:* with an explicit deny in an identity-based policy
```

This indicates that there is an IAM policy directly attached to the user or to a group the user belongs to that is explicitly denying these RDS operations.

### Resolution Steps

1. Identify the policy with the explicit deny:
   ```bash
   aws iam list-user-policies --user-name <EMAIL>
   aws iam list-attached-user-policies --user-name <EMAIL>
   ```

2. For each policy, get the policy document:
   ```bash
   aws iam get-policy-version --policy-arn POLICY_ARN --version-id VERSION_ID
   ```

3. Look for statements with `"Effect": "Deny"` and actions related to RDS.

4. If the deny is necessary for security reasons, create a more specific allow policy that doesn't conflict with the security requirements.

5. If the deny is not necessary, modify the policy to remove the deny statement or add an exception for specific resources.

## Applying RDS Access Policies

We've created a script to apply RDS access policies to users:

```bash
# Make the script executable
chmod +x scripts/apply-rds-access-policy.sh

# Run the script
./scripts/apply-rds-access-policy.sh
```

The script will:
1. Create a policy that allows RDS describe operations
2. Attach the policy to the specified user

## Policy Precedence in AWS IAM

AWS IAM follows these rules for policy evaluation:
1. By default, all requests are implicitly denied
2. An explicit allow overrides this default
3. An explicit deny overrides any allows

This means that if there's an explicit deny in any policy that applies to a user, it will take precedence over any allow statements, unless the deny policy has a condition that doesn't apply in the specific case.

## Service Control Policies (SCPs)

If your AWS account is part of an AWS Organization, Service Control Policies might be in effect. SCPs are a type of organization policy that you can use to manage permissions across your organization.

SCPs offer central control over the maximum available permissions for all accounts in your organization. They help ensure your accounts stay within your organization's access control guidelines.

If an SCP is denying access to RDS operations, you'll need to work with your AWS Organization administrators to modify the SCP or request an exception.

## Recommended RDS Access Policies

For developers and operators who need to work with RDS instances, we recommend the following permission sets:

1. **Read-Only Access**: For developers who need to view but not modify RDS resources
2. **Developer Access**: For developers who need to connect to databases but not modify the RDS infrastructure
3. **Operator Access**: For operators who need to manage RDS instances, including creation and modification
4. **Admin Access**: Full access to RDS resources (should be limited to a small number of administrators)

Each of these permission sets should be implemented as separate IAM policies and attached to appropriate IAM groups or roles.
