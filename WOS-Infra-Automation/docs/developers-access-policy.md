# Developers Access Policy

This document explains the access policy for developers in the WasteOS platform.

## Background

Developers need access to various AWS services to perform their development tasks, but they don't need access to all services. The Developers policy grants the necessary permissions for development work while excluding access to certain services like Amazon Bedrock.

## Policy Overview

The Developers policy (`policies/Developers-Updated.json`) includes permissions for:

- AWS Console access (Support only)
- S3 (object storage)
- CloudWatch (logs and monitoring)
- IAM (read-only access)
- MFA management (for the user's own MFA device)
- EC2, RDS, and other services (read-only access)
- ECR (container registry)
- ECS (full access)

## Key Permissions

### S3 Access

The policy includes permissions to:
- List all S3 buckets
- Get bucket location and list bucket contents
- Get, put, and delete objects
- Get and put object tags
- Access object versions

### ECR Access

The policy includes permissions to:
- Get authorization tokens for Docker client authentication
- Check layer availability and download layers
- Describe repositories and list images
- Upload layers and complete image uploads
- Push images to repositories
- Create new repositories
- Tag resources

### ECS Full Access

The policy includes full permissions for Amazon ECS (Elastic Container Service):
- Create, read, update, and delete ECS clusters, services, and tasks
- Work with Auto Scaling for ECS services
- Manage CloudWatch alarms for ECS
- Create and manage IAM roles for ECS tasks
- Pass roles to ECS services and tasks

### Developer Services Read Access

The policy includes read-only permissions for:
- EC2 (virtual servers)
- Elastic Load Balancing
- Auto Scaling
- RDS (relational databases)
- DynamoDB (NoSQL database)
- Lambda (serverless functions)
- API Gateway
- SNS (notification service)
- SQS (queue service)
- CloudFormation (infrastructure as code)
- CloudFront (CDN)
- Route 53 (DNS)

## Excluded Permissions

The policy explicitly excludes:
- Amazon Bedrock permissions
- AWS Billing and Account portal access
- Cost Explorer access
- CodeCommit access
- CodeBuild access
- CodePipeline access
- Administrative actions for most services (except ECS)
- Access to sensitive services like IAM (beyond read-only and ECS-related permissions)
- Direct access to production resources (unless specifically granted)

## Applying the Policy

We've created a script to apply the Developers policy to users:

```bash
# Make the script executable
chmod +x scripts/apply-developers-policy.sh

# Run the script for a specific user
./scripts/apply-developers-policy.sh <EMAIL>
```

The script will:
1. Create the Developers policy if it doesn't exist
2. Update the policy if it already exists
3. Attach the policy to the specified user
4. Detach any Bedrock-specific policies that might be attached to the user

## MFA Policy Interaction

This account has an MFA policy (`policies/MFA.json`) that requires users to authenticate with multi-factor authentication before accessing most AWS services. The policy includes a statement with Sid "DenyAllExceptListedIfNoMFA" that denies all actions except a few IAM-related ones if MFA is not present.

When using the Developers policy, users must:
1. Set up MFA for their IAM user
2. Authenticate with MFA when signing in to the AWS console or using the AWS CLI
3. Use temporary credentials obtained with MFA when making API calls

Without MFA authentication, users will be denied access to the services included in the Developers policy, regardless of the permissions granted.

## Troubleshooting

If you encounter permission issues even after applying the Developers policy, check for:

1. **MFA Authentication**: Ensure the user has set up MFA and is authenticated with it. The MFA policy will deny access to all services if MFA is not present.

2. **Missing Permissions**: If you encounter an "Access Denied" error for a specific action, check if that action is included in the Developers policy. You may need to add additional permissions as services evolve.

3. **Explicit Deny Policies**: Look for other policies attached to the user or their groups that might have explicit deny statements.

4. **Service Control Policies**: Check if there are SCPs at the organization level that restrict access.

5. **Resource-Based Policies**: Some resources might have their own policies that restrict access.

## Requesting Additional Permissions

If a developer needs access to additional services or actions not covered by the Developers policy, they should:

1. Identify the specific service and actions needed
2. Provide a business justification for the access
3. Request the access from the platform team

The platform team will review the request and, if approved, either:
- Update the Developers policy to include the additional permissions
- Create a custom policy for the specific user or use case
