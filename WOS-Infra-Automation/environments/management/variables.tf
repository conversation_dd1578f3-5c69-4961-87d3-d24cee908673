variable "region" {
  description = "The AWS region to deploy resources in"
  type        = string
  default     = "ap-southeast-1"
}

variable "aws_account_id" {
  description = "The AWS account ID"
  type        = string
}

variable "user_name" {
  description = "The IAM user name to grant access to the bucket"
  type        = string
  default     = "<EMAIL>"
}

variable "cicd_role_name" {
  description = "The IAM role name for CI/CD to grant access to the bucket"
  type        = string
  default     = "cicd-role"
}

variable "terraform_lock_table_name" {
  description = "Name of the DynamoDB table for Terraform state locking"
  type        = string
  default     = "terraform-statefile-locks-wos"
}

variable "deployment_history_table_name" {
  description = "Name of the DynamoDB table for deployment history"
  type        = string
  default     = "wos-deployment-history"
}

variable "tags" {
  description = "A map of tags to add to all resources"
  type        = map(string)
  default = {
    Project     = "WasteOS"
    Environment = "Management"
    ManagedBy   = "Terraform"
    Owner       = "Platform Team"
    CostCenter  = "Engineering"
  }
}
