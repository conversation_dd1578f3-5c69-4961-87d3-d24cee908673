# WasteOS Management Environment

This directory contains the Terraform configuration for the WasteOS management environment, which includes the infrastructure required for Terraform state management.

## Resources Created

- S3 bucket for Terraform state storage
- DynamoDB table for Terraform state locking
- DynamoDB table for deployment history tracking

## Prerequisites

- AWS CLI configured with appropriate credentials
- Terraform 1.0.0 or later
- Appropriate AWS permissions to create resources

## Usage

### Initial Deployment

For the initial deployment, you need to use local state since the remote state infrastructure doesn't exist yet:

```bash
# Navigate to the management environment directory
cd environments/management

# Initialize Terraform
terraform init

# Validate the configuration
terraform validate

# Plan the deployment
terraform plan -out=tfplan

# Apply the changes
terraform apply tfplan
```

### Migrating to Remote State

After the initial deployment, you can migrate to remote state:

1. Uncomment the backend configuration in `main.tf`
2. Run the following commands:

```bash
# Reinitialize Terraform with the new backend configuration
terraform init -migrate-state

# Confirm the migration when prompted
```

### Assuming the CI/CD Role

After setting up the remote state, you should use the CI/CD role for all subsequent operations:

1. Uncomment the assume_role configuration in the AWS provider block in `main.tf`
2. Use the assume-role.sh script to get temporary credentials:

```bash
source ../../bootstrap/assume-role.sh
```

## Variables

| Name                          | Description                                            | Default                       |
| ----------------------------- | ------------------------------------------------------ | ----------------------------- |
| region                        | The AWS region to deploy resources in                  | ap-southeast-1                |
| aws_account_id                | The AWS account ID                                     | (required)                    |
| user_name                     | The IAM user name to grant access to the bucket        | <EMAIL>       |
| cicd_role_name                | The IAM role name for CI/CD                            | cicd-role                     |
| terraform_lock_table_name     | Name of the DynamoDB table for Terraform state locking | terraform-statefile-locks-wos |
| deployment_history_table_name | Name of the DynamoDB table for deployment history      | wos-deployment-history        |
| tags                          | A map of tags to add to all resources                  | (see terraform.tfvars)        |

## Outputs

| Name                         | Description                              |
| ---------------------------- | ---------------------------------------- |
| terraform_state_bucket_id    | The name of the Terraform state bucket   |
| terraform_state_bucket_arn   | The ARN of the Terraform state bucket    |
| terraform_lock_table_id      | The name of the Terraform lock table     |
| terraform_lock_table_arn     | The ARN of the Terraform lock table      |
| deployment_history_table_id  | The name of the deployment history table |
| deployment_history_table_arn | The ARN of the deployment history table  |

## Notes

- The S3 bucket and DynamoDB tables have lifecycle rules to prevent accidental destruction
- The S3 bucket has versioning enabled to protect against accidental deletion of state files
- The S3 bucket policy grants specific permissions to the specified IAM user and CI/CD role
- All resources are tagged according to the WasteOS tagging standard

unset AWS_SESSION_TOKEN && echo "AWS_SESSION_TOKEN has been unset"
