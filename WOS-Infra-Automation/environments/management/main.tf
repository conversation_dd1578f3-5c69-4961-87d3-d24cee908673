/**
 * # WasteOS Management Environment
 *
 * This configuration creates the infrastructure for the management environment,
 * including S3 bucket for Terraform state and DynamoDB tables for state locking
 * and deployment history.
 */

terraform {
  required_version = ">= 1.0.0"

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 4.0"
    }
  }

  # Backend configuration for remote state
  backend "s3" {
    bucket         = "wos-terraform-state-bucket-************"
    key            = "wos/management/terraform.tfstate"
    region         = "ap-southeast-1"
    dynamodb_table = "terraform-statefile-locks-wos"
    encrypt        = true
  }
}

provider "aws" {
  region = var.region

  # Assume role configuration (uncomment after initial deployment)
  # assume_role {
  #   role_arn = "arn:aws:iam::${var.aws_account_id}:role/cicd-role"
  # }
}

# Create S3 bucket for Terraform state
module "terraform_state_storage" {
  source = "../../stacks/storage/s3"

  aws_account_id = var.aws_account_id
  user_name      = var.user_name
  cicd_role_name = var.cicd_role_name

  tags = var.tags
}

# Create DynamoDB tables for state locking and deployment history
module "dynamodb_tables" {
  source = "../../stacks/storage/dynamodb"

  terraform_lock_table_name     = var.terraform_lock_table_name
  deployment_history_table_name = var.deployment_history_table_name

  tags = var.tags
}
