output "terraform_state_bucket_id" {
  description = "The name of the Terraform state bucket"
  value       = module.terraform_state_storage.bucket_id
}

output "terraform_state_bucket_arn" {
  description = "The ARN of the Terraform state bucket"
  value       = module.terraform_state_storage.bucket_arn
}

output "terraform_lock_table_id" {
  description = "The name of the Terraform lock table"
  value       = module.dynamodb_tables.terraform_lock_table_id
}

output "terraform_lock_table_arn" {
  description = "The ARN of the Terraform lock table"
  value       = module.dynamodb_tables.terraform_lock_table_arn
}

output "deployment_history_table_id" {
  description = "The name of the deployment history table"
  value       = module.dynamodb_tables.deployment_history_table_id
}

output "deployment_history_table_arn" {
  description = "The ARN of the deployment history table"
  value       = module.dynamodb_tables.deployment_history_table_arn
}
