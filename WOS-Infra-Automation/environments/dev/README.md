# WasteOS Dev Environment

This directory contains the Terraform configuration for the WasteOS development environment.

## Prerequisites

- AWS CLI configured with appropriate credentials
- Terraform 1.0.0 or later
- S3 bucket and DynamoDB table for Terraform state

## Usage

1. Copy the example tfvars file and update it with your values:

```bash
cp terraform.tfvars.example terraform.tfvars
```

2. Edit `terraform.tfvars` with your specific values.

3. Initialize Terraform:

```bash
terraform init
```

4. Plan the deployment:

```bash
terraform plan -out=plan.out
```

5. Apply the changes:

```bash
terraform apply plan.out
```

## Resources Created

- EC2 IAM role and instance profile for SSM access
- VPC Flow Logs IAM role
- Associated IAM policies

## Notes

- The CI/CD role is typically created outside of Terraform
- Make sure to update the AWS account ID in terraform.tfvars
- Ensure that the S3 bucket and DynamoDB table for Terraform state exist
