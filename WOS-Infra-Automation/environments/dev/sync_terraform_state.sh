#!/bin/bash
# Terraform State Synchronization Script
# This script helps align Terraform state with current AWS infrastructure
# It performs the following actions:
# 1. Import untracked resources into Terraform state
# 2. Update health check configurations for target groups
# 3. Adjust listener rule priorities to avoid conflicts
# 4. Verify Route53 DNS records
# 5. Check security group rules

set -e

# Set AWS region
export AWS_REGION=ap-southeast-1

echo "===== WasteOS Dev Environment Terraform State Synchronization ====="
echo "Starting synchronization process..."

# 1. Import ALB resources if not already tracked
echo "Checking and importing ALB resources..."

# Get ALB ARN
ALB_ARN=$(aws elbv2 describe-load-balancers --names wos-dev-alb --query 'LoadBalancers[0].LoadBalancerArn' --output text)
if [ -z "$ALB_ARN" ]; then
  echo "ERROR: Could not find ALB with name wos-dev-alb"
  exit 1
fi
echo "Found ALB: $ALB_ARN"

# Import ALB if not already in Terraform state
terraform state list module.alb.aws_lb.this || {
  echo "Importing ALB into Terraform state..."
  terraform import module.alb.aws_lb.this $ALB_ARN
}

# Import HTTP listener if not tracked
HTTP_LISTENER_ARN=$(aws elbv2 describe-listeners --load-balancer-arn $ALB_ARN --query "Listeners[?Port==\`80\`].ListenerArn" --output text)
if [ -n "$HTTP_LISTENER_ARN" ]; then
  terraform state list module.alb.aws_lb_listener.http || {
    echo "Importing HTTP listener into Terraform state..."
    terraform import module.alb.aws_lb_listener.http $HTTP_LISTENER_ARN
  }
fi

# Import HTTPS listener if not tracked
HTTPS_LISTENER_ARN=$(aws elbv2 describe-listeners --load-balancer-arn $ALB_ARN --query "Listeners[?Port==\`443\`].ListenerArn" --output text)
if [ -n "$HTTPS_LISTENER_ARN" ]; then
  terraform state list module.alb.aws_lb_listener.https || {
    echo "Importing HTTPS listener into Terraform state..."
    terraform import module.alb.aws_lb_listener.https $HTTPS_LISTENER_ARN
  }
fi

# 2. Import and update target groups for all services
echo "Checking and importing target groups..."

# Services and their expected health check paths
declare -A services=(
  ["dashboard"]="/login"
  ["chatbot"]="/health"
  ["api"]="/api/health"
  ["wos"]="/health"
)

for service in "${!services[@]}"; do
  # Get target group ARN
  TG_ARN=$(aws elbv2 describe-target-groups --names wos-dev-${service}-tg --query 'TargetGroups[0].TargetGroupArn' --output text 2>/dev/null || echo "")
  
  if [ -n "$TG_ARN" ]; then
    echo "Found target group for $service: $TG_ARN"
    
    # Import target group if not tracked
    terraform state list module.alb-${service}.aws_lb_target_group.this || {
      echo "Importing $service target group into Terraform state..."
      terraform import module.alb-${service}.aws_lb_target_group.this $TG_ARN
    }
    
    # Check health check configuration
    CURRENT_PATH=$(aws elbv2 describe-target-groups --target-group-arn $TG_ARN --query 'TargetGroups[0].HealthCheckPath' --output text)
    EXPECTED_PATH=${services[$service]}
    
    if [ "$CURRENT_PATH" != "$EXPECTED_PATH" ]; then
      echo "Health check path for $service needs updating from $CURRENT_PATH to $EXPECTED_PATH"
      echo "Create or update the following in your Terraform configuration:"
      echo "module \"alb-${service}\" {"
      echo "  # other config..."
      echo "  health_check = {"
      echo "    path                = \"$EXPECTED_PATH\""
      echo "    interval            = 30"
      echo "    timeout             = 5"
      echo "    healthy_threshold   = 3"
      echo "    unhealthy_threshold = 3"
      echo "    matcher             = \"200-399\""
      echo "  }"
      echo "}"
    fi
  else
    echo "Target group for $service not found. May need to be created."
  fi
done

# 3. Import and update listener rules
echo "Checking and importing listener rules..."

if [ -n "$HTTPS_LISTENER_ARN" ]; then
  # Get existing listener rules
  RULES=$(aws elbv2 describe-rules --listener-arn $HTTPS_LISTENER_ARN --query 'Rules[?IsDefault==`false`].[RuleArn,Priority]' --output text)
  
  echo "Found the following listener rules:"
  echo "$RULES"
  
  # Import listener rules and suggest priority adjustments
  PRIORITY_MAP=()
  while read -r RULE_ARN PRIORITY; do
    if [ -n "$RULE_ARN" ]; then
      # Check conditions to identify the service
      CONDITIONS=$(aws elbv2 describe-rules --rule-arns $RULE_ARN --query 'Rules[0].Conditions' --output json)
      
      if [[ $CONDITIONS == *"dashboard.dev.wasteos.net"* ]]; then
        SERVICE="dashboard"
        EXPECTED_PRIORITY=10
      elif [[ $CONDITIONS == *"chatbot.dev.wasteos.net"* ]]; then
        SERVICE="chatbot"
        EXPECTED_PRIORITY=20
      elif [[ $CONDITIONS == *"api.dev.wasteos.net"* ]]; then
        SERVICE="api"
        EXPECTED_PRIORITY=50
      elif [[ $CONDITIONS == *"wos.dev.wasteos.net"* ]]; then
        SERVICE="wos"
        EXPECTED_PRIORITY=60
      else
        SERVICE="unknown"
        EXPECTED_PRIORITY=$PRIORITY
      fi
      
      echo "Rule for $SERVICE (current priority: $PRIORITY, expected: $EXPECTED_PRIORITY)"
      
      # Import rule if not tracked
      terraform state list module.alb-${SERVICE}.aws_lb_listener_rule.this || {
        echo "Importing $SERVICE listener rule into Terraform state..."
        terraform import module.alb-${SERVICE}.aws_lb_listener_rule.this $RULE_ARN
      }
      
      # Store in priority map for conflict detection
      PRIORITY_MAP+=("$SERVICE:$PRIORITY:$EXPECTED_PRIORITY")
    fi
  done <<< "$RULES"
  
  # Check for priority conflicts
  echo "Checking for priority conflicts..."
  CURRENT_PRIORITIES=$(echo "${PRIORITY_MAP[@]}" | tr ' ' '\n' | cut -d':' -f2 | sort)
  DUPLICATE_PRIORITIES=$(echo "$CURRENT_PRIORITIES" | uniq -d)
  
  if [ -n "$DUPLICATE_PRIORITIES" ]; then
    echo "WARN: Found duplicate priorities: $DUPLICATE_PRIORITIES"
    echo "Priority conflict resolution suggestions:"
    
    for entry in "${PRIORITY_MAP[@]}"; do
      IFS=':' read -r SERVICE CURRENT EXPECTED <<< "$entry"
      for dup in $DUPLICATE_PRIORITIES; do
        if [ "$CURRENT" == "$dup" ]; then
          echo "  - Update $SERVICE rule priority from $CURRENT to $EXPECTED"
        fi
      done
    done
  fi
fi

# 4. Verify Route53 DNS records
echo "Checking Route53 DNS records..."

ZONE_ID="Z0958744982TPSOX8RJM"
RECORDS=("dashboard.dev.wasteos.net" "chatbot.dev.wasteos.net" "api.dev.wasteos.net" "wos.dev.wasteos.net")
ALB_DNS=$(aws elbv2 describe-load-balancers --names wos-dev-alb --query 'LoadBalancers[0].DNSName' --output text)

for record in "${RECORDS[@]}"; do
  # Check if record exists
  aws route53 list-resource-record-sets --hosted-zone-id $ZONE_ID --query "ResourceRecordSets[?Name=='$record.']" --output text > /dev/null 2>&1
  if [ $? -eq 0 ]; then
    echo "Record $record exists in Route53"
    
    # Import if not tracked
    terraform state list aws_route53_record.$record || {
      echo "Record $record is not in Terraform state. Create or import this record:"
      echo "resource \"aws_route53_record\" \"$record\" {"
      echo "  zone_id = \"$ZONE_ID\""
      echo "  name    = \"$record\""
      echo "  type    = \"A\""
      echo "  alias {"
      echo "    name                   = \"$ALB_DNS\""
      echo "    zone_id                = \"<ALB Zone ID>\""
      echo "    evaluate_target_health = true"
      echo "  }"
      echo "}"
    }
  else
    echo "WARNING: Record $record does not exist in Route53"
    echo "Create the following record in your Terraform configuration:"
    echo "resource \"aws_route53_record\" \"$record\" {"
    echo "  zone_id = \"$ZONE_ID\""
    echo "  name    = \"$record\""
    echo "  type    = \"A\""
    echo "  alias {"
    echo "    name                   = \"$ALB_DNS\""
    echo "    zone_id                = \"<ALB Zone ID>\""
    echo "    evaluate_target_health = true"
    echo "  }"
    echo "}"
  fi
done

# 5. Verify security group rules
echo "Checking security group rules..."

# Get ALB security group
ALB_SG=$(aws elbv2 describe-load-balancers --names wos-dev-alb --query 'LoadBalancers[0].SecurityGroups[0]' --output text)

if [ -n "$ALB_SG" ]; then
  echo "Found ALB security group: $ALB_SG"
  
  # Check for HTTPS (443) ingress rule
  HTTPS_RULE=$(aws ec2 describe-security-groups --group-ids $ALB_SG --query "SecurityGroups[0].IpPermissions[?ToPort==\`443\`]" --output text)
  
  if [ -z "$HTTPS_RULE" ]; then
    echo "WARNING: No HTTPS (port 443) ingress rule found for ALB security group"
    echo "Add the following to your security group configuration:"
    echo "resource \"aws_security_group_rule\" \"alb_https_ingress\" {"
    echo "  type              = \"ingress\""
    echo "  from_port         = 443"
    echo "  to_port           = 443"
    echo "  protocol          = \"tcp\""
    echo "  cidr_blocks       = [\"0.0.0.0/0\"]"
    echo "  security_group_id = \"$ALB_SG\""
    echo "  description       = \"Allow HTTPS traffic from Internet\""
    echo "}"
  else
    echo "HTTPS ingress rule exists for ALB security group"
  fi
  
  # Check egress rules for target services
  for service in "${!services[@]}"; do
    # Get target group
    TG_ARN=$(aws elbv2 describe-target-groups --names wos-dev-${service}-tg --query 'TargetGroups[0].TargetGroupArn' --output text 2>/dev/null || echo "")
    
    if [ -n "$TG_ARN" ]; then
      # Get target port
      TARGET_PORT=$(aws elbv2 describe-target-groups --target-group-arn $TG_ARN --query 'TargetGroups[0].Port' --output text)
      
      if [ -n "$TARGET_PORT" ]; then
        echo "Service $service uses port $TARGET_PORT"
        
        # Check if there's an egress rule for this port
        SG_RULE=$(aws ec2 describe-security-groups --group-ids $ALB_SG --query "SecurityGroups[0].IpPermissionsEgress[?ToPort==\`$TARGET_PORT\`]" --output text)
        
        if [ -z "$SG_RULE" ]; then
          echo "WARNING: No egress rule found for $service (port $TARGET_PORT)"
          echo "Add the following to your security group configuration:"
          echo "resource \"aws_security_group_rule\" \"alb_${service}_egress\" {"
          echo "  type              = \"egress\""
          echo "  from_port         = $TARGET_PORT"
          echo "  to_port           = $TARGET_PORT"
          echo "  protocol          = \"tcp\""
          echo "  security_group_id = \"$ALB_SG\""
          echo "  # Use one of the following approaches:"
          echo "  # 1. Target service security group:"
          echo "  # source_security_group_id = \"<TARGET_SERVICE_SG>\""
          echo "  # 2. Or, all VPC CIDR:"
          echo "  # cidr_blocks       = [\"<VPC_CIDR>\"]"
          echo "  description       = \"Allow traffic to $service service\""
          echo "}"
        else
          echo "Egress rule exists for $service (port $TARGET_PORT)"
        fi
      fi
    fi
  done
else
  echo "WARNING: Could not find security group for ALB"
fi

# Additional checks for certificate
echo "Checking SSL certificate..."

# List certificates and find one for *.dev.wasteos.net
CERT_ARN=$(aws acm list-certificates --query "CertificateSummaryList[?DomainName=='*.dev.wasteos.net'].CertificateArn" --output text)

if [ -n "$CERT_ARN" ]; then
  echo "Found certificate for *.dev.wasteos.net: $CERT_ARN"
  
  # Check if certificate is attached to HTTPS listener
  if [ -n "$HTTPS_LISTENER_ARN" ]; then
    LISTENER_CERT=$(aws elbv2 describe-listeners --listener-arns $HTTPS_LISTENER_ARN --query 'Listeners[0].Certificates[0].CertificateArn' --output text)
    
    if [ "$LISTENER_CERT" == "$CERT_ARN" ]; then
      echo "Certificate is correctly attached to HTTPS listener"
    else
      echo "WARNING: HTTPS listener uses a different certificate"
      echo "Update your ALB module configuration to use the correct certificate:"
      echo "module \"alb\" {"
      echo "  # other config..."
      echo "  https_listeners = ["
      echo "    {"
      echo "      port               = 443"

