# Dev Environment - variables.tf
# Variables for the dev environment


variable "region" {
  description = "AWS region"
  type        = string
  default     = "ap-southeast-1"
}

variable "environment" {
  description = "Environment name"
  type        = string
  default     = "dev"
}

variable "aws_account_id" {
  description = "AWS account ID"
  type        = string
}

variable "tags" {
  description = "Tags to apply to all resources"
  type        = map(string)
}

variable "secret_arns" {
  description = "ARNs of secrets to grant access to"
  type        = list(string)
  default     = ["*"]
}

variable "s3_bucket_arns" {
  description = "ARNs of S3 buckets to grant access to"
  type        = list(string)
  default     = ["*"]
}

# VPC Variables
variable "vpc_cidr" {
  description = "CIDR block for the VPC"
  type        = string
  default     = "*********/20"
}

variable "azs" {
  description = "Availability zones to use"
  type        = list(string)
  default     = ["ap-southeast-1a", "ap-southeast-1b"]
}

variable "az_suffixes" {
  description = "Suffixes to use for AZ-specific resources"
  type        = list(string)
  default     = ["1a", "1b"]
}

variable "public_subnets" {
  description = "CIDR blocks for public subnets"
  type        = list(string)
  default     = ["*********/24", "*********/24"]
}

variable "private_app_subnets" {
  description = "CIDR blocks for private application subnets"
  type        = list(string)
  default     = ["*********/24", "*********/24"]
}

variable "private_db_subnets" {
  description = "CIDR blocks for private database subnets"
  type        = list(string)
  default     = ["*********/24", "*********/24"]
}

variable "create_nat_gateway" {
  description = "Create NAT Gateways in public subnets"
  type        = bool
  default     = true
}

variable "create_vpc_endpoints" {
  description = "Create VPC endpoints for AWS services"
  type        = bool
  default     = true
}

# RDS PostgreSQL Variables
variable "postgres_family_version" {
  description = "PostgreSQL parameter group family version"
  type        = string
  default     = "17"
}

variable "postgres_engine_version" {
  description = "PostgreSQL engine version"
  type        = string
  default     = "17.4"
}

variable "rds_instance_class" {
  description = "RDS instance class"
  type        = string
  default     = "db.t4g.large"
}

variable "rds_allocated_storage" {
  description = "Allocated storage in GB"
  type        = number
  default     = 50
}

variable "rds_max_allocated_storage" {
  description = "Maximum allocated storage in GB for autoscaling"
  type        = number
  default     = 100
}

variable "rds_storage_type" {
  description = "Storage type (gp2, gp3, io1)"
  type        = string
  default     = "gp3"
}

variable "rds_backup_retention_period" {
  description = "Backup retention period in days"
  type        = number
  default     = 7
}

variable "rds_backup_window" {
  description = "Preferred backup window"
  type        = string
  default     = "03:00-04:00"
}

variable "rds_maintenance_window" {
  description = "Preferred maintenance window"
  type        = string
  default     = "sun:04:30-sun:05:30"
}

variable "rds_multi_az" {
  description = "Whether to deploy a multi-AZ RDS instance"
  type        = bool
  default     = false
}

variable "rds_deletion_protection" {
  description = "Whether to enable deletion protection"
  type        = bool
  default     = true
}

variable "rds_skip_final_snapshot" {
  description = "Whether to skip the final snapshot when deleting the instance"
  type        = bool
  default     = false
}

variable "rds_apply_immediately" {
  description = "Whether to apply changes immediately or during the maintenance window"
  type        = bool
  default     = true
}

variable "rds_enable_enhanced_monitoring" {
  description = "Whether to enable enhanced monitoring"
  type        = bool
  default     = false
}

variable "rds_enable_performance_insights" {
  description = "Whether to enable Performance Insights"
  type        = bool
  default     = true
}

variable "rds_performance_insights_retention_period" {
  description = "Performance Insights retention period in days"
  type        = number
  default     = 7
}

variable "rds_create_read_replica" {
  description = "Whether to create a read replica"
  type        = bool
  default     = false
}

variable "rds_replica_instance_class" {
  description = "Instance class for the read replica"
  type        = string
  default     = "db.t4g.large"
}

# Bastion Host Variables
variable "bastion_instance_type" {
  description = "EC2 instance type for the bastion host"
  type        = string
  default     = "t3a.micro"
}

variable "bastion_key_name" {
  description = "Name of the SSH key pair to use for the bastion host"
  type        = string
}

variable "bastion_allowed_ssh_cidr_blocks" {
  description = "List of CIDR blocks allowed to SSH to the bastion host"
  type        = list(string)
  default     = []
}

variable "bastion_root_volume_size" {
  description = "Size of the root volume in GB for the bastion host"
  type        = number
  default     = 20
}

# THECHATBOT Variables
variable "openai_api_key" {
  description = "OpenAI API key for THECHATBOT"
  type        = string
  sensitive   = true
  default     = ""
}

variable "thechatbot_log_retention_days" {
  description = "Number of days to retain logs for THECHATBOT"
  type        = number
  default     = 30
}

# Bedrock Variables
variable "bedrock_model_ids" {
  description = "List of Bedrock model IDs to allow access to"
  type        = list(string)
  default     = ["anthropic.claude-3-sonnet-20240229-v1:0"]
}

variable "route53_hosted_zone_id" {
  description = "The ID of the Route53 hosted zone for wasteos.net"
  type        = string
  default     = "Z0958744982TPSOX8RJM"  # Setting default for backward compatibility
}
