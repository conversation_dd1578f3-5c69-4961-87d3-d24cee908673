# Dev Environment - outputs.tf
# Outputs from the dev environment

# VPC Outputs
output "vpc_id" {
  description = "The ID of the VPC"
  value       = module.vpc.vpc_id
}

output "vpc_cidr_block" {
  description = "The CIDR block of the VPC"
  value       = module.vpc.vpc_cidr_block
}

output "vpc_arn" {
  description = "The ARN of the VPC"
  value       = module.vpc.vpc_arn
}

output "internet_gateway_id" {
  description = "The ID of the Internet Gateway"
  value       = module.vpc.internet_gateway_id
}

output "public_subnet_ids" {
  description = "List of IDs of public subnets"
  value       = module.vpc.public_subnet_ids
}

output "private_app_subnet_ids" {
  description = "List of IDs of private application subnets"
  value       = module.vpc.private_app_subnet_ids
}

output "private_db_subnet_ids" {
  description = "List of IDs of private database subnets"
  value       = module.vpc.private_db_subnet_ids
}

output "nat_gateway_ids" {
  description = "List of NAT Gateway IDs"
  value       = module.vpc.nat_gateway_ids
}

output "nat_gateway_public_ips" {
  description = "List of public Elastic IPs created for NAT Gateways"
  value       = module.vpc.nat_gateway_public_ips
}

output "public_route_table_ids" {
  description = "List of IDs of public route tables"
  value       = module.vpc.public_route_table_ids
}

output "private_app_route_table_ids" {
  description = "List of IDs of private application route tables"
  value       = module.vpc.private_app_route_table_ids
}

output "private_db_route_table_ids" {
  description = "List of IDs of private database route tables"
  value       = module.vpc.private_db_route_table_ids
}

output "vpc_alb_security_group_id" {
  description = "ID of the ALB security group from VPC module"
  value       = module.vpc.alb_security_group_id
}

output "alb_security_group_id" {
  description = "ID of the ALB security group"
  value       = module.vpc.alb_security_group_id
}

output "eks_cluster_security_group_id" {
  description = "ID of the EKS cluster security group"
  value       = module.vpc.eks_cluster_security_group_id
}

output "rds_security_group_id" {
  description = "ID of the RDS security group"
  value       = module.vpc.rds_security_group_id
}

output "redis_security_group_id" {
  description = "ID of the Redis security group"
  value       = module.vpc.redis_security_group_id
}

output "vpc_endpoint_s3_id" {
  description = "ID of the S3 VPC endpoint"
  value       = module.vpc.vpc_endpoint_s3_id
}

output "vpc_endpoint_dynamodb_id" {
  description = "ID of the DynamoDB VPC endpoint"
  value       = module.vpc.vpc_endpoint_dynamodb_id
}

# ECS Outputs
output "ecs_cluster_id" {
  description = "The ID of the ECS cluster"
  value       = module.ecs.cluster_id
}

output "ecs_cluster_arn" {
  description = "The ARN of the ECS cluster"
  value       = module.ecs.cluster_arn
}

output "ecs_cluster_name" {
  description = "The name of the ECS cluster"
  value       = module.ecs.cluster_name
}

output "ecs_security_group_id" {
  description = "The ID of the ECS security group"
  value       = module.ecs.ecs_security_group_id
}

output "ecs_task_execution_role_arn" {
  description = "The ARN of the ECS task execution role"
  value       = module.ecs.ecs_task_execution_role_arn
}

output "ecs_task_execution_role_name" {
  description = "The name of the ECS task execution role"
  value       = module.ecs.ecs_task_execution_role_name
}

output "ecs_task_role_access_policy_arn" {
  description = "The ARN of the ECS task role access policy"
  value       = module.ecs.ecs_task_role_access_policy_arn
}

# ECS Sample App Outputs have been removed as they're no longer needed

# ECR Repository Outputs
# Commented out sample app ECR repository outputs
# output "sample_app_ecr_repository_url" {
#   description = "The URL of the sample app ECR repository"
#   value       = module.ecr.repository_url
# }
#
# output "sample_app_ecr_repository_name" {
#   description = "The name of the sample app ECR repository"
#   value       = module.ecr.repository_name
# }

output "sembwaste_dashboard_ecr_repository_url" {
  description = "The URL of the sembwaste-dashboard ECR repository"
  value       = module.sembwaste_dashboard_ecr.repository_url
}

output "sembwaste_dashboard_ecr_repository_name" {
  description = "The name of the sembwaste-dashboard ECR repository"
  value       = module.sembwaste_dashboard_ecr.repository_name
}

output "sembwaste_dashboard_ecr_repository_arn" {
  description = "The ARN of the sembwaste-dashboard ECR repository"
  value       = module.sembwaste_dashboard_ecr.repository_arn
}

# RDS PostgreSQL Outputs
output "rds_instance_id" {
  description = "The RDS instance ID"
  value       = module.rds.db_instance_id
}

output "rds_instance_address" {
  description = "The address of the RDS instance"
  value       = module.rds.db_instance_address
}

output "rds_instance_endpoint" {
  description = "The connection endpoint of the RDS instance"
  value       = module.rds.db_instance_endpoint
}

output "rds_instance_name" {
  description = "The database name"
  value       = module.rds.db_instance_name
}

output "rds_instance_port" {
  description = "The database port"
  value       = module.rds.db_instance_port
}

output "rds_credentials_secret_arn" {
  description = "The ARN of the Secrets Manager secret containing database credentials"
  value       = module.rds.db_credentials_secret_arn
}

output "rds_uri_secret_arn" {
  description = "The ARN of the Secrets Manager secret containing the database URI"
  value       = module.rds.db_uri_secret_arn
}

# ALB Outputs
output "alb_id" {
  description = "The ID of the ALB"
  value       = module.alb.alb_id
}

output "alb_arn" {
  description = "The ARN of the ALB"
  value       = module.alb.alb_arn
}

output "alb_dns_name" {
  description = "The DNS name of the ALB"
  value       = module.alb.alb_dns_name
}

output "alb_zone_id" {
  description = "The zone ID of the ALB"
  value       = module.alb.alb_zone_id
}

output "target_group_arn" {
  description = "The ARN of the target group"
  value       = module.alb.target_group_arn
}

# Sembwaste Dashboard ALB Outputs
output "sembwaste_dashboard_target_group_arn" {
  description = "The ARN of the sembwaste-dashboard target group"
  value       = module.alb_sembwaste_dashboard.target_group_arn
}

output "sembwaste_dashboard_target_group_name" {
  description = "The name of the sembwaste-dashboard target group"
  value       = module.alb_sembwaste_dashboard.target_group_name
}

# CloudWatch Logs Outputs
output "sembwaste_dashboard_log_group_name" {
  description = "The name of the CloudWatch log group for sembwaste-dashboard"
  value       = module.cloudwatch_logs.sembwaste_dashboard_log_group_name
}

output "sembwaste_dashboard_log_group_arn" {
  description = "The ARN of the CloudWatch log group for sembwaste-dashboard"
  value       = module.cloudwatch_logs.sembwaste_dashboard_log_group_arn
}

# CloudWatch Alarms Outputs
output "sembwaste_dashboard_alb_5xx_errors_alarm_arn" {
  description = "The ARN of the ALB 5XX errors alarm for sembwaste-dashboard"
  value       = module.cloudwatch_alarms.alb_5xx_errors_alarm_arn
}

output "sembwaste_dashboard_target_group_health_alarm_arn" {
  description = "The ARN of the target group health alarm for sembwaste-dashboard"
  value       = module.cloudwatch_alarms.target_group_health_alarm_arn
}

output "sembwaste_dashboard_high_cpu_alarm_arn" {
  description = "The ARN of the high CPU utilization alarm for sembwaste-dashboard"
  value       = module.cloudwatch_alarms.high_cpu_alarm_arn
}

output "sembwaste_dashboard_high_memory_alarm_arn" {
  description = "The ARN of the high memory utilization alarm for sembwaste-dashboard"
  value       = module.cloudwatch_alarms.high_memory_alarm_arn
}

# Route53 Outputs
output "app_fqdn" {
  description = "The FQDN of the application"
  value       = module.route53.app_fqdn
}

output "api_fqdn" {
  description = "The FQDN of the API"
  value       = module.route53.api_fqdn
}

# Bastion Host Outputs
output "bastion_instance_id" {
  description = "ID of the bastion host EC2 instance"
  value       = module.bastion.bastion_instance_id
}

output "bastion_public_ip" {
  description = "Public IP address of the bastion host"
  value       = module.bastion.bastion_public_ip
}

output "bastion_security_group_id" {
  description = "ID of the bastion host security group"
  value       = module.bastion.bastion_security_group_id
}

output "bastion_ssh_command" {
  description = "SSH command to connect to the bastion host"
  value       = module.bastion.bastion_ssh_command
}

output "bastion_tunnel_command" {
  description = "SSH tunnel command to connect to RDS through the bastion host"
  value       = module.bastion.bastion_tunnel_command
}

# THECHATBOT Outputs
output "thechatbot_ecr_repository_url" {
  description = "The URL of the THECHATBOT ECR repository"
  value       = module.thechatbot_ecr.repository_url
}

output "thechatbot_ecr_repository_name" {
  description = "The name of the THECHATBOT ECR repository"
  value       = module.thechatbot_ecr.repository_name
}

output "thechatbot_ecr_repository_arn" {
  description = "The ARN of the THECHATBOT ECR repository"
  value       = module.thechatbot_ecr.repository_arn
}

output "thechatbot_target_group_arn" {
  description = "The ARN of the THECHATBOT target group"
  value       = module.alb_thechatbot.target_group_arn
}

output "thechatbot_target_group_name" {
  description = "The name of the THECHATBOT target group"
  value       = module.alb_thechatbot.target_group_name
}

output "thechatbot_openai_secret_arn" {
  description = "The ARN of the OpenAI API key secret for THECHATBOT"
  value       = module.thechatbot_openai_secret.secret_arn
}

output "thechatbot_log_group_name" {
  description = "The name of the CloudWatch log group for THECHATBOT"
  value       = module.thechatbot_logs.log_group_name
}

output "thechatbot_log_group_arn" {
  description = "The ARN of the CloudWatch log group for THECHATBOT"
  value       = module.thechatbot_logs.log_group_arn
}
