# STG Environment - main.tf
# Main configuration for the stg environment

terraform {
  required_version = ">= 1.0.0"

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }

  backend "s3" {
    bucket         = "wos-terraform-state-bucket-028864969427"
    key            = "wos/stg/terraform.tfstate"
    region         = "ap-southeast-2"  # Updated for Sydney region
    dynamodb_table = "terraform-statefile-locks-wos"
    encrypt        = true
  }
}

provider "aws" {
  region = var.region
  default_tags {
    tags = var.tags
  }
}

# VPC Stack
module "vpc" {
  source = "../../stacks/networking/vpc"

  environment = var.environment
  region      = var.region
  vpc_cidr    = var.vpc_cidr

  azs         = var.azs
  az_suffixes = var.az_suffixes

  public_subnets      = var.public_subnets
  private_app_subnets = var.private_app_subnets
  private_db_subnets  = var.private_db_subnets

  create_nat_gateway   = var.create_nat_gateway
  create_vpc_endpoints = var.create_vpc_endpoints

  tags = var.tags
}

# ECR Repository Stack for Sembawaste Dashboard
module "sembwaste_dashboard_ecr" {
  source = "../../stacks/container/ecr"

  environment          = var.environment
  repository_name      = "sembwaste-dashboard"
  image_tag_mutability = "MUTABLE"
  scan_on_push         = true
  encryption_type      = "AES256"

  tags = var.tags
}

# ECR Repository for THECHATBOT
module "thechatbot_ecr" {
  source = "../../stacks/container/ecr"

  environment          = var.environment
  repository_name      = "wos-${var.environment}-thechatbot"
  image_tag_mutability = "MUTABLE"
  scan_on_push         = true
  encryption_type      = "AES256"

  lifecycle_policy = jsonencode({
    rules = [
      {
        rulePriority = 1
        description  = "Keep only the last 10 images"
        selection = {
          tagStatus   = "any"
          countType   = "imageCountMoreThan"
          countNumber = 10
        }
        action = {
          type = "expire"
        }
      }
    ]
  })

  tags = var.tags
}

# IAM Stack for ECS
module "iam_ecs" {
  source = "../../stacks/iam/ecs"

  environment = var.environment
  ecr_repository_arns = [
    module.sembwaste_dashboard_ecr.repository_arn,
    "${module.sembwaste_dashboard_ecr.repository_arn}/*",
    module.thechatbot_ecr.repository_arn,
    "${module.thechatbot_ecr.repository_arn}/*"
  ]
  secret_arns      = var.secret_arns
  scp_restrictions = true # Set to true because there's an explicit SCP deny for Secrets Manager access

  tags = var.tags
}

# ECS Stack
module "ecs" {
  source = "../../stacks/compute/ecs"

  environment             = var.environment
  vpc_id                  = module.vpc.vpc_id
  vpc_cidr                = var.vpc_cidr
  secret_arns             = var.secret_arns
  s3_bucket_arns          = var.s3_bucket_arns
  task_execution_role_arn = module.iam_ecs.ecs_task_execution_role_arn

  tags = var.tags
}

# RDS PostgreSQL Stack
module "rds" {
  source = "../../stacks/database/rds"

  environment           = var.environment
  private_db_subnet_ids = module.vpc.private_db_subnet_ids
  rds_security_group_id = module.vpc.rds_security_group_id

  # PostgreSQL configuration
  postgres_family_version = var.postgres_family_version
  engine_version          = var.postgres_engine_version
  instance_class          = var.rds_instance_class

  # Storage configuration
  allocated_storage     = var.rds_allocated_storage
  max_allocated_storage = var.rds_max_allocated_storage
  storage_type          = var.rds_storage_type

  # Backup and maintenance
  backup_retention_period = var.rds_backup_retention_period
  backup_window           = var.rds_backup_window
  maintenance_window      = var.rds_maintenance_window

  # High availability
  multi_az = var.rds_multi_az

  # Security
  deletion_protection = var.rds_deletion_protection
  skip_final_snapshot = var.rds_skip_final_snapshot

  # Monitoring
  enable_enhanced_monitoring            = var.rds_enable_enhanced_monitoring
  enable_performance_insights           = var.rds_enable_performance_insights
  performance_insights_retention_period = var.rds_performance_insights_retention_period

  # Read replica
  create_read_replica    = var.rds_create_read_replica
  replica_instance_class = var.rds_replica_instance_class

  # Operations
  apply_immediately = var.rds_apply_immediately

  tags = var.tags
}

# Security Group Rule to allow ECS to access RDS
resource "aws_security_group_rule" "rds_from_ecs" {
  type                     = "ingress"
  from_port                = 5432
  to_port                  = 5432
  protocol                 = "tcp"
  source_security_group_id = module.ecs.ecs_security_group_id
  security_group_id        = module.vpc.rds_security_group_id
  description              = "Allow PostgreSQL from ECS cluster"
}

# ALB Stack
module "alb" {
  source = "../../stacks/networking/alb"

  environment           = var.environment
  vpc_id                = module.vpc.vpc_id
  public_subnet_ids     = module.vpc.public_subnet_ids
  alb_security_group_id = module.vpc.alb_security_group_id

  # ACM certificate for *.stg.wasteos.net domain
  certificate_arn = "arn:aws:acm:ap-southeast-1:028864969427:certificate/fbc843e6-636b-4e9f-a5b0-0f1e3bf8bdb1"

  # Access logs bucket is not created yet
  access_logs_bucket = ""

  tags = var.tags
}

# ALB Stack for Sembwaste Dashboard
module "alb_sembwaste_dashboard" {
  source = "../../stacks/networking/alb-sembwaste-dashboard"

  environment       = var.environment
  vpc_id            = module.vpc.vpc_id
  http_listener_arn = module.alb.http_listener_arn

  tags = var.tags
}

# CloudWatch Logs Stack for Sembwaste Dashboard
module "cloudwatch_logs" {
  source = "../../stacks/monitoring/cloudwatch-logs"

  environment        = var.environment
  log_retention_days = 30

  tags = var.tags
}

# CloudWatch Alarms Stack for Sembwaste Dashboard
module "cloudwatch_alarms" {
  source = "../../stacks/monitoring/cloudwatch-alarms"

  environment              = var.environment
  target_group_arn_suffix  = element(split(":", module.alb_sembwaste_dashboard.target_group_arn), 5)
  load_balancer_arn_suffix = element(split(":", module.alb.alb_arn), 5)

  tags = var.tags
}

# Route53 Stack
module "route53" {
  source = "../../stacks/networking/route53"

  environment             = var.environment
  domain_name             = "wasteos.net"
  hosted_zone_id          = var.route53_hosted_zone_id
  alb_dns_name            = module.alb.alb_dns_name
  alb_zone_id             = module.alb.alb_zone_id
  create_api_record       = true
  create_dashboard_record = true
  create_chatbot_record   = true

  depends_on = [module.alb]
}

# THECHATBOT Infrastructure

# Bedrock IAM Stack for THECHATBOT
module "bedrock_iam" {
  source = "../../stacks/iam/bedrock"

  environment       = var.environment
  region            = var.region
  account_id        = var.aws_account_id
  bedrock_model_ids = var.bedrock_model_ids
  trusted_services  = ["ecs-tasks.amazonaws.com"]

  tags = var.tags
}

# ALB Target Group and Listener Rules for THECHATBOT
module "alb_thechatbot" {
  source = "../../stacks/networking/alb-thechatbot"

  environment       = var.environment
  vpc_id            = module.vpc.vpc_id
  http_listener_arn = module.alb.http_listener_arn

  tags = var.tags
}

# ALB Target Group and Listener Rules for API Services
module "alb_api" {
  source = "../../stacks/networking/alb-api"

  environment           = var.environment
  vpc_id                = module.vpc.vpc_id
  http_listener_arn     = module.alb.http_listener_arn
  https_listener_arn    = module.alb.https_listener_arn  # Add HTTPS listener ARN
  alb_security_group_id = module.vpc.alb_security_group_id
  alb_dns_name          = module.alb.alb_dns_name
  alb_zone_id           = module.alb.alb_zone_id
  route53_zone_id       = var.route53_hosted_zone_id
  domain_name           = "wasteos.net"  # Add domain name

  enable_http_to_https_redirect = true  # Explicitly enable HTTP to HTTPS redirect

  tags = var.tags
}

# Secrets Manager for THECHATBOT OpenAI API Key
module "thechatbot_openai_secret" {
  source = "../../modules/security/secrets_manager"

  name        = "wos/${var.environment}/thechatbot/openai"
  description = "OpenAI API key for THECHATBOT"

  create_secret_version = true
  secret_string = jsonencode({
    OPENAI_API_KEY = var.openai_api_key
  })

  tags = merge(
    var.tags,
    {
      Name        = "wos-${var.environment}-thechatbot-openai"
      Environment = var.environment
    }
  )
}

# CloudWatch Logs for THECHATBOT
module "thechatbot_logs" {
  source = "../../modules/monitoring/cloudwatch_logs"

  name              = "/ecs/wos-${var.environment}-thechatbot"
  retention_in_days = var.thechatbot_log_retention_days

  tags = merge(
    var.tags,
    {
      Name        = "/ecs/wos-${var.environment}-thechatbot"
      Environment = var.environment
    }
  )
}