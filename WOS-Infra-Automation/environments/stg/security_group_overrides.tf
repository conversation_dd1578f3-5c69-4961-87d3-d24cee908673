# Security group overrides for the stg environment

# Security Group Rule to allow ALB to access ECS for THECHATBOT
resource "aws_security_group_rule" "ecs_from_alb_thechatbot" {
  type                     = "ingress"
  from_port                = 8000
  to_port                  = 8000
  protocol                 = "tcp"
  source_security_group_id = module.vpc.alb_security_group_id
  security_group_id        = module.ecs.ecs_security_group_id
  description              = "Allow traffic from ALB to ECS on port 8000 for THECHATBOT"
}

# Security Group Rule to allow ALB to access ECS for API service
resource "aws_security_group_rule" "ecs_from_alb_api" {
  type                     = "ingress"
  from_port                = 8080
  to_port                  = 8080
  protocol                 = "tcp"
  source_security_group_id = module.vpc.alb_security_group_id
  security_group_id        = module.ecs.ecs_security_group_id
  description              = "Allow traffic from ALB to ECS on port 8080 for API service"
}

# Security Group Rule to allow ALB to access ECS for sembwaste-dashboard
resource "aws_security_group_rule" "ecs_from_alb_sembwaste_dashboard" {
  type                     = "ingress"
  from_port                = 3000
  to_port                  = 3000
  protocol                 = "tcp"
  source_security_group_id = module.vpc.alb_security_group_id
  security_group_id        = module.ecs.ecs_security_group_id
  description              = "Allow traffic from ALB to ECS on port 3000 for sembwaste-dashboard"
}
