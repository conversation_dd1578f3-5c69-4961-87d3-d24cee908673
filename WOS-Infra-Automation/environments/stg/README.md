# WasteOS STG Environment

This directory contains the Terraform configuration for the WasteOS staging environment.

## Overview

The staging environment is a replica of the development environment with the following key differences:

### Included Components
- **VPC Stack** - Complete networking setup with staging-specific CIDR (*********/20)
- **ECR Repositories** - For sembwaste-dashboard and THECHATBOT
- **IAM Stack for ECS** - Container service permissions
- **ECS Stack** - Container orchestration
- **RDS PostgreSQL Stack** - Database with staging-optimized settings
- **ALB Stack** - Load balancer and target groups
- **CloudWatch Logs and Alarms** - Monitoring and alerting
- **Route53 Stack** - DNS configuration
- **Bedrock IAM Stack** - For THECHATBOT AI capabilities
- **Secrets Manager** - For THECHATBOT OpenAI API key

### Excluded Components (vs Dev Environment)
- **Bastion Host** - No SSH access host for cost optimization
- **GitHub Runner EC2** - No dedicated CI/CD runner instance

### Staging-Specific Optimizations
- **Smaller RDS instance** - `db.t4g.medium` instead of `db.t4g.large`
- **Reduced storage** - 30GB allocated, 50GB max (vs 50GB/100GB in dev)
- **Shorter backup retention** - 3 days instead of 7 days
- **Single AZ RDS** - No multi-AZ for cost savings
- **Disabled monitoring** - No enhanced monitoring or performance insights
- **Shorter log retention** - 14 days for THECHATBOT logs instead of 30 days
- **No deletion protection** - Allows easier cleanup
- **Skip final snapshot** - Faster deletion when needed

## Prerequisites

- AWS CLI configured with appropriate credentials
- Terraform 1.0.0 or later
- S3 bucket and DynamoDB table for Terraform state

## Usage

1. Copy the example tfvars file and update it with your values:

```bash
cp terraform.tfvars.example terraform.tfvars
```

2. Edit `terraform.tfvars` with your specific values for staging.

3. Initialize Terraform:

```bash
terraform init
```

4. Plan the deployment:

```bash
terraform plan -out=plan.out
```

5. Apply the changes:

```bash
terraform apply plan.out
```

## Network Configuration

- **VPC CIDR**: *********/20
- **Public Subnets**: *********/24, *********/24
- **Private App Subnets**: *********/24, *********/24
- **Private DB Subnets**: *********/24, *********/24

## Security Considerations

Since this environment excludes the bastion host:
- Direct database access requires alternative methods (e.g., VPN, AWS Systems Manager Session Manager)
- ECS tasks can still access RDS through security group rules
- Consider setting up temporary access methods if database administration is needed

## Cost Optimization

This staging environment is optimized for cost with:
- Smaller instance sizes
- Reduced storage allocations
- Shorter retention periods
- Single AZ deployments where appropriate
- Disabled expensive monitoring features

## Notes

- The staging environment uses the same S3 bucket for Terraform state but a different key (`wos/stg/terraform.tfstate`)
- Make sure to update the AWS account ID in terraform.tfvars
- Ensure that the S3 bucket and DynamoDB table for Terraform state exist
- The environment is designed to be ephemeral and can be destroyed/recreated as needed
