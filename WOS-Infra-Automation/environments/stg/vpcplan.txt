
Terraform used the selected providers to generate the following execution
plan. Resource actions are indicated with the following symbols:
  + create

Terraform will perform the following actions:

  # module.vpc.module.vpc.aws_eip.nat[0] will be created
  + resource "aws_eip" "nat" {
      + allocation_id        = (known after apply)
      + arn                  = (known after apply)
      + association_id       = (known after apply)
      + carrier_ip           = (known after apply)
      + customer_owned_ip    = (known after apply)
      + domain               = "vpc"
      + id                   = (known after apply)
      + instance             = (known after apply)
      + ipam_pool_id         = (known after apply)
      + network_border_group = (known after apply)
      + network_interface    = (known after apply)
      + private_dns          = (known after apply)
      + private_ip           = (known after apply)
      + ptr_record           = (known after apply)
      + public_dns           = (known after apply)
      + public_ip            = (known after apply)
      + public_ipv4_pool     = (known after apply)
      + tags                 = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-nat-eip-1a"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + tags_all             = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-nat-eip-1a"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + vpc                  = (known after apply)
    }

  # module.vpc.module.vpc.aws_eip.nat[1] will be created
  + resource "aws_eip" "nat" {
      + allocation_id        = (known after apply)
      + arn                  = (known after apply)
      + association_id       = (known after apply)
      + carrier_ip           = (known after apply)
      + customer_owned_ip    = (known after apply)
      + domain               = "vpc"
      + id                   = (known after apply)
      + instance             = (known after apply)
      + ipam_pool_id         = (known after apply)
      + network_border_group = (known after apply)
      + network_interface    = (known after apply)
      + private_dns          = (known after apply)
      + private_ip           = (known after apply)
      + ptr_record           = (known after apply)
      + public_dns           = (known after apply)
      + public_ip            = (known after apply)
      + public_ipv4_pool     = (known after apply)
      + tags                 = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-nat-eip-1b"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + tags_all             = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-nat-eip-1b"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + vpc                  = (known after apply)
    }

  # module.vpc.module.vpc.aws_internet_gateway.this will be created
  + resource "aws_internet_gateway" "this" {
      + arn      = (known after apply)
      + id       = (known after apply)
      + owner_id = (known after apply)
      + tags     = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-igw"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + tags_all = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-igw"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + vpc_id   = (known after apply)
    }

  # module.vpc.module.vpc.aws_nat_gateway.this[0] will be created
  + resource "aws_nat_gateway" "this" {
      + allocation_id                      = (known after apply)
      + association_id                     = (known after apply)
      + connectivity_type                  = "public"
      + id                                 = (known after apply)
      + network_interface_id               = (known after apply)
      + private_ip                         = (known after apply)
      + public_ip                          = (known after apply)
      + secondary_private_ip_address_count = (known after apply)
      + secondary_private_ip_addresses     = (known after apply)
      + subnet_id                          = (known after apply)
      + tags                               = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-nat-gw-1a"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + tags_all                           = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-nat-gw-1a"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
    }

  # module.vpc.module.vpc.aws_nat_gateway.this[1] will be created
  + resource "aws_nat_gateway" "this" {
      + allocation_id                      = (known after apply)
      + association_id                     = (known after apply)
      + connectivity_type                  = "public"
      + id                                 = (known after apply)
      + network_interface_id               = (known after apply)
      + private_ip                         = (known after apply)
      + public_ip                          = (known after apply)
      + secondary_private_ip_address_count = (known after apply)
      + secondary_private_ip_addresses     = (known after apply)
      + subnet_id                          = (known after apply)
      + tags                               = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-nat-gw-1b"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + tags_all                           = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-nat-gw-1b"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
    }

  # module.vpc.module.vpc.aws_route.private_app_nat_gateway[0] will be created
  + resource "aws_route" "private_app_nat_gateway" {
      + destination_cidr_block = "0.0.0.0/0"
      + id                     = (known after apply)
      + instance_id            = (known after apply)
      + instance_owner_id      = (known after apply)
      + nat_gateway_id         = (known after apply)
      + network_interface_id   = (known after apply)
      + origin                 = (known after apply)
      + route_table_id         = (known after apply)
      + state                  = (known after apply)

      + timeouts {
          + create = "5m"
        }
    }

  # module.vpc.module.vpc.aws_route.private_app_nat_gateway[1] will be created
  + resource "aws_route" "private_app_nat_gateway" {
      + destination_cidr_block = "0.0.0.0/0"
      + id                     = (known after apply)
      + instance_id            = (known after apply)
      + instance_owner_id      = (known after apply)
      + nat_gateway_id         = (known after apply)
      + network_interface_id   = (known after apply)
      + origin                 = (known after apply)
      + route_table_id         = (known after apply)
      + state                  = (known after apply)

      + timeouts {
          + create = "5m"
        }
    }

  # module.vpc.module.vpc.aws_route.private_db_nat_gateway[0] will be created
  + resource "aws_route" "private_db_nat_gateway" {
      + destination_cidr_block = "0.0.0.0/0"
      + id                     = (known after apply)
      + instance_id            = (known after apply)
      + instance_owner_id      = (known after apply)
      + nat_gateway_id         = (known after apply)
      + network_interface_id   = (known after apply)
      + origin                 = (known after apply)
      + route_table_id         = (known after apply)
      + state                  = (known after apply)

      + timeouts {
          + create = "5m"
        }
    }

  # module.vpc.module.vpc.aws_route.private_db_nat_gateway[1] will be created
  + resource "aws_route" "private_db_nat_gateway" {
      + destination_cidr_block = "0.0.0.0/0"
      + id                     = (known after apply)
      + instance_id            = (known after apply)
      + instance_owner_id      = (known after apply)
      + nat_gateway_id         = (known after apply)
      + network_interface_id   = (known after apply)
      + origin                 = (known after apply)
      + route_table_id         = (known after apply)
      + state                  = (known after apply)

      + timeouts {
          + create = "5m"
        }
    }

  # module.vpc.module.vpc.aws_route.public_internet_gateway[0] will be created
  + resource "aws_route" "public_internet_gateway" {
      + destination_cidr_block = "0.0.0.0/0"
      + gateway_id             = (known after apply)
      + id                     = (known after apply)
      + instance_id            = (known after apply)
      + instance_owner_id      = (known after apply)
      + network_interface_id   = (known after apply)
      + origin                 = (known after apply)
      + route_table_id         = (known after apply)
      + state                  = (known after apply)

      + timeouts {
          + create = "5m"
        }
    }

  # module.vpc.module.vpc.aws_route.public_internet_gateway[1] will be created
  + resource "aws_route" "public_internet_gateway" {
      + destination_cidr_block = "0.0.0.0/0"
      + gateway_id             = (known after apply)
      + id                     = (known after apply)
      + instance_id            = (known after apply)
      + instance_owner_id      = (known after apply)
      + network_interface_id   = (known after apply)
      + origin                 = (known after apply)
      + route_table_id         = (known after apply)
      + state                  = (known after apply)

      + timeouts {
          + create = "5m"
        }
    }

  # module.vpc.module.vpc.aws_route_table.private_app[0] will be created
  + resource "aws_route_table" "private_app" {
      + arn              = (known after apply)
      + id               = (known after apply)
      + owner_id         = (known after apply)
      + propagating_vgws = (known after apply)
      + route            = (known after apply)
      + tags             = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-private-app-rt-1a"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + tags_all         = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-private-app-rt-1a"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + vpc_id           = (known after apply)
    }

  # module.vpc.module.vpc.aws_route_table.private_app[1] will be created
  + resource "aws_route_table" "private_app" {
      + arn              = (known after apply)
      + id               = (known after apply)
      + owner_id         = (known after apply)
      + propagating_vgws = (known after apply)
      + route            = (known after apply)
      + tags             = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-private-app-rt-1b"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + tags_all         = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-private-app-rt-1b"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + vpc_id           = (known after apply)
    }

  # module.vpc.module.vpc.aws_route_table.private_db[0] will be created
  + resource "aws_route_table" "private_db" {
      + arn              = (known after apply)
      + id               = (known after apply)
      + owner_id         = (known after apply)
      + propagating_vgws = (known after apply)
      + route            = (known after apply)
      + tags             = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-private-db-rt-1a"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + tags_all         = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-private-db-rt-1a"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + vpc_id           = (known after apply)
    }

  # module.vpc.module.vpc.aws_route_table.private_db[1] will be created
  + resource "aws_route_table" "private_db" {
      + arn              = (known after apply)
      + id               = (known after apply)
      + owner_id         = (known after apply)
      + propagating_vgws = (known after apply)
      + route            = (known after apply)
      + tags             = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-private-db-rt-1b"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + tags_all         = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-private-db-rt-1b"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + vpc_id           = (known after apply)
    }

  # module.vpc.module.vpc.aws_route_table.public[0] will be created
  + resource "aws_route_table" "public" {
      + arn              = (known after apply)
      + id               = (known after apply)
      + owner_id         = (known after apply)
      + propagating_vgws = (known after apply)
      + route            = (known after apply)
      + tags             = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-public-rt-1a"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + tags_all         = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-public-rt-1a"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + vpc_id           = (known after apply)
    }

  # module.vpc.module.vpc.aws_route_table.public[1] will be created
  + resource "aws_route_table" "public" {
      + arn              = (known after apply)
      + id               = (known after apply)
      + owner_id         = (known after apply)
      + propagating_vgws = (known after apply)
      + route            = (known after apply)
      + tags             = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-public-rt-1b"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + tags_all         = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-public-rt-1b"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + vpc_id           = (known after apply)
    }

  # module.vpc.module.vpc.aws_route_table_association.private_app[0] will be created
  + resource "aws_route_table_association" "private_app" {
      + id             = (known after apply)
      + route_table_id = (known after apply)
      + subnet_id      = (known after apply)
    }

  # module.vpc.module.vpc.aws_route_table_association.private_app[1] will be created
  + resource "aws_route_table_association" "private_app" {
      + id             = (known after apply)
      + route_table_id = (known after apply)
      + subnet_id      = (known after apply)
    }

  # module.vpc.module.vpc.aws_route_table_association.private_db[0] will be created
  + resource "aws_route_table_association" "private_db" {
      + id             = (known after apply)
      + route_table_id = (known after apply)
      + subnet_id      = (known after apply)
    }

  # module.vpc.module.vpc.aws_route_table_association.private_db[1] will be created
  + resource "aws_route_table_association" "private_db" {
      + id             = (known after apply)
      + route_table_id = (known after apply)
      + subnet_id      = (known after apply)
    }

  # module.vpc.module.vpc.aws_route_table_association.public[0] will be created
  + resource "aws_route_table_association" "public" {
      + id             = (known after apply)
      + route_table_id = (known after apply)
      + subnet_id      = (known after apply)
    }

  # module.vpc.module.vpc.aws_route_table_association.public[1] will be created
  + resource "aws_route_table_association" "public" {
      + id             = (known after apply)
      + route_table_id = (known after apply)
      + subnet_id      = (known after apply)
    }

  # module.vpc.module.vpc.aws_security_group.alb will be created
  + resource "aws_security_group" "alb" {
      + arn                    = (known after apply)
      + description            = "Security group for ALB"
      + egress                 = [
          + {
              + cidr_blocks      = [
                  + "0.0.0.0/0",
                ]
              + description      = "Allow all outbound traffic"
              + from_port        = 0
              + ipv6_cidr_blocks = []
              + prefix_list_ids  = []
              + protocol         = "-1"
              + security_groups  = []
              + self             = false
              + to_port          = 0
            },
        ]
      + id                     = (known after apply)
      + ingress                = [
          + {
              + cidr_blocks      = [
                  + "0.0.0.0/0",
                ]
              + description      = "Allow HTTP from anywhere"
              + from_port        = 80
              + ipv6_cidr_blocks = []
              + prefix_list_ids  = []
              + protocol         = "tcp"
              + security_groups  = []
              + self             = false
              + to_port          = 80
            },
          + {
              + cidr_blocks      = [
                  + "0.0.0.0/0",
                ]
              + description      = "Allow HTTPS from anywhere"
              + from_port        = 443
              + ipv6_cidr_blocks = []
              + prefix_list_ids  = []
              + protocol         = "tcp"
              + security_groups  = []
              + self             = false
              + to_port          = 443
            },
        ]
      + name                   = "wos-stg-alb-sg"
      + name_prefix            = (known after apply)
      + owner_id               = (known after apply)
      + revoke_rules_on_delete = false
      + tags                   = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-alb-sg"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + tags_all               = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-alb-sg"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + vpc_id                 = (known after apply)
    }

  # module.vpc.module.vpc.aws_security_group.eks_cluster will be created
  + resource "aws_security_group" "eks_cluster" {
      + arn                    = (known after apply)
      + description            = "Security group for EKS cluster"
      + egress                 = [
          + {
              + cidr_blocks      = [
                  + "0.0.0.0/0",
                ]
              + description      = "Allow all outbound traffic"
              + from_port        = 0
              + ipv6_cidr_blocks = []
              + prefix_list_ids  = []
              + protocol         = "-1"
              + security_groups  = []
              + self             = false
              + to_port          = 0
            },
        ]
      + id                     = (known after apply)
      + ingress                = [
          + {
              + cidr_blocks      = []
              + description      = "Allow HTTPS from ALB"
              + from_port        = 443
              + ipv6_cidr_blocks = []
              + prefix_list_ids  = []
              + protocol         = "tcp"
              + security_groups  = (known after apply)
              + self             = false
              + to_port          = 443
            },
          + {
              + cidr_blocks      = []
              + description      = "Allow all traffic from self"
              + from_port        = 0
              + ipv6_cidr_blocks = []
              + prefix_list_ids  = []
              + protocol         = "-1"
              + security_groups  = []
              + self             = true
              + to_port          = 0
            },
        ]
      + name                   = "wos-stg-eks-cluster-sg"
      + name_prefix            = (known after apply)
      + owner_id               = (known after apply)
      + revoke_rules_on_delete = false
      + tags                   = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-eks-cluster-sg"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + tags_all               = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-eks-cluster-sg"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + vpc_id                 = (known after apply)
    }

  # module.vpc.module.vpc.aws_security_group.rds will be created
  + resource "aws_security_group" "rds" {
      + arn                    = (known after apply)
      + description            = "Security group for RDS PostgreSQL"
      + egress                 = [
          + {
              + cidr_blocks      = [
                  + "0.0.0.0/0",
                ]
              + description      = "Allow all outbound traffic"
              + from_port        = 0
              + ipv6_cidr_blocks = []
              + prefix_list_ids  = []
              + protocol         = "-1"
              + security_groups  = []
              + self             = false
              + to_port          = 0
            },
        ]
      + id                     = (known after apply)
      + ingress                = [
          + {
              + cidr_blocks      = []
              + description      = "Allow PostgreSQL from EKS cluster"
              + from_port        = 5432
              + ipv6_cidr_blocks = []
              + prefix_list_ids  = []
              + protocol         = "tcp"
              + security_groups  = (known after apply)
              + self             = false
              + to_port          = 5432
            },
        ]
      + name                   = "wos-stg-rds-sg"
      + name_prefix            = (known after apply)
      + owner_id               = (known after apply)
      + revoke_rules_on_delete = false
      + tags                   = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-rds-sg"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + tags_all               = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-rds-sg"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + vpc_id                 = (known after apply)
    }

  # module.vpc.module.vpc.aws_security_group.redis will be created
  + resource "aws_security_group" "redis" {
      + arn                    = (known after apply)
      + description            = "Security group for Redis"
      + egress                 = [
          + {
              + cidr_blocks      = [
                  + "0.0.0.0/0",
                ]
              + description      = "Allow all outbound traffic"
              + from_port        = 0
              + ipv6_cidr_blocks = []
              + prefix_list_ids  = []
              + protocol         = "-1"
              + security_groups  = []
              + self             = false
              + to_port          = 0
            },
        ]
      + id                     = (known after apply)
      + ingress                = [
          + {
              + cidr_blocks      = []
              + description      = "Allow Redis from EKS cluster"
              + from_port        = 6379
              + ipv6_cidr_blocks = []
              + prefix_list_ids  = []
              + protocol         = "tcp"
              + security_groups  = (known after apply)
              + self             = false
              + to_port          = 6379
            },
        ]
      + name                   = "wos-stg-redis-sg"
      + name_prefix            = (known after apply)
      + owner_id               = (known after apply)
      + revoke_rules_on_delete = false
      + tags                   = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-redis-sg"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + tags_all               = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-redis-sg"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + vpc_id                 = (known after apply)
    }

  # module.vpc.module.vpc.aws_security_group.vpc_endpoints[0] will be created
  + resource "aws_security_group" "vpc_endpoints" {
      + arn                    = (known after apply)
      + description            = "Security group for VPC endpoints"
      + egress                 = [
          + {
              + cidr_blocks      = [
                  + "0.0.0.0/0",
                ]
              + description      = "Allow all outbound traffic"
              + from_port        = 0
              + ipv6_cidr_blocks = []
              + prefix_list_ids  = []
              + protocol         = "-1"
              + security_groups  = []
              + self             = false
              + to_port          = 0
            },
        ]
      + id                     = (known after apply)
      + ingress                = [
          + {
              + cidr_blocks      = [
                  + "*********/20",
                ]
              + description      = "Allow HTTP from VPC CIDR"
              + from_port        = 80
              + ipv6_cidr_blocks = []
              + prefix_list_ids  = []
              + protocol         = "tcp"
              + security_groups  = []
              + self             = false
              + to_port          = 80
            },
          + {
              + cidr_blocks      = [
                  + "*********/20",
                ]
              + description      = "Allow HTTPS from VPC CIDR"
              + from_port        = 443
              + ipv6_cidr_blocks = []
              + prefix_list_ids  = []
              + protocol         = "tcp"
              + security_groups  = []
              + self             = false
              + to_port          = 443
            },
          + {
              + cidr_blocks      = [
                  + "*********/20",
                ]
              + description      = "Allow PostgreSQL from VPC CIDR"
              + from_port        = 5432
              + ipv6_cidr_blocks = []
              + prefix_list_ids  = []
              + protocol         = "tcp"
              + security_groups  = []
              + self             = false
              + to_port          = 5432
            },
          + {
              + cidr_blocks      = [
                  + "*********/20",
                ]
              + description      = "Allow Redis from VPC CIDR"
              + from_port        = 6379
              + ipv6_cidr_blocks = []
              + prefix_list_ids  = []
              + protocol         = "tcp"
              + security_groups  = []
              + self             = false
              + to_port          = 6379
            },
        ]
      + name                   = "wos-stg-vpc-endpoints-sg"
      + name_prefix            = (known after apply)
      + owner_id               = (known after apply)
      + revoke_rules_on_delete = false
      + tags                   = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-vpc-endpoints-sg"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + tags_all               = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-vpc-endpoints-sg"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + vpc_id                 = (known after apply)
    }

  # module.vpc.module.vpc.aws_subnet.private_app[0] will be created
  + resource "aws_subnet" "private_app" {
      + arn                                            = (known after apply)
      + assign_ipv6_address_on_creation                = false
      + availability_zone                              = "ap-southeast-1a"
      + availability_zone_id                           = (known after apply)
      + cidr_block                                     = "*********/24"
      + enable_dns64                                   = false
      + enable_resource_name_dns_a_record_on_launch    = false
      + enable_resource_name_dns_aaaa_record_on_launch = false
      + id                                             = (known after apply)
      + ipv6_cidr_block_association_id                 = (known after apply)
      + ipv6_native                                    = false
      + map_public_ip_on_launch                        = false
      + owner_id                                       = (known after apply)
      + private_dns_hostname_type_on_launch            = (known after apply)
      + tags                                           = {
          + "CostCenter"                      = "Engineering"
          + "Environment"                     = "stg"
          + "ManagedBy"                       = "Terraform"
          + "Name"                            = "wos-stg-private-app-subnet-1a"
          + "Owner"                           = "Platform Team"
          + "Project"                         = "WasteOS"
          + "kubernetes.io/role/internal-elb" = "1"
        }
      + tags_all                                       = {
          + "CostCenter"                      = "Engineering"
          + "Environment"                     = "stg"
          + "ManagedBy"                       = "Terraform"
          + "Name"                            = "wos-stg-private-app-subnet-1a"
          + "Owner"                           = "Platform Team"
          + "Project"                         = "WasteOS"
          + "kubernetes.io/role/internal-elb" = "1"
        }
      + vpc_id                                         = (known after apply)
    }

  # module.vpc.module.vpc.aws_subnet.private_app[1] will be created
  + resource "aws_subnet" "private_app" {
      + arn                                            = (known after apply)
      + assign_ipv6_address_on_creation                = false
      + availability_zone                              = "ap-southeast-1b"
      + availability_zone_id                           = (known after apply)
      + cidr_block                                     = "*********/24"
      + enable_dns64                                   = false
      + enable_resource_name_dns_a_record_on_launch    = false
      + enable_resource_name_dns_aaaa_record_on_launch = false
      + id                                             = (known after apply)
      + ipv6_cidr_block_association_id                 = (known after apply)
      + ipv6_native                                    = false
      + map_public_ip_on_launch                        = false
      + owner_id                                       = (known after apply)
      + private_dns_hostname_type_on_launch            = (known after apply)
      + tags                                           = {
          + "CostCenter"                      = "Engineering"
          + "Environment"                     = "stg"
          + "ManagedBy"                       = "Terraform"
          + "Name"                            = "wos-stg-private-app-subnet-1b"
          + "Owner"                           = "Platform Team"
          + "Project"                         = "WasteOS"
          + "kubernetes.io/role/internal-elb" = "1"
        }
      + tags_all                                       = {
          + "CostCenter"                      = "Engineering"
          + "Environment"                     = "stg"
          + "ManagedBy"                       = "Terraform"
          + "Name"                            = "wos-stg-private-app-subnet-1b"
          + "Owner"                           = "Platform Team"
          + "Project"                         = "WasteOS"
          + "kubernetes.io/role/internal-elb" = "1"
        }
      + vpc_id                                         = (known after apply)
    }

  # module.vpc.module.vpc.aws_subnet.private_db[0] will be created
  + resource "aws_subnet" "private_db" {
      + arn                                            = (known after apply)
      + assign_ipv6_address_on_creation                = false
      + availability_zone                              = "ap-southeast-1a"
      + availability_zone_id                           = (known after apply)
      + cidr_block                                     = "*********/24"
      + enable_dns64                                   = false
      + enable_resource_name_dns_a_record_on_launch    = false
      + enable_resource_name_dns_aaaa_record_on_launch = false
      + id                                             = (known after apply)
      + ipv6_cidr_block_association_id                 = (known after apply)
      + ipv6_native                                    = false
      + map_public_ip_on_launch                        = false
      + owner_id                                       = (known after apply)
      + private_dns_hostname_type_on_launch            = (known after apply)
      + tags                                           = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-private-db-subnet-1a"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + tags_all                                       = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-private-db-subnet-1a"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + vpc_id                                         = (known after apply)
    }

  # module.vpc.module.vpc.aws_subnet.private_db[1] will be created
  + resource "aws_subnet" "private_db" {
      + arn                                            = (known after apply)
      + assign_ipv6_address_on_creation                = false
      + availability_zone                              = "ap-southeast-1b"
      + availability_zone_id                           = (known after apply)
      + cidr_block                                     = "*********/24"
      + enable_dns64                                   = false
      + enable_resource_name_dns_a_record_on_launch    = false
      + enable_resource_name_dns_aaaa_record_on_launch = false
      + id                                             = (known after apply)
      + ipv6_cidr_block_association_id                 = (known after apply)
      + ipv6_native                                    = false
      + map_public_ip_on_launch                        = false
      + owner_id                                       = (known after apply)
      + private_dns_hostname_type_on_launch            = (known after apply)
      + tags                                           = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-private-db-subnet-1b"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + tags_all                                       = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-private-db-subnet-1b"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + vpc_id                                         = (known after apply)
    }

  # module.vpc.module.vpc.aws_subnet.public[0] will be created
  + resource "aws_subnet" "public" {
      + arn                                            = (known after apply)
      + assign_ipv6_address_on_creation                = false
      + availability_zone                              = "ap-southeast-1a"
      + availability_zone_id                           = (known after apply)
      + cidr_block                                     = "*********/24"
      + enable_dns64                                   = false
      + enable_resource_name_dns_a_record_on_launch    = false
      + enable_resource_name_dns_aaaa_record_on_launch = false
      + id                                             = (known after apply)
      + ipv6_cidr_block_association_id                 = (known after apply)
      + ipv6_native                                    = false
      + map_public_ip_on_launch                        = true
      + owner_id                                       = (known after apply)
      + private_dns_hostname_type_on_launch            = (known after apply)
      + tags                                           = {
          + "CostCenter"             = "Engineering"
          + "Environment"            = "stg"
          + "ManagedBy"              = "Terraform"
          + "Name"                   = "wos-stg-public-subnet-1a"
          + "Owner"                  = "Platform Team"
          + "Project"                = "WasteOS"
          + "kubernetes.io/role/elb" = "1"
        }
      + tags_all                                       = {
          + "CostCenter"             = "Engineering"
          + "Environment"            = "stg"
          + "ManagedBy"              = "Terraform"
          + "Name"                   = "wos-stg-public-subnet-1a"
          + "Owner"                  = "Platform Team"
          + "Project"                = "WasteOS"
          + "kubernetes.io/role/elb" = "1"
        }
      + vpc_id                                         = (known after apply)
    }

  # module.vpc.module.vpc.aws_subnet.public[1] will be created
  + resource "aws_subnet" "public" {
      + arn                                            = (known after apply)
      + assign_ipv6_address_on_creation                = false
      + availability_zone                              = "ap-southeast-1b"
      + availability_zone_id                           = (known after apply)
      + cidr_block                                     = "*********/24"
      + enable_dns64                                   = false
      + enable_resource_name_dns_a_record_on_launch    = false
      + enable_resource_name_dns_aaaa_record_on_launch = false
      + id                                             = (known after apply)
      + ipv6_cidr_block_association_id                 = (known after apply)
      + ipv6_native                                    = false
      + map_public_ip_on_launch                        = true
      + owner_id                                       = (known after apply)
      + private_dns_hostname_type_on_launch            = (known after apply)
      + tags                                           = {
          + "CostCenter"             = "Engineering"
          + "Environment"            = "stg"
          + "ManagedBy"              = "Terraform"
          + "Name"                   = "wos-stg-public-subnet-1b"
          + "Owner"                  = "Platform Team"
          + "Project"                = "WasteOS"
          + "kubernetes.io/role/elb" = "1"
        }
      + tags_all                                       = {
          + "CostCenter"             = "Engineering"
          + "Environment"            = "stg"
          + "ManagedBy"              = "Terraform"
          + "Name"                   = "wos-stg-public-subnet-1b"
          + "Owner"                  = "Platform Team"
          + "Project"                = "WasteOS"
          + "kubernetes.io/role/elb" = "1"
        }
      + vpc_id                                         = (known after apply)
    }

  # module.vpc.module.vpc.aws_vpc.this will be created
  + resource "aws_vpc" "this" {
      + arn                                  = (known after apply)
      + cidr_block                           = "*********/20"
      + default_network_acl_id               = (known after apply)
      + default_route_table_id               = (known after apply)
      + default_security_group_id            = (known after apply)
      + dhcp_options_id                      = (known after apply)
      + enable_dns_hostnames                 = true
      + enable_dns_support                   = true
      + enable_network_address_usage_metrics = (known after apply)
      + id                                   = (known after apply)
      + instance_tenancy                     = "default"
      + ipv6_association_id                  = (known after apply)
      + ipv6_cidr_block                      = (known after apply)
      + ipv6_cidr_block_network_border_group = (known after apply)
      + main_route_table_id                  = (known after apply)
      + owner_id                             = (known after apply)
      + tags                                 = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-vpc"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + tags_all                             = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-vpc"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
    }

  # module.vpc.module.vpc.aws_vpc_endpoint.dynamodb[0] will be created
  + resource "aws_vpc_endpoint" "dynamodb" {
      + arn                   = (known after apply)
      + cidr_blocks           = (known after apply)
      + dns_entry             = (known after apply)
      + id                    = (known after apply)
      + ip_address_type       = (known after apply)
      + network_interface_ids = (known after apply)
      + owner_id              = (known after apply)
      + policy                = (known after apply)
      + prefix_list_id        = (known after apply)
      + private_dns_enabled   = (known after apply)
      + requester_managed     = (known after apply)
      + route_table_ids       = (known after apply)
      + security_group_ids    = (known after apply)
      + service_name          = "com.amazonaws.ap-southeast-1.dynamodb"
      + service_region        = (known after apply)
      + state                 = (known after apply)
      + subnet_ids            = (known after apply)
      + tags                  = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-dynamodb-endpoint"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + tags_all              = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-dynamodb-endpoint"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + vpc_endpoint_type     = "Gateway"
      + vpc_id                = (known after apply)

      + dns_options (known after apply)

      + subnet_configuration (known after apply)
    }

  # module.vpc.module.vpc.aws_vpc_endpoint.interface_endpoints["bedrock-runtime"] will be created
  + resource "aws_vpc_endpoint" "interface_endpoints" {
      + arn                   = (known after apply)
      + cidr_blocks           = (known after apply)
      + dns_entry             = (known after apply)
      + id                    = (known after apply)
      + ip_address_type       = (known after apply)
      + network_interface_ids = (known after apply)
      + owner_id              = (known after apply)
      + policy                = (known after apply)
      + prefix_list_id        = (known after apply)
      + private_dns_enabled   = true
      + requester_managed     = (known after apply)
      + route_table_ids       = (known after apply)
      + security_group_ids    = (known after apply)
      + service_name          = "com.amazonaws.ap-southeast-1.bedrock-runtime"
      + service_region        = (known after apply)
      + state                 = (known after apply)
      + subnet_ids            = (known after apply)
      + tags                  = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-bedrock-runtime-endpoint"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + tags_all              = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-bedrock-runtime-endpoint"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + vpc_endpoint_type     = "Interface"
      + vpc_id                = (known after apply)

      + dns_options (known after apply)

      + subnet_configuration (known after apply)
    }

  # module.vpc.module.vpc.aws_vpc_endpoint.interface_endpoints["ec2"] will be created
  + resource "aws_vpc_endpoint" "interface_endpoints" {
      + arn                   = (known after apply)
      + cidr_blocks           = (known after apply)
      + dns_entry             = (known after apply)
      + id                    = (known after apply)
      + ip_address_type       = (known after apply)
      + network_interface_ids = (known after apply)
      + owner_id              = (known after apply)
      + policy                = (known after apply)
      + prefix_list_id        = (known after apply)
      + private_dns_enabled   = true
      + requester_managed     = (known after apply)
      + route_table_ids       = (known after apply)
      + security_group_ids    = (known after apply)
      + service_name          = "com.amazonaws.ap-southeast-1.ec2"
      + service_region        = (known after apply)
      + state                 = (known after apply)
      + subnet_ids            = (known after apply)
      + tags                  = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-ec2-endpoint"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + tags_all              = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-ec2-endpoint"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + vpc_endpoint_type     = "Interface"
      + vpc_id                = (known after apply)

      + dns_options (known after apply)

      + subnet_configuration (known after apply)
    }

  # module.vpc.module.vpc.aws_vpc_endpoint.interface_endpoints["ecr.api"] will be created
  + resource "aws_vpc_endpoint" "interface_endpoints" {
      + arn                   = (known after apply)
      + cidr_blocks           = (known after apply)
      + dns_entry             = (known after apply)
      + id                    = (known after apply)
      + ip_address_type       = (known after apply)
      + network_interface_ids = (known after apply)
      + owner_id              = (known after apply)
      + policy                = (known after apply)
      + prefix_list_id        = (known after apply)
      + private_dns_enabled   = true
      + requester_managed     = (known after apply)
      + route_table_ids       = (known after apply)
      + security_group_ids    = (known after apply)
      + service_name          = "com.amazonaws.ap-southeast-1.ecr.api"
      + service_region        = (known after apply)
      + state                 = (known after apply)
      + subnet_ids            = (known after apply)
      + tags                  = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-ecr.api-endpoint"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + tags_all              = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-ecr.api-endpoint"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + vpc_endpoint_type     = "Interface"
      + vpc_id                = (known after apply)

      + dns_options (known after apply)

      + subnet_configuration (known after apply)
    }

  # module.vpc.module.vpc.aws_vpc_endpoint.interface_endpoints["ecr.dkr"] will be created
  + resource "aws_vpc_endpoint" "interface_endpoints" {
      + arn                   = (known after apply)
      + cidr_blocks           = (known after apply)
      + dns_entry             = (known after apply)
      + id                    = (known after apply)
      + ip_address_type       = (known after apply)
      + network_interface_ids = (known after apply)
      + owner_id              = (known after apply)
      + policy                = (known after apply)
      + prefix_list_id        = (known after apply)
      + private_dns_enabled   = true
      + requester_managed     = (known after apply)
      + route_table_ids       = (known after apply)
      + security_group_ids    = (known after apply)
      + service_name          = "com.amazonaws.ap-southeast-1.ecr.dkr"
      + service_region        = (known after apply)
      + state                 = (known after apply)
      + subnet_ids            = (known after apply)
      + tags                  = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-ecr.dkr-endpoint"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + tags_all              = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-ecr.dkr-endpoint"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + vpc_endpoint_type     = "Interface"
      + vpc_id                = (known after apply)

      + dns_options (known after apply)

      + subnet_configuration (known after apply)
    }

  # module.vpc.module.vpc.aws_vpc_endpoint.interface_endpoints["ecs"] will be created
  + resource "aws_vpc_endpoint" "interface_endpoints" {
      + arn                   = (known after apply)
      + cidr_blocks           = (known after apply)
      + dns_entry             = (known after apply)
      + id                    = (known after apply)
      + ip_address_type       = (known after apply)
      + network_interface_ids = (known after apply)
      + owner_id              = (known after apply)
      + policy                = (known after apply)
      + prefix_list_id        = (known after apply)
      + private_dns_enabled   = true
      + requester_managed     = (known after apply)
      + route_table_ids       = (known after apply)
      + security_group_ids    = (known after apply)
      + service_name          = "com.amazonaws.ap-southeast-1.ecs"
      + service_region        = (known after apply)
      + state                 = (known after apply)
      + subnet_ids            = (known after apply)
      + tags                  = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-ecs-endpoint"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + tags_all              = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-ecs-endpoint"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + vpc_endpoint_type     = "Interface"
      + vpc_id                = (known after apply)

      + dns_options (known after apply)

      + subnet_configuration (known after apply)
    }

  # module.vpc.module.vpc.aws_vpc_endpoint.interface_endpoints["ecs-agent"] will be created
  + resource "aws_vpc_endpoint" "interface_endpoints" {
      + arn                   = (known after apply)
      + cidr_blocks           = (known after apply)
      + dns_entry             = (known after apply)
      + id                    = (known after apply)
      + ip_address_type       = (known after apply)
      + network_interface_ids = (known after apply)
      + owner_id              = (known after apply)
      + policy                = (known after apply)
      + prefix_list_id        = (known after apply)
      + private_dns_enabled   = true
      + requester_managed     = (known after apply)
      + route_table_ids       = (known after apply)
      + security_group_ids    = (known after apply)
      + service_name          = "com.amazonaws.ap-southeast-1.ecs-agent"
      + service_region        = (known after apply)
      + state                 = (known after apply)
      + subnet_ids            = (known after apply)
      + tags                  = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-ecs-agent-endpoint"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + tags_all              = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-ecs-agent-endpoint"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + vpc_endpoint_type     = "Interface"
      + vpc_id                = (known after apply)

      + dns_options (known after apply)

      + subnet_configuration (known after apply)
    }

  # module.vpc.module.vpc.aws_vpc_endpoint.interface_endpoints["ecs-telemetry"] will be created
  + resource "aws_vpc_endpoint" "interface_endpoints" {
      + arn                   = (known after apply)
      + cidr_blocks           = (known after apply)
      + dns_entry             = (known after apply)
      + id                    = (known after apply)
      + ip_address_type       = (known after apply)
      + network_interface_ids = (known after apply)
      + owner_id              = (known after apply)
      + policy                = (known after apply)
      + prefix_list_id        = (known after apply)
      + private_dns_enabled   = true
      + requester_managed     = (known after apply)
      + route_table_ids       = (known after apply)
      + security_group_ids    = (known after apply)
      + service_name          = "com.amazonaws.ap-southeast-1.ecs-telemetry"
      + service_region        = (known after apply)
      + state                 = (known after apply)
      + subnet_ids            = (known after apply)
      + tags                  = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-ecs-telemetry-endpoint"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + tags_all              = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-ecs-telemetry-endpoint"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + vpc_endpoint_type     = "Interface"
      + vpc_id                = (known after apply)

      + dns_options (known after apply)

      + subnet_configuration (known after apply)
    }

  # module.vpc.module.vpc.aws_vpc_endpoint.interface_endpoints["elasticloadbalancing"] will be created
  + resource "aws_vpc_endpoint" "interface_endpoints" {
      + arn                   = (known after apply)
      + cidr_blocks           = (known after apply)
      + dns_entry             = (known after apply)
      + id                    = (known after apply)
      + ip_address_type       = (known after apply)
      + network_interface_ids = (known after apply)
      + owner_id              = (known after apply)
      + policy                = (known after apply)
      + prefix_list_id        = (known after apply)
      + private_dns_enabled   = true
      + requester_managed     = (known after apply)
      + route_table_ids       = (known after apply)
      + security_group_ids    = (known after apply)
      + service_name          = "com.amazonaws.ap-southeast-1.elasticloadbalancing"
      + service_region        = (known after apply)
      + state                 = (known after apply)
      + subnet_ids            = (known after apply)
      + tags                  = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-elasticloadbalancing-endpoint"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + tags_all              = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-elasticloadbalancing-endpoint"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + vpc_endpoint_type     = "Interface"
      + vpc_id                = (known after apply)

      + dns_options (known after apply)

      + subnet_configuration (known after apply)
    }

  # module.vpc.module.vpc.aws_vpc_endpoint.interface_endpoints["logs"] will be created
  + resource "aws_vpc_endpoint" "interface_endpoints" {
      + arn                   = (known after apply)
      + cidr_blocks           = (known after apply)
      + dns_entry             = (known after apply)
      + id                    = (known after apply)
      + ip_address_type       = (known after apply)
      + network_interface_ids = (known after apply)
      + owner_id              = (known after apply)
      + policy                = (known after apply)
      + prefix_list_id        = (known after apply)
      + private_dns_enabled   = true
      + requester_managed     = (known after apply)
      + route_table_ids       = (known after apply)
      + security_group_ids    = (known after apply)
      + service_name          = "com.amazonaws.ap-southeast-1.logs"
      + service_region        = (known after apply)
      + state                 = (known after apply)
      + subnet_ids            = (known after apply)
      + tags                  = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-logs-endpoint"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + tags_all              = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-logs-endpoint"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + vpc_endpoint_type     = "Interface"
      + vpc_id                = (known after apply)

      + dns_options (known after apply)

      + subnet_configuration (known after apply)
    }

  # module.vpc.module.vpc.aws_vpc_endpoint.interface_endpoints["rds"] will be created
  + resource "aws_vpc_endpoint" "interface_endpoints" {
      + arn                   = (known after apply)
      + cidr_blocks           = (known after apply)
      + dns_entry             = (known after apply)
      + id                    = (known after apply)
      + ip_address_type       = (known after apply)
      + network_interface_ids = (known after apply)
      + owner_id              = (known after apply)
      + policy                = (known after apply)
      + prefix_list_id        = (known after apply)
      + private_dns_enabled   = true
      + requester_managed     = (known after apply)
      + route_table_ids       = (known after apply)
      + security_group_ids    = (known after apply)
      + service_name          = "com.amazonaws.ap-southeast-1.rds"
      + service_region        = (known after apply)
      + state                 = (known after apply)
      + subnet_ids            = (known after apply)
      + tags                  = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-rds-endpoint"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + tags_all              = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-rds-endpoint"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + vpc_endpoint_type     = "Interface"
      + vpc_id                = (known after apply)

      + dns_options (known after apply)

      + subnet_configuration (known after apply)
    }

  # module.vpc.module.vpc.aws_vpc_endpoint.interface_endpoints["secretsmanager"] will be created
  + resource "aws_vpc_endpoint" "interface_endpoints" {
      + arn                   = (known after apply)
      + cidr_blocks           = (known after apply)
      + dns_entry             = (known after apply)
      + id                    = (known after apply)
      + ip_address_type       = (known after apply)
      + network_interface_ids = (known after apply)
      + owner_id              = (known after apply)
      + policy                = (known after apply)
      + prefix_list_id        = (known after apply)
      + private_dns_enabled   = true
      + requester_managed     = (known after apply)
      + route_table_ids       = (known after apply)
      + security_group_ids    = (known after apply)
      + service_name          = "com.amazonaws.ap-southeast-1.secretsmanager"
      + service_region        = (known after apply)
      + state                 = (known after apply)
      + subnet_ids            = (known after apply)
      + tags                  = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-secretsmanager-endpoint"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + tags_all              = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-secretsmanager-endpoint"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + vpc_endpoint_type     = "Interface"
      + vpc_id                = (known after apply)

      + dns_options (known after apply)

      + subnet_configuration (known after apply)
    }

  # module.vpc.module.vpc.aws_vpc_endpoint.interface_endpoints["sns"] will be created
  + resource "aws_vpc_endpoint" "interface_endpoints" {
      + arn                   = (known after apply)
      + cidr_blocks           = (known after apply)
      + dns_entry             = (known after apply)
      + id                    = (known after apply)
      + ip_address_type       = (known after apply)
      + network_interface_ids = (known after apply)
      + owner_id              = (known after apply)
      + policy                = (known after apply)
      + prefix_list_id        = (known after apply)
      + private_dns_enabled   = true
      + requester_managed     = (known after apply)
      + route_table_ids       = (known after apply)
      + security_group_ids    = (known after apply)
      + service_name          = "com.amazonaws.ap-southeast-1.sns"
      + service_region        = (known after apply)
      + state                 = (known after apply)
      + subnet_ids            = (known after apply)
      + tags                  = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-sns-endpoint"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + tags_all              = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-sns-endpoint"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + vpc_endpoint_type     = "Interface"
      + vpc_id                = (known after apply)

      + dns_options (known after apply)

      + subnet_configuration (known after apply)
    }

  # module.vpc.module.vpc.aws_vpc_endpoint.interface_endpoints["sqs"] will be created
  + resource "aws_vpc_endpoint" "interface_endpoints" {
      + arn                   = (known after apply)
      + cidr_blocks           = (known after apply)
      + dns_entry             = (known after apply)
      + id                    = (known after apply)
      + ip_address_type       = (known after apply)
      + network_interface_ids = (known after apply)
      + owner_id              = (known after apply)
      + policy                = (known after apply)
      + prefix_list_id        = (known after apply)
      + private_dns_enabled   = true
      + requester_managed     = (known after apply)
      + route_table_ids       = (known after apply)
      + security_group_ids    = (known after apply)
      + service_name          = "com.amazonaws.ap-southeast-1.sqs"
      + service_region        = (known after apply)
      + state                 = (known after apply)
      + subnet_ids            = (known after apply)
      + tags                  = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-sqs-endpoint"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + tags_all              = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-sqs-endpoint"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + vpc_endpoint_type     = "Interface"
      + vpc_id                = (known after apply)

      + dns_options (known after apply)

      + subnet_configuration (known after apply)
    }

  # module.vpc.module.vpc.aws_vpc_endpoint.interface_endpoints["wafv2"] will be created
  + resource "aws_vpc_endpoint" "interface_endpoints" {
      + arn                   = (known after apply)
      + cidr_blocks           = (known after apply)
      + dns_entry             = (known after apply)
      + id                    = (known after apply)
      + ip_address_type       = (known after apply)
      + network_interface_ids = (known after apply)
      + owner_id              = (known after apply)
      + policy                = (known after apply)
      + prefix_list_id        = (known after apply)
      + private_dns_enabled   = true
      + requester_managed     = (known after apply)
      + route_table_ids       = (known after apply)
      + security_group_ids    = (known after apply)
      + service_name          = "com.amazonaws.ap-southeast-1.wafv2"
      + service_region        = (known after apply)
      + state                 = (known after apply)
      + subnet_ids            = (known after apply)
      + tags                  = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-wafv2-endpoint"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + tags_all              = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-wafv2-endpoint"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + vpc_endpoint_type     = "Interface"
      + vpc_id                = (known after apply)

      + dns_options (known after apply)

      + subnet_configuration (known after apply)
    }

  # module.vpc.module.vpc.aws_vpc_endpoint.s3[0] will be created
  + resource "aws_vpc_endpoint" "s3" {
      + arn                   = (known after apply)
      + cidr_blocks           = (known after apply)
      + dns_entry             = (known after apply)
      + id                    = (known after apply)
      + ip_address_type       = (known after apply)
      + network_interface_ids = (known after apply)
      + owner_id              = (known after apply)
      + policy                = (known after apply)
      + prefix_list_id        = (known after apply)
      + private_dns_enabled   = (known after apply)
      + requester_managed     = (known after apply)
      + route_table_ids       = (known after apply)
      + security_group_ids    = (known after apply)
      + service_name          = "com.amazonaws.ap-southeast-1.s3"
      + service_region        = (known after apply)
      + state                 = (known after apply)
      + subnet_ids            = (known after apply)
      + tags                  = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-s3-endpoint"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + tags_all              = {
          + "CostCenter"  = "Engineering"
          + "Environment" = "stg"
          + "ManagedBy"   = "Terraform"
          + "Name"        = "wos-stg-s3-endpoint"
          + "Owner"       = "Platform Team"
          + "Project"     = "WasteOS"
        }
      + vpc_endpoint_type     = "Gateway"
      + vpc_id                = (known after apply)

      + dns_options (known after apply)

      + subnet_configuration (known after apply)
    }

Plan: 51 to add, 0 to change, 0 to destroy.

Changes to Outputs:
  + alb_security_group_id         = (known after apply)
  + eks_cluster_security_group_id = (known after apply)
  + internet_gateway_id           = (known after apply)
  + nat_gateway_ids               = [
      + (known after apply),
      + (known after apply),
    ]
  + nat_gateway_public_ips        = [
      + (known after apply),
      + (known after apply),
    ]
  + private_app_route_table_ids   = [
      + (known after apply),
      + (known after apply),
    ]
  + private_app_subnet_ids        = [
      + (known after apply),
      + (known after apply),
    ]
  + private_db_route_table_ids    = [
      + (known after apply),
      + (known after apply),
    ]
  + private_db_subnet_ids         = [
      + (known after apply),
      + (known after apply),
    ]
  + public_route_table_ids        = [
      + (known after apply),
      + (known after apply),
    ]
  + public_subnet_ids             = [
      + (known after apply),
      + (known after apply),
    ]
  + rds_security_group_id         = (known after apply)
  + redis_security_group_id       = (known after apply)
  + vpc_alb_security_group_id     = (known after apply)
  + vpc_arn                       = (known after apply)
  + vpc_cidr_block                = "*********/20"
  + vpc_endpoint_dynamodb_id      = (known after apply)
  + vpc_endpoint_s3_id            = (known after apply)
  + vpc_id                        = (known after apply)
