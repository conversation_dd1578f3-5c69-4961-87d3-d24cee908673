# Local .terraform directories
**/.terraform/*

# .tfstate files
*.tfstate
*.tfstate.*

# Crash log files
crash.log
crash.*.log

# Exclude all .tfvars files, which are likely to contain sensitive data
*.tfvars
*.tfvars.json

# Ignore override files as they are usually used for local development
override.tf
override.tf.json
*_override.tf
*_override.tf.json

# Ignore CLI configuration files
.terraformrc
terraform.rc

# Ignore environment variables and credentials
.env
credentials.env
*.pem
*.key

# Ignore generated credentials from assume-role script
bootstrap/credentials.env

# Ignore lock files
.terraform.lock.hcl

# Ignore Mac OS specific files
.DS_Store

# Ignore editor specific files
.vscode/
.idea/
*.swp
*.swo

# Ignore log files
*.log

# Ignore backup files
*.bak
*~
*.backup

# Ignore temporary files
*.tmp
*.temp

# Ignore plan output files
*tfplan*
plan.out
plan.json

# Ignore local development files
.envrc
.direnv/

# Project Documentation and Non-Core Folders
0.References/
1.Requirements/
2.Jira/
3.Prompts/
4.App-Deployment/
Errors/
bootstrap/
.qodo/
